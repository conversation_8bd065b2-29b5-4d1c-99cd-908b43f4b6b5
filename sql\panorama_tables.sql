-- =====================================================
-- 全景图热点编辑系统数据库表结构
-- 创建时间: 2025-01-27
-- 修改时间: 2025-01-27
-- 描述: 支持多任务管理的全景图热点编辑系统
-- 数据库: Oracle 11g
-- 修改说明: 移除主键外键约束，调整字段结构
-- =====================================================

-- 1. 任务管理表
CREATE TABLE PANORAMA_TASK
(
    TASK_ID       NUMBER,
    TASK_NAME     VARCHAR2(255) NOT NULL,
    MODEL_ID      VARCHAR2(100),
    MODEL_NAME    VARCHAR2(255),
    DESCRIPTION   VARCHAR2(1000),
    ZIP_FILE_PATH VARCHAR2(500),
    EXTRACT_PATH  VARCHAR2(500),
    CREATE_USER   VARCHAR2(50) DEFAULT 'adm',
    CREATE_TIME   DATE DEFAULT SYSDATE,
    UPDATE_TIME   DATE DEFAULT SYSDATE,
    STATUS        NUMBER(1) DEFAULT 0
)
/

COMMENT ON TABLE PANORAMA_TASK IS '全景图任务管理表'
/
COMMENT ON COLUMN PANORAMA_TASK.TASK_ID IS '任务ID'
/
COMMENT ON COLUMN PANORAMA_TASK.TASK_NAME IS '任务名称'
/
COMMENT ON COLUMN PANORAMA_TASK.MODEL_ID IS '型号ID'
/
COMMENT ON COLUMN PANORAMA_TASK.MODEL_NAME IS '型号名称'
/
COMMENT ON COLUMN PANORAMA_TASK.DESCRIPTION IS '任务描述'
/
COMMENT ON COLUMN PANORAMA_TASK.ZIP_FILE_PATH IS 'ZIP文件存储路径'
/
COMMENT ON COLUMN PANORAMA_TASK.EXTRACT_PATH IS 'ZIP文件解压路径'
/
COMMENT ON COLUMN PANORAMA_TASK.CREATE_USER IS '创建用户'
/
COMMENT ON COLUMN PANORAMA_TASK.CREATE_TIME IS '创建时间'
/
COMMENT ON COLUMN PANORAMA_TASK.UPDATE_TIME IS '更新时间'
/
COMMENT ON COLUMN PANORAMA_TASK.STATUS IS '任务状态：0-创建中，1-已完成，2-已导出'
/

-- 2. 热点信息表
CREATE TABLE PANORAMA_HOTSPOT
(
    HOTSPOT_ID          NUMBER,
    TASK_ID             NUMBER,
    HOTSPOT_XML_ID      VARCHAR2(100),
    ORIGINAL_TITLE      VARCHAR2(500),
    ORIGINAL_DESCRIPTION VARCHAR2(1000),
    EDITED_TITLE        VARCHAR2(500),
    EDITED_DESCRIPTION  VARCHAR2(1000),
    DEVICE_ID           NUMBER,
    PAN                 VARCHAR2(50),
    TILT                VARCHAR2(50),
    SKINID              VARCHAR2(100),
    URL                 VARCHAR2(1000),
    TARGET              VARCHAR2(500),
    IS_EDITED           NUMBER(1) DEFAULT 0,
    CREATE_TIME         DATE DEFAULT SYSDATE,
    UPDATE_TIME         DATE DEFAULT SYSDATE
)
/

COMMENT ON TABLE PANORAMA_HOTSPOT IS '全景图热点信息表'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.HOTSPOT_ID IS '热点ID'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.TASK_ID IS '关联任务ID'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.HOTSPOT_XML_ID IS 'XML文件中的热点ID'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.ORIGINAL_TITLE IS '原始热点标题'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.ORIGINAL_DESCRIPTION IS '原始热点描述'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.EDITED_TITLE IS '编辑后热点标题'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.EDITED_DESCRIPTION IS '编辑后热点描述'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.DEVICE_ID IS '关联单机信息ID'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.PAN IS '水平角度'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.TILT IS '垂直角度'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.SKINID IS '热点皮肤ID'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.URL IS '热点链接地址'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.TARGET IS '热点目标'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.IS_EDITED IS '是否已编辑：0-未编辑，1-已编辑'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.CREATE_TIME IS '创建时间'
/
COMMENT ON COLUMN PANORAMA_HOTSPOT.UPDATE_TIME IS '更新时间'
/

-- 3. 单机信息表
CREATE TABLE PANORAMA_DEVICE
(
    DEVICE_ID     NUMBER,
    TASK_ID       NUMBER,
    DEVICE_NAME   VARCHAR2(255) NOT NULL,
    DEVICE_CODE   VARCHAR2(100),
    BATCH_NO      VARCHAR2(100),
    SEQUENCE_NO   NUMBER,
    MODEL_ID      VARCHAR2(100),
    MODEL_NAME    VARCHAR2(255),
    CREATE_TIME   DATE DEFAULT SYSDATE
)
/

COMMENT ON TABLE PANORAMA_DEVICE IS '单机信息表'
/
COMMENT ON COLUMN PANORAMA_DEVICE.DEVICE_ID IS '单机ID'
/
COMMENT ON COLUMN PANORAMA_DEVICE.TASK_ID IS '关联任务ID'
/
COMMENT ON COLUMN PANORAMA_DEVICE.DEVICE_NAME IS '单机名称'
/
COMMENT ON COLUMN PANORAMA_DEVICE.DEVICE_CODE IS '单机代号'
/
COMMENT ON COLUMN PANORAMA_DEVICE.BATCH_NO IS '批次号'
/
COMMENT ON COLUMN PANORAMA_DEVICE.SEQUENCE_NO IS '序号'
/
COMMENT ON COLUMN PANORAMA_DEVICE.MODEL_ID IS '型号ID'
/
COMMENT ON COLUMN PANORAMA_DEVICE.MODEL_NAME IS '型号名称'
/
COMMENT ON COLUMN PANORAMA_DEVICE.CREATE_TIME IS '创建时间'
/

-- 创建序列
CREATE SEQUENCE SEQ_PANORAMA_TASK
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE
/

CREATE SEQUENCE SEQ_PANORAMA_HOTSPOT
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE
/

CREATE SEQUENCE SEQ_PANORAMA_DEVICE
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE
/

-- 创建索引（基于常用查询字段）
CREATE INDEX IDX_HOTSPOT_TASK_ID ON PANORAMA_HOTSPOT(TASK_ID)
/
CREATE INDEX IDX_HOTSPOT_EDITED ON PANORAMA_HOTSPOT(IS_EDITED)
/
CREATE INDEX IDX_DEVICE_TASK_ID ON PANORAMA_DEVICE(TASK_ID)
/
CREATE INDEX IDX_TASK_STATUS ON PANORAMA_TASK(STATUS)
/
CREATE INDEX IDX_TASK_CREATE_TIME ON PANORAMA_TASK(CREATE_TIME)
/
CREATE INDEX IDX_TASK_MODEL_ID ON PANORAMA_TASK(MODEL_ID)
/
CREATE INDEX IDX_DEVICE_MODEL_ID ON PANORAMA_DEVICE(MODEL_ID)
/

-- 插入测试数据（可选）
INSERT INTO PANORAMA_TASK (TASK_ID, TASK_NAME, MODEL_ID, MODEL_NAME, DESCRIPTION, CREATE_USER, STATUS)
VALUES (SEQ_PANORAMA_TASK.NEXTVAL, '测试任务1', 'M001', 'Model-A型号', '全景图热点编辑测试任务', 'adm', 0)
/

COMMIT
/
