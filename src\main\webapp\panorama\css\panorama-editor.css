/* 全景图热点编辑系统样式 */

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    overflow-x: hidden;
}

/* 顶部导航栏 */
.panorama-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    height: 100%;
}

.header-left .system-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-left .system-title i {
    font-size: 24px;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 400px;
    margin: 0 40px;
}

.task-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
}

.task-selector label {
    font-weight: 500;
    white-space: nowrap;
}

.task-selector .layui-form-item {
    margin-bottom: 0;
}

.task-selector .layui-input-inline {
    margin-right: 0;
}

/* Layui select 组件样式自定义 */
.task-selector .layui-form-select {
    min-width: 250px;
}

/* 选择框标题区域 */
.task-selector .layui-form-select .layui-select-title {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    border-radius: 4px;
}

/* 选择框输入框 */
.task-selector .layui-form-select .layui-select-title input {
    color: white !important;
    background: transparent !important;
}

/* 选择框占位符 */
.task-selector .layui-form-select .layui-select-title input::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* 下拉箭头 */
.task-selector .layui-form-select .layui-edge {
    border-top-color: rgba(255, 255, 255, 0.8) !important;
}

/* 下拉选项容器 */
.task-selector .layui-form-select dl {
    background-color: white !important;
    border: 1px solid #e6e8eb !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 下拉选项 */
.task-selector .layui-form-select dl dd {
    color: #333 !important;
    background-color: white !important;
}

/* 下拉选项悬停 */
.task-selector .layui-form-select dl dd:hover {
    background-color: #f8f9fa !important;
    color: #667eea !important;
}

/* 下拉选项选中 */
.task-selector .layui-form-select dl dd.layui-this {
    background-color: #667eea !important;
    color: white !important;
}

/* 选择框获得焦点时 */
.task-selector .layui-form-select.layui-form-selected .layui-select-title {
    border-color: rgba(255, 255, 255, 0.6) !important;
}

.header-right {
    display: flex;
    gap: 10px;
}

/* 主体容器 - 全宽度无边距 */
.panorama-container {
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

.container-content {
    display: flex;
    height: calc(100vh - 60px);
    position: relative;
}

/* 左侧面板 - 可调整宽度 */
.left-panel {
    width: 30%;
    min-width: 300px;
    max-width: 60%;
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
}

/* 可拖拽分隔条 */
.resize-handle {
    width: 6px;
    background: #f0f0f0;
    cursor: col-resize;
    position: relative;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.resize-handle:hover {
    background: #d0d0d0;
}

.resize-handle::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 30px;
    background: #999;
    border-radius: 1px;
}

.resize-handle:hover::before {
    background: #666;
}

/* 右侧面板 - 自适应宽度 */
.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    min-width: 300px;
}

/* 卡片样式 */
.panel-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.panel-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: #fafbfc;
    border-bottom: 1px solid #e6e8eb;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-header h3 i {
    color: #667eea;
}

.card-tools {
    display: flex;
    gap: 8px;
}

.card-body {
    padding: 20px;
}

/* 任务信息和文件上传合并区域 */
.task-upload-combined {
    flex-shrink: 0;
    height: 200px;
    border-bottom: 1px solid #e6e8eb;
}

.task-upload-combined .card-header {
    padding: 12px 16px;
    background: #fafbfc;
}

.task-upload-combined .card-body {
    padding: 16px;
    height: calc(100% - 45px);
    overflow-y: auto;
}

/* 任务信息区域 - 两行两列布局 */
.task-info-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.task-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px 16px;
}

.task-info .info-item {
    display: flex;
    align-items: flex-start;
    font-size: 13px;
}

.task-info .info-label {
    font-weight: 500;
    color: #666;
    min-width: 70px;
    flex-shrink: 0;
}

.task-info .info-value {
    color: #333;
    word-break: break-all;
    flex: 1;
}

/* 文件上传区域 - 一行显示 */
.upload-section {
    margin-top: 12px;
    display: flex;
    gap: 16px;
}

.upload-section .upload-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    flex: 1;
}

.upload-label {
    font-weight: 500;
    color: #666;
    font-size: 12px;
    white-space: nowrap;
}

.upload-controls {
    display: flex;
    align-items: center;
    gap: 6px;
}

.upload-status {
    font-size: 11px;
    color: #999;
    white-space: nowrap;
}

/* 紧凑按钮样式 */
.upload-controls .layui-btn {
    padding: 4px 10px;
    font-size: 12px;
    height: 26px;
    line-height: 18px;
}

/* 表格卡片 - 占据剩余高度，分页栏在底部 */
.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100% - 200px);
    overflow: hidden;
}

.table-card .card-header {
    padding: 12px 16px;
    background: #fafbfc;
    flex-shrink: 0;
}

.table-card .card-body {
    flex: 1;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 表格容器优化 - 分页栏固定在底部 */
.table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.table-container .layui-table-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100% !important;
}

.table-container .layui-table-box {
    flex: 1;
    overflow: hidden;
}

.table-container .layui-table-body {
    height: 100% !important;
    overflow-y: auto !important;
}

.table-container .layui-table-page {
    flex-shrink: 0;
    border-top: 1px solid #e6e8eb;
    background: #fafbfc;
}

/* 预览卡片 - 全高度显示 */
.preview-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.preview-card .card-header {
    padding: 12px 16px;
    background: #fafbfc;
    flex-shrink: 0;
}

.preview-card .card-body {
    flex: 1;
    padding: 0;
    position: relative;
    height: calc(100% - 45px);
}

.preview-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: #f8f9fa;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-size: 14px;
}

.preview-placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #ddd;
}

.panorama-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container-content {
        grid-template-columns: 400px 1fr;
    }
}

@media (max-width: 992px) {
    .container-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        height: auto;
    }
    
    .left-panel {
        order: 1;
    }
    
    .right-panel {
        order: 2;
        min-height: 500px;
    }
    
    .header-content {
        flex-direction: column;
        height: auto;
        padding: 10px 20px;
    }
    
    .header-center {
        margin: 10px 0;
        max-width: none;
    }
    
    .panorama-header {
        height: auto;
    }
    
    .panorama-container {
        margin-top: 120px;
    }
}

@media (max-width: 768px) {
    .panorama-container {
        padding: 10px;
    }
    
    .container-content {
        gap: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .upload-section .upload-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .upload-label {
        min-width: auto;
    }
}

/* 表格样式优化 */
.layui-table-cell {
    height: auto !important;
    padding: 8px 15px !important;
}

.layui-table th {
    background-color: #fafbfc !important;
    font-weight: 600 !important;
}

/* 对话框样式 */
.layui-layer-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
}

/* 按钮样式优化 */
.layui-btn {
    border-radius: 4px;
    font-weight: 500;
}

.layui-btn-normal {
    background-color: #667eea;
    border-color: #667eea;
}

.layui-btn-normal:hover {
    background-color: #5a6fd8;
    border-color: #5a6fd8;
}

/* 状态标签 */
.layui-badge {
    border-radius: 12px;
    font-size: 11px;
    padding: 2px 8px;
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar {
    width: 6px;
}

.left-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 5px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
