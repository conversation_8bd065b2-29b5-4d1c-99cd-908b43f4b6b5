# 全景图热点编辑系统 - 单机信息Excel模板说明

## 📋 Excel模板格式

### 表头结构（第一行）
| 序号 | 单机名称 | 单机代号 | 批次号 | 型号ID | 型号名称 |
|------|----------|----------|--------|--------|----------|
| SEQUENCE_NO | DEVICE_NAME | DEVICE_CODE | BATCH_NO | MODEL_ID | MODEL_NAME |

### 字段说明

| 列位置 | 字段名称 | 数据库字段 | 数据类型 | 是否必填 | 说明 |
|--------|----------|------------|----------|----------|------|
| A列 | 序号 | SEQUENCE_NO | NUMBER | 否 | 设备序号，可以是数字 |
| B列 | 单机名称 | DEVICE_NAME | VARCHAR2(255) | **是** | 设备的完整名称 |
| C列 | 单机代号 | DEVICE_CODE | VARCHAR2(100) | 否 | 设备的代号或编码 |
| D列 | 批次号 | BATCH_NO | VARCHAR2(100) | 否 | 生产批次号 |
| E列 | 型号ID | MODEL_ID | VARCHAR2(100) | 否 | 设备型号标识 |
| F列 | 型号名称 | MODEL_NAME | VARCHAR2(255) | 否 | 设备型号名称 |

## 📝 Excel模板示例

```
序号    单机名称              单机代号    批次号      型号ID    型号名称
1       主控制器单元          MCU-001     2024-001    T001      主控制器T型
2       数据采集单元          DAQ-002     2024-001    T002      数据采集T型
3       信号处理单元          SPU-003     2024-002    T003      信号处理T型
4       通信接口单元          CIU-004     2024-002    T004      通信接口T型
5       电源管理单元          PMU-005     2024-003    T005      电源管理T型
```

## 🔧 上传逻辑说明

### 后端处理流程
1. **文件接收**：接收上传的Excel文件（.xlsx/.xls格式）
2. **临时存储**：保存到临时目录 `tempPath/excel_时间戳/`
3. **Excel解析**：使用hutool的ExcelUtil读取文件内容
4. **数据验证**：检查行数据完整性（至少4列数据）
5. **数据库存储**：逐行插入到PANORAMA_DEVICE表
6. **清理临时文件**：删除临时目录和文件

### 前端上传配置
- **接口地址**：`POST /panorama/upload/excel`
- **支持格式**：xlsx, xls
- **必需参数**：taskId（当前选中的任务ID）
- **上传前检查**：必须先选择任务

### 数据库存储
```sql
INSERT INTO PANORAMA_DEVICE 
(DEVICE_ID, TASK_ID, DEVICE_NAME, DEVICE_CODE, BATCH_NO, SEQUENCE_NO, MODEL_ID, MODEL_NAME, CREATE_TIME) 
VALUES 
(SEQ_PANORAMA_DEVICE.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, SYSDATE)
```

## ⚠️ 注意事项

### 1. 文件格式要求
- 必须是Excel格式（.xlsx 或 .xls）
- 第一行为表头，从第二行开始为数据
- 至少需要前4列数据（序号、单机名称、单机代号、批次号）

### 2. 数据要求
- **单机名称**是必填字段，不能为空
- 其他字段可以为空，但建议填写完整
- 文本字段中的单引号会被自动转义处理

### 3. 任务关联
- 上传前必须先选择或创建任务
- 所有设备信息都会关联到当前选中的任务
- 同一任务可以多次上传，数据会累加

### 4. 错误处理
- 文件格式错误：提示"上传Excel文件失败"
- 任务未选择：提示"请先选择任务"
- 数据解析错误：显示具体错误信息

## 🧪 测试步骤

### 1. 准备测试数据
创建一个Excel文件，包含以下测试数据：
```
序号    单机名称              单机代号    批次号      型号ID    型号名称
1       测试设备1             TEST-001    2024-T01    T001      测试型号1
2       测试设备2             TEST-002    2024-T01    T002      测试型号2
3       测试设备3             TEST-003    2024-T02    T003      测试型号3
```

### 2. 执行测试
1. 打开全景图热点编辑系统
2. 创建或选择一个任务
3. 点击"上传单机信息"按钮
4. 选择准备好的Excel文件
5. 确认上传成功提示

### 3. 验证结果
- 检查上传状态显示为"已上传"
- 在数据库中查询PANORAMA_DEVICE表确认数据
- 后续可以在热点编辑中关联这些设备信息

## 🔍 调试信息

### 日志查看
- 后端日志：查看PanoramaService.uploadDeviceExcel()方法的日志
- 前端控制台：查看上传请求和响应信息
- 数据库日志：确认INSERT语句执行情况

### 常见问题
1. **上传按钮无响应**：检查是否已选择任务
2. **文件格式错误**：确认文件是.xlsx或.xls格式
3. **数据未保存**：检查数据库连接和表结构
4. **中文乱码**：确保Excel文件使用UTF-8编码保存

---
**创建时间**：2025-01-27 16:00:00  
**用途**：全景图热点编辑系统单机信息Excel上传功能测试指南
