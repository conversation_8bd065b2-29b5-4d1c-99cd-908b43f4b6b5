-- 全景图热点编辑系统 - PANORAMA_DEVICE表结构更新脚本
-- 添加UPDATE_TIME字段以支持去重更新功能
-- 执行时间：2025-01-27 16:20:00

-- 检查UPDATE_TIME字段是否存在，如果不存在则添加
DECLARE
    column_exists NUMBER := 0;
BEGIN
    SELECT COUNT(*)
    INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'PANORAMA_DEVICE'
    AND column_name = 'UPDATE_TIME';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE PANORAMA_DEVICE ADD UPDATE_TIME DATE DEFAULT SYSDATE';
        DBMS_OUTPUT.PUT_LINE('UPDATE_TIME字段已添加到PANORAMA_DEVICE表');
        
        -- 为现有记录设置UPDATE_TIME值
        EXECUTE IMMEDIATE 'UPDATE PANORAMA_DEVICE SET UPDATE_TIME = CREATE_TIME WHERE UPDATE_TIME IS NULL';
        DBMS_OUTPUT.PUT_LINE('现有记录的UPDATE_TIME字段已初始化');
        
        COMMIT;
    ELSE
        DBMS_OUTPUT.PUT_LINE('UPDATE_TIME字段已存在，无需添加');
    END IF;
END;
/

-- 验证字段是否添加成功
SELECT column_name, data_type, data_default
FROM user_tab_columns
WHERE table_name = 'PANORAMA_DEVICE'
AND column_name IN ('CREATE_TIME', 'UPDATE_TIME')
ORDER BY column_name;

-- 显示表结构
DESC PANORAMA_DEVICE;
