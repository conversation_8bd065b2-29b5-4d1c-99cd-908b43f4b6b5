<?xml version="1.0" encoding="UTF-8"?>
<tour apprev="17319" appversion="6.0.4" start="node1">
  <panorama id="node1">
    <input levelingpitch="0" levelingroll="0" width="1664" overlap="1" height="1664" leveltilesize="510" leveltileurl="tiles/node1/cf_%c/l_%l/c_%x/tile_%y.jpg" levelbias="0.400" levelbiashidpi="1.000">
      <level width="1664" height="1664" predecode="0" preload="0"/>
      <level width="832" height="832" predecode="0" preload="0"/>
      <level width="416" height="416" predecode="1" preload="1"/>
      <preview strip="1" color="0x808080"/>
    </input>
    <view fovmode="0" pannorth="0">
      <start tilt="0" projection="4" fov="70" pan="0"/>
      <flyin tilt="-90" projection="9" fov="170" pan="0"/>
      <min tilt="-48.1085" fovpixel="2" pan="-180"/>
      <max tilt="48.1085" fovstereographic="270" fovfisheye="360" fov="120" pan="180"/>
    </view>
    <userdata comment="" source="" latitude="" title="" datetime="2005/10/24 22:35" tags="浏览器下载" customnodeid="" longitude="" author="unknown" info="" description="" copyright=""/>
    <hotspots width="180" wordwrap="1" height="20">
      <label borderradius="1" border="1" width="180" wordwrap="1" height="20" bordercolor="0x000000" textcolor="0x000000" background="1" textalpha="1" backgroundalpha="1" backgroundcolor="0xffffff" enabled="1" borderalpha="1"/>
      <polystyle handcursor="1" mode="1" bordercolor="0x0000ff" backgroundalpha="0.25098" backgroundcolor="0x0000ff" borderalpha="1"/>
      <hotspot tilt="-2.43" url="http://www.baidu.com" title="S中继功放1 dh1 C01-01" target="_blank" skinid="stand-alone" id="Point01" description="S中继功放1 dh1 C01-01" pan="-24.24"/>
      <hotspot tilt="-3.65" url="" title="150Ah锂离子蓄电池组结构块a dh3 C02-01" target="" skinid="stand-alone" id="Point01" description="150Ah锂离子蓄电池组结构块a dh3 C02-01" pan="-57.42"/>
      <hotspot tilt="4.24" url="{node2}" title="跳转" target="58.4/0.0/117.2" skinid="ht_node" id="Point02" description="跳转" pan="-322.22"/>
      <hotspot tilt="-4.85" url="" title="150Ah锂离子蓄电池组结构块a dh3 C02-01" target="" skinid="stand-alone" id="Point03" description="150Ah锂离子蓄电池组结构块a1 dh3 C02-01" pan="-92.78"/>
    </hotspots>
    <media/>
    <transition zoomoutpause="1" blendtime="1" zoomfov="20" zoomin="0" zoomout="0" type="crossdissolve" blendcolor="0x000000" softedge="0" zoomspeed="2" enabled="1"/>
    <animation useinautorotation="0" syncanimationwithvideo="0" animsequence="Animation01"/>
    <control dblclickfullscreen="0" rubberband="0" sensitivity="8" invertwheel="0" lockedmouse="0" invertcontrol="1" contextprojections="0" hideabout="0" simulatemass="1" lockedkeyboardzoom="0" lockedkeyboard="0" lockedwheel="0" contextfullscreen="1" speedwheel="1"/>
  </panorama>
  <panorama id="node2">
    <input levelingpitch="0" levelingroll="0" width="143" overlap="1" height="143" leveltilesize="510" leveltileurl="tiles/node2/cf_%c/l_%l/c_%x/tile_%y.jpg" levelbias="0.400" levelbiashidpi="1.000">
      <level width="143" height="143" predecode="1" preload="1"/>
      <preview strip="1" color="0x808080"/>
    </input>
    <view fovmode="0" pannorth="0">
      <start tilt="0" projection="4" fov="70" pan="0"/>
      <flyin tilt="-90" projection="9" fov="170" pan="0"/>
      <min tilt="-58.6129" fovpixel="2" pan="-180"/>
      <max tilt="58.6129" fovstereographic="270" fovfisheye="360" fov="120" pan="180"/>
    </view>
    <userdata comment="" source="" latitude="" title="" datetime="" tags="浏览器下载" customnodeid="" longitude="" author="" info="" description="" copyright=""/>
    <hotspots width="180" wordwrap="1" height="20">
      <label borderradius="1" border="1" width="180" wordwrap="1" height="20" bordercolor="0x000000" textcolor="0x000000" background="1" textalpha="1" backgroundalpha="1" backgroundcolor="0xffffff" enabled="1" borderalpha="1"/>
      <polystyle handcursor="1" mode="1" bordercolor="0x0000ff" backgroundalpha="0.25098" backgroundcolor="0x0000ff" borderalpha="1"/>
      <hotspot tilt="-3.47" url="" title="hrth" target="" skinid="stand-alone" id="Point01" description="rhrthrthtr" pan="-314.75"/>
    </hotspots>
    <media/>
    <transition zoomoutpause="1" blendtime="1" zoomfov="20" zoomin="0" zoomout="0" type="crossdissolve" blendcolor="0x000000" softedge="0" zoomspeed="2" enabled="1"/>
    <animation useinautorotation="0" syncanimationwithvideo="0" animsequence="Animation01"/>
    <control dblclickfullscreen="0" rubberband="0" sensitivity="8" invertwheel="0" lockedmouse="0" invertcontrol="1" contextprojections="0" hideabout="0" simulatemass="1" lockedkeyboardzoom="0" lockedkeyboard="0" lockedwheel="0" contextfullscreen="1" speedwheel="1"/>
  </panorama>
  <masternode>
    <userdata comment="" source="" latitude="" title="" datetime="" tags="" customnodeid="" longitude="" author="" info="" description="" copyright=""/>
  </masternode>
</tour>
