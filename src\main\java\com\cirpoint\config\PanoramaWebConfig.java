package com.cirpoint.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 全景图Web配置
 * 配置静态资源访问路径
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Configuration
public class PanoramaWebConfig implements WebMvcConfigurer {

    @Value("${file.upload.path:D:/DataPkgFile/}")
    private String fileUploadPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 添加全景图文件的静态资源映射
        registry.addResourceHandler("/panorama/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600); // 缓存1小时
        
        // 添加全景图预览文件的资源映射
        registry.addResourceHandler("/file/preview/**")
                .addResourceLocations("file:" + fileUploadPath)
                .setCachePeriod(0); // 不缓存，确保实时更新
    }
}
