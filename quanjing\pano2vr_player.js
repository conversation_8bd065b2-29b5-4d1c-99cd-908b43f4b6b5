//////////////////////////////////////////////////////////////////////
// Pano2VR 6.0.4/17319 HTML5/CSS3 & WebGL Panorama Player           //
// License: BKYM.COM                                                //
// (c) 2019, Garden Gnome Software, http://ggnome.com               //
//////////////////////////////////////////////////////////////////////

var G="function"==typeof Object.defineProperties?Object.defineProperty:function(p,f,h){p!=Array.prototype&&p!=Object.prototype&&(p[f]=h.value)},N="undefined"!=typeof window&&window===this?this:"undefined"!=typeof global&&null!=global?global:this;function O(p,f){if(f){var h=N;p=p.split(".");for(var d=0;d<p.length-1;d++){var b=p[d];b in h||(h[b]={});h=h[b]}p=p[p.length-1];d=h[p];f=f(d);f!=d&&null!=f&&G(h,p,{configurable:!0,writable:!0,value:f})}}var P;
if("function"==typeof Object.setPrototypeOf)P=Object.setPrototypeOf;else{var Q;a:{var R={Zm:!0},S={};try{S.__proto__=R;Q=S.Zm;break a}catch(p){}Q=!1}P=Q?function(p,f){p.__proto__=f;if(p.__proto__!==f)throw new TypeError(p+" is not extensible");return p}:null}var T=P;O("Object.setPrototypeOf",function(p){return p||T});
O("Array.prototype.fill",function(p){return p?p:function(f,h,d){var b=this.length||0;0>h&&(h=Math.max(0,b+h));if(null==d||d>b)d=b;d=Number(d);0>d&&(d=Math.max(0,b+d));for(h=Number(h||0);h<d;h++)this[h]=f;return this}});
var __extends=this&&this.__extends||function(){function p(f,h){p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var a in b)b.hasOwnProperty(a)&&(d[a]=b[a])};return p(f,h)}return function(f,h){function d(){this.constructor=f}p(f,h);f.prototype=null===h?Object.create(h):(d.prototype=h.prototype,new d)}}();
function U(){var p="perspective",f=["Webkit","Moz","O","ms","Ms"],h;for(h=0;h<f.length;h++)"undefined"!==typeof document.documentElement.style[f[h]+"Perspective"]&&(p=f[h]+"Perspective");"undefined"!==typeof document.documentElement.style[p]?"webkitPerspective"in document.documentElement.style?(p=document.createElement("style"),f=document.createElement("div"),h=document.head||document.getElementsByTagName("head")[0],p.textContent="@media (-webkit-transform-3d) {#ggswhtml5{height:5px}}",h.appendChild(p),
f.id="ggswhtml5",document.documentElement.appendChild(f),h=5===f.offsetHeight,p.parentNode.removeChild(p),f.parentNode.removeChild(f)):h=!0:h=!1;return h}function V(){var p;if(p=!!window.WebGLRenderingContext)try{var f=document.createElement("canvas");f.width=100;f.height=100;var h=f.getContext("webgl");h||(h=f.getContext("experimental-webgl"));p=!!h}catch(d){p=!1}return p}var ggP2VR;
(function(p){var f=function(){function d(b){this.g=null;this.lg=this.hl=this.gb=!1;this.hb=!0;this.mb=this.Ea=this.wa=0;this.f=70;this.Ua=0;this.autoplay=this.qg=!1;this.id="";this.i=this.pan=0;this.g=b;this.ed=this.Hc=100;this.xd=1}d.prototype.Nb=function(b){var a;if(a=b.getAttributeNode("id"))this.id=a.nodeValue.toString();if(a=b.getAttributeNode("pan"))this.pan=Number(a.nodeValue);if(a=b.getAttributeNode("tilt"))this.i=Number(a.nodeValue)};d.prototype.Dm=function(b){var a="",c=this.g,k=!0;if(c.Ih){var d=
new p.xa(0,0,-100);d.wa(-this.i*Math.PI/180);d.Ea(this.pan*Math.PI/180);d.Ea(-c.pan.c*Math.PI/180);d.wa(c.i.c*Math.PI/180);d.mb(c.O.c*Math.PI/180);.01<=d.z&&(k=!1)}c.Ac&&(a+="perspective("+b+"px) ");a=a+("translate3d(0px,0px,"+b+"px) ")+("rotateZ("+c.O.c.toFixed(10)+"deg) ");a+="rotateX("+c.i.c.toFixed(10)+"deg) ";a+="rotateY("+(-c.pan.c).toFixed(10)+"deg) ";a+="rotateY("+this.pan.toFixed(10)+"deg) ";a+="rotateX("+(-this.i).toFixed(10)+"deg) ";b=1E4;d=this.b.videoWidth;var e=this.b.videoHeight;if(0==
d||0==e)d=640,e=480;0<this.Hc&&(d=this.Hc);0<this.ed&&(e=this.ed);0<d&&0<e&&(this.b.width=d,this.b.height=e,this.b.style.width=d+"px",this.b.style.height=e+"px");0<this.f&&(b=d/(2*Math.tan(this.f/2*Math.PI/180)));a+="translate3d(0px,0px,"+(-b).toFixed(10)+"px) ";a+="rotateZ("+this.mb.toFixed(10)+"deg) ";a+="rotateY("+(-this.Ea).toFixed(10)+"deg) ";a+="rotateX("+this.wa.toFixed(10)+"deg) ";this.xd&&1!=this.xd&&(a+="scaleY("+this.xd+") ");a+="translate3d("+-d/2+"px,"+-e/2+"px,0px) ";this.b.style[c.Ra+
"Origin"]="0% 0%";this.gb&&(a="",1==this.Ua&&(a+="scale("+Math.min(c.o.width/d,c.o.height/e)+") "),a+="translate3d("+-d/2+"px,"+-e/2+"px,0px) ");this.Ao!=a&&(this.Ao=a,this.b.style[c.Ra]=a,this.b.style.visibility=k&&this.hb?"visible":"hidden",this.lg&&this.hl==this.gb&&(this.b.style[c.bd]="all 0s linear 0s"),this.hl=this.gb)};d.prototype.Cf=function(b){this.b&&(this.b.style.visibility=b?"visible":"hidden")};d.prototype.Ke=function(){var b=this.g;b.Z?(this.b.style.left=b.o.width/2+"px",this.b.style.top=
b.o.height/2+"px"):(this.b.style.left=b.margin.left+b.o.width/2+"px",this.b.style.top=b.margin.top+b.o.height/2+"px")};return d}();p.Dq=f;var h=function(d){function b(a){a=d.call(this,a)||this;a.la=!1;a.pm=!1;a.Ml=!1;a.If=!1;a.$i=!1;a.Aj=!1;a.Le=!1;a.Rb=null;a.ph=null;a.Tg=0;a.ze=0;a.nf=!1;a.kp=!1;a.stopped=!1;a.url=[];a.loop=0;a.level=1;a.fc=0;a.mode=1;a.xh=10;a.tb=0;a.Hf=0;a.ka=1;a.Tc=0;a.Cc=0;a.Dc=0;a.Uc=0;a.Zh=-1;return a}__extends(b,d);b.prototype.Wj=function(){var a=this,c=a.b.play();if(void 0!==
c)c.then(function(){a.Di()})["catch"](function(){a.b.pause();a.pm&&(a.b.muted=!0,a.b.play())})};b.prototype.lm=function(){this.la&&this.stopped?this.stopped=!1:0==this.loop?this.la?(this.Rb=null,this.Md()):this.b.play():0<this.Zc?(this.Zc--,this.la||(this.b.currentTime=0),this.$i&&(this.cd&&0==this.cd.gain.value||0==this.oc.gain.value&&0==this.Wb.gain.value&&0==this.Ub.gain.value&&0==this.Vb.gain.value)||(this.la?(this.Rb=null,this.Md()):this.b.play())):this.la&&(this.Rb=null,this.nf=!1)};b.prototype.Di=
function(){this.Le=!1;var a=this.g.pa;a&&(this.la||(this.source=a.createMediaElementSource(this.b)),2==this.mode||3==this.mode||5==this.mode?(this.ud=a.createChannelSplitter(2),this.oc=a.createGain(),this.Ub=a.createGain(),this.Vb=a.createGain(),this.Wb=a.createGain(),this.Wf=a.createChannelMerger(2),this.la||this.source.connect(this.ud),this.ud.connect(this.oc,0),this.ud.connect(this.Ub,0),this.ud.connect(this.Vb,1),this.ud.connect(this.Wb,1),this.oc.connect(this.Wf,0,0),this.Ub.connect(this.Wf,
0,1),this.Vb.connect(this.Wf,0,0),this.Wb.connect(this.Wf,0,1),this.Wf.connect(a.destination),1==this.Zh&&this.rm()):(this.cd=a.createGain(),this.la||this.source.connect(this.cd),this.cd.connect(a.destination)))};b.prototype.rm=function(){1!=this.Zh||this.Le||!this.ud||2!=this.mode&&3!=this.mode&&5!=this.mode||(this.ud.connect(this.Vb,0),this.ud.connect(this.Wb,0))};b.prototype.Qg=function(){var a=this.g.pa;this.gb||this.Aj||(this.oc.gain.setValueAtTime(this.Tc,a.currentTime),this.Wb.gain.setValueAtTime(this.Uc,
a.currentTime),this.Ub.gain.setValueAtTime(this.Cc,a.currentTime),this.Vb.gain.setValueAtTime(this.Dc,a.currentTime))};b.prototype.yi=function(){if(!this.Le||4==this.mode||6==this.mode){var a=this.g,c=this.g.pa;if(this.b||this.la){var b,d=this.pan-a.pan.c;for(b=this.i-a.i.c;-180>d;)d+=360;for(;180<d;)d-=360;var e=this.fc,m=this.xh;0==m&&(m=.01);0>m&&(m=a.f.c);this.Qb||(this.Qb=new p.xa,this.Qb.Kk(this.pan,this.i));0!=this.mode&&1!=this.mode||!c||this.cd&&this.cd.gain.setValueAtTime(this.level*a.V*
this.ka,c.currentTime);if(2==this.mode&&c){var t=.5*Math.cos(d*Math.PI/180)+.5;this.Tc=Math.sqrt(t)*this.ka*this.level*a.V;this.Uc=Math.sqrt(t)*this.ka*this.level*a.V;this.Cc=Math.sqrt(1-t)*this.ka*this.level*a.V;this.Dc=Math.sqrt(1-t)*this.ka*this.level*a.V;this.Qg()}if(3==this.mode){0>d?d<-this.tb?d+=this.tb:d=0:d=d>this.tb?d-this.tb:0;var g=this.level;b=Math.abs(b);b=b<this.Hf?0:b-this.Hf;var h=1-b/m;if(Math.abs(d)>m||0>h)t=g*e*a.V,c?(this.Tc=t*this.ka,this.Uc=t*this.ka,this.Dc=this.Cc=0,this.Qg()):
this.b.volume=g*e*a.V;else if(t=1-Math.abs(d/m),c){var f=g*(e+(1-e)*h*t)*a.V;t=g*e*a.V;0<=d?(this.Tc=f*this.ka,this.Uc=t*this.ka):(this.Tc=t*this.ka,this.Uc=f*this.ka);2*Math.abs(d)<m?(t=1-Math.abs(2*d)/m,f=g*(e+(1-e)*h*t)*a.V,t=.5*g*(1-e)*h*(1-t)*a.V,0<=d?(this.Uc=f*this.ka,this.Dc=t*this.ka,this.Cc=0):(this.Tc=f*this.ka,this.Cc=t*this.ka,this.Dc=0)):(t=1-(Math.abs(2*d)-m)/m,f=.5*g*(1-e)*h*t*a.V,0<=d?(this.Dc=f*this.ka,this.Cc=0):(this.Cc=f*this.ka,this.Dc=0));this.Qg()}else this.b.volume=g*(e+(1-
e)*h*t)*a.V}4==this.mode&&(Math.abs(d)<this.tb&&Math.abs(b)<this.Hf?this.If||(this.If=!0,this.Zc=this.loop-1,a.Ze||(this.b.play(),this.Le&&this.Di())):this.If=!1);5==this.mode&&(b=180*Math.acos(a.Ti.ci(this.Qb))/Math.PI,b<this.tb?c?(this.Tc=this.level*a.V*this.ka,this.Uc=this.level*a.V*this.ka,this.Dc=this.Cc=0,this.Qg()):this.b.volume=this.level*a.V:c?(b<this.tb+m?(0>d?d=d>-this.tb?0:d+this.tb:d=d<this.tb?0:d-this.tb,f=1-Math.max(b-this.tb,0)/m,t=Math.max(1-Math.abs(d)*Math.cos(this.i*Math.PI/180)/
m,0),0<d?(this.Tc=this.level*(f*(1-this.fc)+this.fc)*a.V*this.ka,this.Uc=this.level*(f*t*(1-this.fc)+this.fc)*a.V*this.ka,this.Cc=0,this.Dc=this.level*f*(1-t)*a.V*this.ka):(this.Tc=this.level*(f*t*(1-this.fc)+this.fc)*a.V*this.ka,this.Uc=this.level*(f*(1-this.fc)+this.fc)*a.V*this.ka,this.Cc=this.level*f*(1-t)*a.V*this.ka,this.Dc=0)):(f=this.level*this.fc*a.V,this.Tc=f*this.ka,this.Uc=f*this.ka,this.Dc=this.Cc=0),this.Qg()):(b-=this.tb,b<m&&0<m?(t=1-Math.abs(b/m),this.b.volume=this.level*(e+(1-e)*
t)*a.V):this.b.volume=e*a.V));6==this.mode&&(b=180*Math.acos(a.Ti.ci(this.Qb))/Math.PI,Math.abs(b)<this.tb?this.If||(this.If=!0,this.Zc=this.loop-1,this.la?this.nf||this.Md():this.b.play()):this.If=!1)}}};b.prototype.vn=function(){var a=this;a.Rb=this.g.pa.createBufferSource();a.Rb.addEventListener("ended",function(){a.lm()},!1);2==a.mode||3==a.mode||5==a.mode?a.Rb.connect(a.ud):a.Rb.connect(a.cd)};b.prototype.Md=function(){var a=this.g.pa,c=this.ze;this.ph?(null==this.Rb&&(this.vn(),this.Rb.buffer=
this.ph),this.Tg=a.currentTime-c,this.ze=0,this.nf=!0,this.stopped=!1,this.Rb.start(0,c),this.g.M("buffer Source started")):(this.g.M("bufferSoundPlay() -> no audio buffer -> playWhenReady"),this.kp=!0)};b.prototype.Ki=function(){var a=this.g.pa.currentTime-this.Tg;this.Te();this.ze=a};b.prototype.Te=function(){this.Rb&&this.nf&&(this.stopped=!0,this.Rb.disconnect(),this.Rb.stop(0),this.Rb=null);this.Tg=this.ze=0;this.nf=!1};b.prototype.jn=function(){var a=this.g.pa;return this.ze?this.ze:this.Tg?
a.currentTime-this.Tg:0};b.prototype.kn=function(a){this.Te();this.ze=a;this.Md()};b.prototype.addElement=function(){var a=-1,c=this,b=this.g,d=this.g.pa;try{for(var e=!1,m=0;m<b.N.length;m++)b.N[m].id==c.id&&(a=m,null==b.N[m].b&&!b.N[m].la||b.N[m].url.join()!=c.url.join()||b.N[m].loop!=c.loop||b.N[m].mode!=c.mode||(e=!0,b.N[m].pan=c.pan,b.N[m].i=c.i,b.N[m].level=c.level,b.N[m].fc=c.fc,b.N[m].xh=c.xh,b.N[m].tb=c.tb,b.N[m].Hf=c.Hf));if(e)b.M("Keep playing "+c.id);else{if(0<=a){var t=b.N[a];if(null!=
t.b||t.la)if(d&&b.La.enabled)b.La.Xg.push(t),1!=b.B.Oa&&2!=b.B.Oa&&b.La.Hk(t);else{try{t.la?t.Ki():t.b.pause()}catch(q){b.M(q)}try{t.Be()}catch(q){b.M(q)}}}d&&(this.Le=!0);c.b=document.createElement("audio");c.b.crossOrigin=b.crossOrigin;c.b.setAttribute("class","ggmedia");b.ff&&c.b.setAttribute("id",b.ff+c.id);for(m=0;m<c.url.length;m++)e=void 0,e=document.createElement("source"),""!=c.url[m]&&"#"!=c.url[m]&&(e.crossOrigin=b.crossOrigin,e.setAttribute("src",b.nc(c.url[m])),c.b.appendChild(e));c.b.volume=
c.level*b.V;if(0<c.b.childNodes.length&&(b.T.appendChild(c.b),c.b.addEventListener("ended",function(){c.lm()},!1),d)){c.$i=!1;0==c.loop&&c.source&&c.source.mediaElement&&(c.source.mediaElement.loop=!0);var g=new XMLHttpRequest;g.open("GET",b.nc(c.url[0]),!0);g.responseType="arraybuffer";g.onload=function(){d.decodeAudioData(g.response,function(a){c.Zh=a.numberOfChannels;1==c.Zh&&c.rm()})};g.send()}1<=c.loop&&(c.Zc=c.loop-1);0<=a?b.N[a]=c:b.N.push(c);c.yi();-1!=this.g.Pc.indexOf(c.id)||-1!=this.g.Pc.indexOf("_main")&&
-1==this.g.ce.indexOf(c.id)||(1!=c.mode&&2!=c.mode&&3!=c.mode&&5!=c.mode||!(0<=c.loop)||d&&b.La.enabled||(c.la||(c.b.autoplay=!0,c.Wj()),c.autoplay=!0),0==c.mode&&0<=c.loop&&(c.autoplay=!0,c.Wj()))}}catch(q){this.g.M(q)}};b.prototype.Be=function(){try{this.g.M("Remove Snd:"+this.id),this.la||(this.g.T.removeChild(this.b),this.b=null)}catch(a){this.g.M(a)}};b.prototype.Nb=function(a){d.prototype.Nb.call(this,a);var c;(c=a.getAttributeNode("url"))&&this.url.push(c.nodeValue.toString());if(c=a.getAttributeNode("level"))this.level=
Number(c.nodeValue);if(c=a.getAttributeNode("loop"))this.loop=Number(c.nodeValue);if(c=a.getAttributeNode("mode"))this.mode=Number(c.nodeValue);if(c=a.getAttributeNode("nodechangekeep"))this.Ml=1==Number(c.nodeValue);if(c=a.getAttributeNode("field"))this.xh=Number(c.nodeValue);if(c=a.getAttributeNode("ambientlevel"))this.fc=Number(c.nodeValue);if(c=a.getAttributeNode("pansize"))this.tb=Number(c.nodeValue);if(c=a.getAttributeNode("tiltsize"))this.Hf=Number(c.nodeValue);for(a=a.firstChild;a;)"source"==
a.nodeName&&(c=a.getAttributeNode("url"))&&this.url.push(c.nodeValue.toString()),a=a.nextSibling};return b}(f);p.Xm=h;h=function(d){function b(a){a=d.call(this,a)||this;a.poster="";a.wa=0;a.Ea=0;a.mb=0;a.f=50;a.Ua=0;a.qg=!1;a.ld=!1;return a}__extends(b,d);b.prototype.re=function(){1!=this.Ua&&4!=this.Ua||this.mg(!this.gb);2==this.Ua&&this.g.Rl(this.id)};b.prototype.mg=function(a){var c=this.g,b=c.pa;if(1==this.Ua||4==this.Ua)if(this.gb=a,this.g.Fb)(c=c.ia)&&c.activateSound(this.id,this.gb?1:0);else{if(this.gb)this.b.style.pointerEvents=
"auto",this.b.style.cursor="pointer",this.b.style.zIndex=(c.ih+8E4).toString(),this.b.style[this.g.bd]="all 1s ease 0s",this.g.Xb(this.id)||c.Ae(this.id);else{this.b.style.pointerEvents="none";this.b.style.cursor="default";this.b.style.zIndex=c.ih.toString();this.b.style[this.g.bd]="all 1s ease 0s";this.g.Xb(this.id)&&c.pi(this.id);this.Aj=!0;var d=this;setTimeout(function(){d.Aj=!1},1E3)}if(b&&(2==this.mode||3==this.mode||5==this.mode)&&(b=b.currentTime,this.oc&&this.Wb&&this.Ub.gain&&this.Ub&&this.Vb)){var e=
this.oc.gain.value,m=this.Wb.gain.value,t=this.Ub.gain.value,g=this.Vb.gain.value;this.gb?(this.oc.gain.linearRampToValueAtTime(e,b),this.oc.gain.linearRampToValueAtTime(this.level*c.V,b+1),this.Wb.gain.linearRampToValueAtTime(m,b),this.Wb.gain.linearRampToValueAtTime(this.level*c.V,b+1),this.Ub.gain.linearRampToValueAtTime(t,b),this.Ub.gain.linearRampToValueAtTime(0,b+1),this.Vb.gain.linearRampToValueAtTime(g,b),this.Vb.gain.linearRampToValueAtTime(0,b+1)):(this.oc.gain.linearRampToValueAtTime(e,
b),this.oc.gain.linearRampToValueAtTime(this.Tc,b+1),this.Wb.gain.linearRampToValueAtTime(m,b),this.Wb.gain.linearRampToValueAtTime(this.Uc,b+1),this.Ub.gain.linearRampToValueAtTime(t,b),this.Ub.gain.linearRampToValueAtTime(this.Cc,b+1),this.Vb.gain.linearRampToValueAtTime(g,b),this.Vb.gain.linearRampToValueAtTime(this.Dc,b+1))}this.lg=!0;this.g.Em()}2==this.Ua&&(a?this.g.Ae(this.id):this.g.zj(this.id))};b.prototype.ng=function(){this.lg=!1;this.b.style[this.g.bd]="none"};b.prototype.uq=function(){0==
this.loop?this.b.play():0<this.Zc?(this.Zc--,this.b.currentTime=0,this.b.play()):this.vl=!1};b.prototype.Nb=function(a){d.prototype.Nb.call(this,a);var c;if(c=a.getAttributeNode("poster"))this.poster=String(c.nodeValue);if(c=a.getAttributeNode("rotx"))this.wa=Number(c.nodeValue);if(c=a.getAttributeNode("roty"))this.Ea=Number(c.nodeValue);if(c=a.getAttributeNode("rotz"))this.mb=Number(c.nodeValue);if(c=a.getAttributeNode("fov"))this.f=Number(c.nodeValue);if(c=a.getAttributeNode("width"))this.Hc=Number(c.nodeValue);
if(c=a.getAttributeNode("height"))this.ed=Number(c.nodeValue);this.xd=(c=a.getAttributeNode("stretch"))?Number(c.nodeValue):1;if(c=a.getAttributeNode("clickmode"))this.Ua=Number(c.nodeValue);if(c=a.getAttributeNode("handcursor"))this.qg=1==Number(c.nodeValue);if(c=a.getAttributeNode("startmutedmobile"))this.pm=1==Number(c.nodeValue)};b.prototype.addElement=function(){var a=this,c=this.g,b=this.g.pa;try{a.b=document.createElement("video");a.b.setAttribute("class","ggmedia");a.b.crossOrigin=c.crossOrigin;
a.b.hidden=!0;a.b.addEventListener("click",function(a){a.stopPropagation()});c.ff&&a.b.setAttribute("id",c.ff+a.id);if(c.dh)a.b.setAttribute("playsinline","playsinline"),a.b.setAttribute("style","display: none; max-width:none;");else if(a.b.setAttribute("style","max-width:none;pointer-events:none;"),a.b.setAttribute("playsinline","playsinline"),1==a.Ua||4==a.Ua)a.b.addEventListener(c.Mm(),function(){a.ng()},!1),a.b.addEventListener("transitionend",function(){a.ng()},!1);var d=void 0;for(d=0;d<a.url.length;d++){var e=
void 0;e=document.createElement("source");e.crossOrigin=c.crossOrigin;e.setAttribute("src",c.nc(a.url[d]));a.b.appendChild(e)}""!=a.poster&&(a.b.poster=c.nc(a.poster),0>a.loop&&(a.b.preload="none"));a.b.volume=a.level*c.V;1<=a.loop&&(a.Zc=a.loop-1);b&&(this.Le=!0);(1==a.mode||2==a.mode||3==a.mode||5==a.mode)&&0<=a.loop&&(a.b.autoplay=!0,a.vl=!0,a.autoplay=!0,a.Wj());c.I.push(this);c.dh?c.T.appendChild(a.b):(a.b.style.position="absolute",a.Hc&&(a.b.width=a.Hc),a.ed&&(a.b.height=a.ed),c.D.appendChild(a.b));
a.b.addEventListener("ended",function(){a.uq()},!1)}catch(m){c.M(m)}};b.prototype.registerElement=function(a,c){this.ld=!0;this.b=c;this.id=a;this.level=1;this.g.I.push(this)};b.prototype.Be=function(){var a=this.g;a.dh&&(a.H.deleteTexture(this.sc),this.sc=0,a.T.removeChild(this.b));a.Fm&&a.D.removeChild(this.b);this.b=null};return b}(h);p.ik=h;h=function(d){function b(a){a=d.call(this,a)||this;a.url="";a.wa=0;a.Ea=0;a.mb=0;a.f=50;a.Ua=0;a.qg=!1;a.Hc=100;a.ed=100;a.xd=1;return a}__extends(b,d);b.prototype.Nb=
function(a){d.prototype.Nb.call(this,a);var c;if(c=a.getAttributeNode("url"))this.url=c.nodeValue.toString();if(c=a.getAttributeNode("rotx"))this.wa=Number(c.nodeValue);if(c=a.getAttributeNode("roty"))this.Ea=Number(c.nodeValue);if(c=a.getAttributeNode("rotz"))this.mb=Number(c.nodeValue);if(c=a.getAttributeNode("fov"))this.f=Number(c.nodeValue);if(c=a.getAttributeNode("width"))this.Hc=Number(c.nodeValue);if(c=a.getAttributeNode("height"))this.ed=Number(c.nodeValue);this.xd=(c=a.getAttributeNode("stretch"))?
Number(c.nodeValue):1;if(c=a.getAttributeNode("clickmode"))this.Ua=Number(c.nodeValue);if(c=a.getAttributeNode("handcursor"))this.qg=1==Number(c.nodeValue);for(a=a.firstChild;a;)"source"==a.nodeName&&(c=a.getAttributeNode("url"))&&(this.url=c.nodeValue.toString()),a=a.nextSibling};b.prototype.ng=function(){this.lg=!1;this.b.style[this.g.bd]="none"};b.prototype.re=function(){1!==this.Ua&&4!==this.Ua||this.mg(!this.gb)};b.prototype.mg=function(a){var c=this.g;if(1===this.Ua||4===this.Ua)this.gb=a,this.g.Fb?
(a=this.g.ia)&&a.activateSound(this.id,this.gb?1:0):(this.gb?(this.b.style.pointerEvents="auto",this.b.style.cursor="pointer",this.b.style.zIndex=(c.ih+8E4).toString()):(this.b.style.pointerEvents="none",this.b.style.cursor="default",this.b.style.zIndex=c.ih.toString()),this.b.style[c.bd]="all 1s ease 0s",this.lg=!0,c.zm())};b.prototype.addElement=function(){var a=this,c=this.g;try{a.b=document.createElement("img");a.b.setAttribute("style","-webkit-user-drag:none; max-width:none; pointer-events:none;");
a.b.setAttribute("class","ggmedia");a.b.hidden=!0;a.b.addEventListener("click",function(a){a.stopPropagation()});c.ff&&a.b.setAttribute("id",c.ff+a.id);a.b.ondragstart=function(){return!1};if(1===a.Ua||4===a.Ua)a.b.addEventListener(c.Mm(),function(){a.ng()},!1),a.b.addEventListener("transitionend",function(){a.ng()},!1);a.b.setAttribute("src",c.nc(a.url));a.Hc&&(a.b.width=a.Hc);a.ed&&(a.b.height=a.ed);c.Sa.push(a);a.b.style.position="absolute";c.D.appendChild(a.b)}catch(k){c.M("Error addimage:"+k)}};
b.prototype.Be=function(){this.g.D.removeChild(this.b);this.b=null};return b}(f);p.Vm=h;f=function(d){function b(a){a=d.call(this,a)||this;a.pk=50;a.alpha=50;a.type=0;a.color=16777215;return a}__extends(b,d);b.prototype.Nb=function(a){d.prototype.Nb.call(this,a);var c;if(c=a.getAttributeNode("blinding"))this.pk=Number(c.nodeValue);if(c=a.getAttributeNode("alpha"))this.alpha=Number(c.nodeValue);if(c=a.getAttributeNode("type"))this.type=Number(c.nodeValue);if(c=a.getAttributeNode("color"))this.color=
Number(c.nodeValue)};return b}(f);p.Wm=f;f=function(){function d(b){this.type="empty";this.Nj=this.id=this.target=this.description=this.title=this.url="";this.hh=100;this.pg=20;this.Bi=!1;this.b=null;this.vb=this.Mb=this.ab=this.qa=this.i=this.pan=0;this.visible=!0;this.kc=b.A.kc;this.hc=b.A.hc;this.jc=b.A.jc;this.gc=b.A.gc;this.cf=b.A.cf;this.Nf=[]}d.prototype.Ye=function(){this.id=this.id;this.pan=this.pan;this.tilt=this.i;this.url=this.url;this.target=this.target;this.title=this.title;this.description=
this.description;this.skinid=this.Nj;this.obj=this.b};d.prototype.Nb=function(b){var a;if(a=b.getAttributeNode("url"))this.url=a.nodeValue.toString();if(a=b.getAttributeNode("target"))this.target=a.nodeValue.toString();if(a=b.getAttributeNode("title"))this.title=a.nodeValue.toString();if(a=b.getAttributeNode("description"))this.description=a.nodeValue.toString();if(a=b.getAttributeNode("id"))this.id=a.nodeValue.toString();if(a=b.getAttributeNode("skinid"))this.Nj=a.nodeValue.toString();if(a=b.getAttributeNode("width"))this.hh=
Number(a.nodeValue);if(a=b.getAttributeNode("height"))this.pg=Number(a.nodeValue);if(a=b.getAttributeNode("wordwrap"))this.Bi=1==Number(a.nodeValue);this.pan=(a=b.getAttributeNode("pan"))?Number(a.nodeValue):0;this.i=(a=b.getAttributeNode("tilt"))?Number(a.nodeValue):0;if(a=b.getAttributeNode("bordercolor"))this.kc=Number(a.nodeValue);if(a=b.getAttributeNode("backgroundcolor"))this.hc=Number(a.nodeValue);if(a=b.getAttributeNode("borderalpha"))this.jc=Number(a.nodeValue);if(a=b.getAttributeNode("backgroundalpha"))this.gc=
Number(a.nodeValue);if(a=b.getAttributeNode("handcursor"))this.cf=1==Number(a.nodeValue);for(b=b.firstChild;b;){if("polystring"==b.nodeName){a=b.textContent.toString().split("|");for(var c=0;c<a.length;c++){var d=a[c].split("/");if(2==d.length){var l={pan:0,i:0};l.pan=Number(d[0]);l.i=Number(d[1]);this.Nf.push(l)}}}"vertex"==b.nodeName&&(l={pan:0,i:0},a=b.getAttributeNode("pan"),l.pan=a?Number(a.nodeValue):0,a=b.getAttributeNode("tilt"),l.i=a?Number(a.nodeValue):0,this.Nf.push(l));b=b.nextSibling}this.Ye()};
return d}();p.lh=f})(ggP2VR||(ggP2VR={}));
(function(p){var f=function(){function f(d,b){this.x=d;this.y=b}f.prototype.Za=function(d,b){this.x=d;this.y=b};f.prototype.zd=function(d,b,a){var c=b.y-d.y;this.x=d.x+(b.x-d.x)*a;this.y=d.y+c*a};f.prototype.hn=function(d,b,a,c,k){var l=new f;l.zd(d,a,k);d=new f;d.zd(a,c,k);a=new f;a.zd(c,b,k);b=new f;b.zd(l,d,k);l=new f;l.zd(d,a,k);d=new f;d.zd(b,l,k);this.x=d.x;this.y=d.y};f.prototype.Hi=function(d,b,a,c,k){var l=new f,e=.5,m=.25;do{l.hn(d,b,a,c,e);var t=l.x-k;e=0<t?e-m:e+m;m/=2}while(.01<Math.abs(t));
this.x=l.x;this.y=l.y};return f}();p.vc=f})(ggP2VR||(ggP2VR={}));
(function(p){var f=function(){function f(d,b,a,c,k){this.x=d;this.y=b;this.z=a;this.sd=c;this.Qb=k}f.prototype.Za=function(d,b,a){this.x=d;this.y=b;this.z=a;this.Qb=this.sd=void 0};f.prototype.toString=function(){return"("+this.x+","+this.y+","+this.z+") - ("+this.sd+","+this.Qb+")"};f.prototype.wa=function(d){var b=Math.sin(d);d=Math.cos(d);var a=this.y,c=this.z;this.y=d*a-b*c;this.z=b*a+d*c};f.prototype.qp=function(){var d=this.y;this.y=-this.z;this.z=d};f.prototype.pp=function(){var d=this.y;this.y=
this.z;this.z=-d};f.prototype.Ea=function(d){var b=Math.sin(d);d=Math.cos(d);var a=this.x,c=this.z;this.x=d*a+b*c;this.z=-b*a+d*c};f.prototype.rp=function(){var d=this.x;this.x=-this.z;this.z=d};f.prototype.mb=function(d){var b=Math.sin(d);d=Math.cos(d);var a=this.x,c=this.y;this.x=d*a-b*c;this.y=b*a+d*c};f.prototype.cm=function(){var d=this.x;this.x=-this.y;this.y=d};f.prototype.Fd=function(d){this.wa(d*Math.PI/180)};f.prototype.Ce=function(d){this.Ea(d*Math.PI/180)};f.prototype.De=function(d){this.mb(d*
Math.PI/180)};f.prototype.clone=function(){return new f(this.x,this.y,this.z,this.sd,this.Qb)};f.prototype.length=function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)};f.prototype.normalize=function(){var d=this.length();0<d&&(d=1/d,this.x*=d,this.y*=d,this.z*=d)};f.prototype.ci=function(d){return this.x*d.x+this.y*d.y+this.z*d.z};f.prototype.Kk=function(d,b){var a=Math.cos(b*Math.PI/180);this.x=a*Math.sin(d*Math.PI/180);this.y=Math.sin(b*Math.PI/180);this.z=a*Math.cos(d*Math.PI/
180)};f.prototype.dn=function(){return 180*Math.atan2(-this.x,-this.z)/Math.PI};f.prototype.en=function(){return 180*Math.asin(this.y/this.length())/Math.PI};f.prototype.zd=function(d,b,a){this.x=d.x*a+b.x*(1-a);this.y=d.y*a+b.y*(1-a);this.z=d.z*a+b.z*(1-a);this.sd=d.sd*a+b.sd*(1-a);this.Qb=d.Qb*a+b.Qb*(1-a)};return f}();p.xa=f})(ggP2VR||(ggP2VR={}));
(function(p){var f=function(){function f(){this.gm()}f.prototype.gm=function(){this.Yb=1;this.pc=this.Zb=this.Db=0;this.$b=1;this.ac=this.Jb=this.qc=0;this.Kb=1};f.prototype.clone=function(d){this.Yb=d.Yb;this.Db=d.Db;this.Zb=d.Zb;this.pc=d.pc;this.$b=d.$b;this.qc=d.qc;this.Jb=d.Jb;this.ac=d.ac;this.Kb=d.Kb};f.prototype.Fp=function(d){var b=Math.cos(d);d=Math.sin(d);this.Yb=1;this.pc=this.Zb=this.Db=0;this.$b=b;this.qc=-d;this.Jb=0;this.ac=d;this.Kb=b};f.prototype.Gp=function(d){var b=Math.cos(d);
d=Math.sin(d);this.Yb=b;this.Db=0;this.Zb=d;this.pc=0;this.$b=1;this.qc=0;this.Jb=-d;this.ac=0;this.Kb=b};f.prototype.Hp=function(d){var b=Math.cos(d);d=Math.sin(d);this.Yb=b;this.Db=-d;this.Zb=0;this.pc=d;this.$b=b;this.ac=this.Jb=this.qc=0;this.Kb=1};f.prototype.Cp=function(d){this.Fp(d*Math.PI/180)};f.prototype.Dp=function(d){this.Gp(d*Math.PI/180)};f.prototype.Ep=function(d){this.Hp(d*Math.PI/180)};f.prototype.Fd=function(d){this.Mc||(this.Mc=new f,this.Td=new f);this.Mc.Cp(d);this.Td.clone(this);
this.multiply(this.Mc,this.Td)};f.prototype.Ce=function(d){this.Mc||(this.Mc=new f,this.Td=new f);this.Mc.Dp(d);this.Td.clone(this);this.multiply(this.Mc,this.Td)};f.prototype.De=function(d){this.Mc||(this.Mc=new f,this.Td=new f);this.Mc.Ep(d);this.Td.clone(this);this.multiply(this.Mc,this.Td)};f.prototype.multiply=function(d,b){this.Yb=d.Yb*b.Yb+d.Db*b.pc+d.Zb*b.Jb;this.Db=d.Yb*b.Db+d.Db*b.$b+d.Zb*b.ac;this.Zb=d.Yb*b.Zb+d.Db*b.qc+d.Zb*b.Kb;this.pc=d.pc*b.Yb+d.$b*b.pc+d.qc*b.Jb;this.$b=d.pc*b.Db+
d.$b*b.$b+d.qc*b.ac;this.qc=d.pc*b.Zb+d.$b*b.qc+d.qc*b.Kb;this.Jb=d.Jb*b.Yb+d.ac*b.pc+d.Kb*b.Jb;this.ac=d.Jb*b.Db+d.ac*b.$b+d.Kb*b.ac;this.Kb=d.Jb*b.Zb+d.ac*b.qc+d.Kb*b.Kb};f.prototype.Ro=function(d){var b=d.x;var a=d.y;var c=d.z;d.x=b*this.Yb+a*this.Db+c*this.Zb;d.y=b*this.pc+a*this.$b+c*this.qc;d.z=b*this.Jb+a*this.ac+c*this.Kb};return f}();p.fk=f})(ggP2VR||(ggP2VR={}));
(function(p){p.aa={create:function(f){var h;"undefined"!=typeof Float32Array?h=new Float32Array(16):h=Array(16);f&&(h[0]=f[0],h[1]=f[1],h[2]=f[2],h[3]=f[3],h[4]=f[4],h[5]=f[5],h[6]=f[6],h[7]=f[7],h[8]=f[8],h[9]=f[9],h[10]=f[10],h[11]=f[11],h[12]=f[12],h[13]=f[13],h[14]=f[14],h[15]=f[15]);return h},set:function(f,h){h[0]=f[0];h[1]=f[1];h[2]=f[2];h[3]=f[3];h[4]=f[4];h[5]=f[5];h[6]=f[6];h[7]=f[7];h[8]=f[8];h[9]=f[9];h[10]=f[10];h[11]=f[11];h[12]=f[12];h[13]=f[13];h[14]=f[14];h[15]=f[15];return h},te:function(f){f[0]=
1;f[1]=0;f[2]=0;f[3]=0;f[4]=0;f[5]=1;f[6]=0;f[7]=0;f[8]=0;f[9]=0;f[10]=1;f[11]=0;f[12]=0;f[13]=0;f[14]=0;f[15]=1;return f},multiply:function(f,h,d){d||(d=f);var b=f[0],a=f[1],c=f[2],k=f[3],l=f[4],e=f[5],m=f[6],t=f[7],g=f[8],q=f[9],n=f[10],p=f[11],r=f[12],v=f[13],u=f[14];f=f[15];var x=h[0],A=h[1],w=h[2],B=h[3],z=h[4],C=h[5],D=h[6],E=h[7],F=h[8],H=h[9],I=h[10],J=h[11],K=h[12],L=h[13],M=h[14];h=h[15];d[0]=x*b+A*l+w*g+B*r;d[1]=x*a+A*e+w*q+B*v;d[2]=x*c+A*m+w*n+B*u;d[3]=x*k+A*t+w*p+B*f;d[4]=z*b+C*l+D*g+
E*r;d[5]=z*a+C*e+D*q+E*v;d[6]=z*c+C*m+D*n+E*u;d[7]=z*k+C*t+D*p+E*f;d[8]=F*b+H*l+I*g+J*r;d[9]=F*a+H*e+I*q+J*v;d[10]=F*c+H*m+I*n+J*u;d[11]=F*k+H*t+I*p+J*f;d[12]=K*b+L*l+M*g+h*r;d[13]=K*a+L*e+M*q+h*v;d[14]=K*c+L*m+M*n+h*u;d[15]=K*k+L*t+M*p+h*f;return d},translate:function(f,h,d){var b=h[0],a=h[1];h=h[2];if(!d||f==d)return f[12]=f[0]*b+f[4]*a+f[8]*h+f[12],f[13]=f[1]*b+f[5]*a+f[9]*h+f[13],f[14]=f[2]*b+f[6]*a+f[10]*h+f[14],f[15]=f[3]*b+f[7]*a+f[11]*h+f[15],f;var c=f[0],k=f[1],l=f[2],e=f[3],m=f[4],t=f[5],
g=f[6],q=f[7],n=f[8],p=f[9],r=f[10],v=f[11];d[0]=c;d[1]=k;d[2]=l;d[3]=e;d[4]=m;d[5]=t;d[6]=g;d[7]=q;d[8]=n;d[9]=p;d[10]=r;d[11]=v;d[12]=c*b+m*a+n*h+f[12];d[13]=k*b+t*a+p*h+f[13];d[14]=l*b+g*a+r*h+f[14];d[15]=e*b+q*a+v*h+f[15];return d},scale:function(f,h,d){var b=h[0],a=h[1];h=h[2];if(!d||f==d)return f[0]*=b,f[1]*=b,f[2]*=b,f[3]*=b,f[4]*=a,f[5]*=a,f[6]*=a,f[7]*=a,f[8]*=h,f[9]*=h,f[10]*=h,f[11]*=h,f;d[0]=f[0]*b;d[1]=f[1]*b;d[2]=f[2]*b;d[3]=f[3]*b;d[4]=f[4]*a;d[5]=f[5]*a;d[6]=f[6]*a;d[7]=f[7]*a;d[8]=
f[8]*h;d[9]=f[9]*h;d[10]=f[10]*h;d[11]=f[11]*h;d[12]=f[12];d[13]=f[13];d[14]=f[14];d[15]=f[15];return d},rotate:function(f,h,d,b){var a=d[0],c=d[1];d=d[2];var k=Math.sqrt(a*a+c*c+d*d);if(!k)return null;1!=k&&(k=1/k,a*=k,c*=k,d*=k);var l=Math.sin(h),e=Math.cos(h),m=1-e;h=f[0];k=f[1];var t=f[2],g=f[3],q=f[4],n=f[5],p=f[6],r=f[7],v=f[8],u=f[9],x=f[10],A=f[11],w=a*a*m+e,B=c*a*m+d*l,z=d*a*m-c*l,C=a*c*m-d*l,D=c*c*m+e,E=d*c*m+a*l,F=a*d*m+c*l;a=c*d*m-a*l;c=d*d*m+e;b?f!=b&&(b[12]=f[12],b[13]=f[13],b[14]=f[14],
b[15]=f[15]):b=f;b[0]=h*w+q*B+v*z;b[1]=k*w+n*B+u*z;b[2]=t*w+p*B+x*z;b[3]=g*w+r*B+A*z;b[4]=h*C+q*D+v*E;b[5]=k*C+n*D+u*E;b[6]=t*C+p*D+x*E;b[7]=g*C+r*D+A*E;b[8]=h*F+q*a+v*c;b[9]=k*F+n*a+u*c;b[10]=t*F+p*a+x*c;b[11]=g*F+r*a+A*c;return b},Hn:function(f,h,d,b,a,c,k){k||(k=p.aa.create());var l=h-f,e=b-d,m=c-a;k[0]=2*a/l;k[1]=0;k[2]=0;k[3]=0;k[4]=0;k[5]=2*a/e;k[6]=0;k[7]=0;k[8]=(h+f)/l;k[9]=(b+d)/e;k[10]=-(c+a)/m;k[11]=-1;k[12]=0;k[13]=0;k[14]=-(c*a*2)/m;k[15]=0;return k},perspective:function(f,h,d,b,a){f=
d*Math.tan(f*Math.PI/360);h=f*h;return p.aa.Hn(-h,h,-f,f,d,b,a)},Gq:function(f,h,d,b,a,c,k){k||(k=p.aa.create());var l=h-f,e=b-d,m=c-a;k[0]=2/l;k[1]=0;k[2]=0;k[3]=0;k[4]=0;k[5]=2/e;k[6]=0;k[7]=0;k[8]=0;k[9]=0;k[10]=-2/m;k[11]=0;k[12]=-(f+h)/l;k[13]=-(b+d)/e;k[14]=-(c+a)/m;k[15]=1;return k}}})(ggP2VR||(ggP2VR={}));
(function(p){var f=function(){function f(d){this.ma=p.aa.create();this.sb=p.aa.create();this.fd=0;this.Va=[];this.ug=!1;this.Rj=this.cj=this.Ej=1;this.Ve=1E6;this.sh=[!1,!1,!1,!1,!1,!1];this.oi=!1;this.Xi=[];this.gk=8;this.Ko=new p.fk;this.Pd=[];this.g=d;if(d.Sd||d.sg)d.Vg=2}f.prototype.rg=function(){var d=this.g.H;if(d){var b=d.createShader(d.FRAGMENT_SHADER);d.shaderSource(b,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\n\t\t\t\t\tuniform sampler2D uSampler;\n\t\t\t\t\tvoid main(void) {\n\t\t\t\t\t\tgl_FragColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n\t\t\t\t\t}");
d.compileShader(b);d.getShaderParameter(b,d.COMPILE_STATUS)||(console&&console.log(d.getShaderInfoLog(b)),alert(d.getShaderInfoLog(b)),b=null);var a=d.createShader(d.VERTEX_SHADER);this.Gc(a,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nattribute vec3 aVertexPosition;\n\t\t\t\tattribute vec2 aTextureCoord;\n\t\t\t\tuniform mat4 uMVMatrix;\n\t\t\t\tuniform mat4 uPMatrix;\n\t\t\t\tuniform float uZoffset;\n\t\t\t\tvarying vec2 vTextureCoord;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tgl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n\t\t\t\t\tgl_Position.z += uZoffset;\n\t\t\t\t\tvTextureCoord = aTextureCoord;\n\t\t\t\t}");
this.F=d.createProgram();this.kf(this.F,a,b);this.F.$=d.getAttribLocation(this.F,"aVertexPosition");d.enableVertexAttribArray(this.F.$);this.F.Ca=d.getAttribLocation(this.F,"aTextureCoord");d.enableVertexAttribArray(this.F.Ca);this.F.Vd=d.getUniformLocation(this.F,"uPMatrix");this.F.Ig=d.getUniformLocation(this.F,"uMVMatrix");this.F.wf=d.getUniformLocation(this.F,"uSampler");this.F.Ci=d.getUniformLocation(this.F,"uZoffset");b=d.createShader(d.VERTEX_SHADER);this.Gc(b,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nattribute vec3 aVertexPosition;\n\t\t\t\tuniform vec2 uCanvasDimensions;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tvec2 pointNorm = (aVertexPosition.xy / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n\t\t\t\t\tgl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, 0.0, 1.0);\n\t\t\t\t}");
a=d.createShader(d.FRAGMENT_SHADER);this.Gc(a,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nuniform vec3 uColor;\n\t\t\t\tuniform float uAlpha;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tgl_FragColor = vec4(uColor, uAlpha);\n\t\t\t\t}");this.Dd=d.createProgram();this.kf(this.Dd,b,a);this.Dd.$=d.getAttribLocation(this.Dd,"aVertexPosition");d.enableVertexAttribArray(this.Dd.$);a=d.createShader(d.VERTEX_SHADER);this.Gc(a,"precision highp float;\n\t\t\t\tattribute vec3 aVertexPosition;\n\t\t\t\tvarying vec2 vTextureCoord;\n\t\t\t\tuniform vec2 uCanvasDimensions;\n\t\t\t\tuniform vec4 uRect;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tvec2 pos = vec2(uRect.x + uRect.z*aVertexPosition.x,uRect.y + uRect.w*aVertexPosition.y);\n\t\t\t\t\tvec2 pointNorm = (pos / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n\t\t\t\t\tgl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, 1.0, 1.0);\n\t\t\t\t\tvTextureCoord.s=aVertexPosition.x;\n\t\t\t\t\tvTextureCoord.t=1.0-aVertexPosition.y;\n\t\t\t\t}");
b=d.createShader(d.FRAGMENT_SHADER);this.Gc(b,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\n\t\t\t\tuniform sampler2D uSampler;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tgl_FragColor = texture2D(uSampler,vTextureCoord);\n\t\t\t\t}");this.dg=d.createProgram();this.kf(this.dg,a,b)}};f.prototype.Gh=function(){var d=this.g,b=d.H;if(b){var a=b.createShader(b.FRAGMENT_SHADER);var c=this.hj(13);this.Gc(a,c);c=b.createShader(b.VERTEX_SHADER);
this.Gc(c,"precision highp float;\nattribute vec3 aVertexPosition;\nuniform vec2 uCanvasDimensions;\nvarying vec2 dst;\nuniform vec2 dstSize;\nuniform float zOffset;\nvoid main(void) {\n vec2 pointNorm = (aVertexPosition.xy / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, zOffset, 1.0);\n dst.x= -1.0 + 2.0*((aVertexPosition.x + 0.5) / uCanvasDimensions.x);\n dst.y= (-1.0 * uCanvasDimensions.y + 2.0*(aVertexPosition.y + 0.5)) / uCanvasDimensions.x;\n}\n");
this.Xl=b.createProgram();this.kf(this.Xl,c,a);a=b.createShader(b.FRAGMENT_SHADER);c=this.hj(4);this.Gc(a,c);c=b.createShader(b.VERTEX_SHADER);this.Gc(c,"precision highp float;\nattribute vec3 aVertexPosition;\nuniform vec2 uCanvasDimensions;\nvarying vec2 dst;\nuniform vec2 dstSize;\nuniform float zOffset;\nvoid main(void) {\n vec2 pointNorm = (aVertexPosition.xy / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, zOffset, 1.0);\n dst.x= -1.0 + 2.0*((aVertexPosition.x + 0.5) / uCanvasDimensions.x);\n dst.y= (-1.0 * uCanvasDimensions.y + 2.0*(aVertexPosition.y + 0.5)) / uCanvasDimensions.x;\n}\n");
this.Yl=b.createProgram();this.kf(this.Yl,c,a);a=b.createShader(b.FRAGMENT_SHADER);c=this.hj(d.s.format);this.Gc(a,c);c=b.createShader(b.VERTEX_SHADER);this.Gc(c,"precision highp float;\nattribute vec3 aVertexPosition;\nuniform vec2 uCanvasDimensions;\nvarying vec2 dst;\nuniform vec2 dstSize;\nvoid main(void) {\n vec2 pointNorm = (aVertexPosition.xy / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, 0.0, 1.0);\n dst.x= -1.0 + 2.0*((aVertexPosition.x + 0.5) / uCanvasDimensions.x);\n dst.y= (-1.0 * uCanvasDimensions.y + 2.0*(aVertexPosition.y + 0.5)) / uCanvasDimensions.x;\n}\n");
this.Zl=b.createProgram();this.kf(this.Zl,c,a);this.ei||(this.ei=b.createBuffer())}else this.g.M("No WebGL to initRemapShader!")};f.prototype.Gc=function(d,b){var a=this.g.H;a.shaderSource(d,b);a.compileShader(d);a.getShaderParameter(d,a.COMPILE_STATUS)||(console&&console.log(a.getShaderInfoLog(d)),W&&alert(a.getShaderInfoLog(d)))};f.prototype.kf=function(d,b,a){var c=this.g.H;c.attachShader(d,b);c.attachShader(d,a);c.linkProgram(d);c.getProgramParameter(d,c.LINK_STATUS)||(alert("Could not initialise shader program"),
console&&console.log(c.getError()));c.useProgram(d)};f.prototype.hj=function(d){var b=this.g;var a="#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\n#define M_PI 3.14159265358979323846\nvarying vec2 dst;\nuniform vec2 srcScale;\nuniform vec2 srcOffset;\nuniform float rectDstDistance;\nuniform float fisheyeDistance;\nuniform float stereoDistance;\nuniform float directionBlend;\nuniform mat4 matRotate; // = mat4( 1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0 );\nconst float rectSrcDistance = 1.0;\nuniform vec2 tonemap;\n";
a=(13==d?a+"uniform samplerCube cubeTexture;":a+"uniform sampler2D tileTexture;\n")+"void main()\n{\n";a+="vec4 direction;\n";a+="vec2 src;\n";a+="vec2 srcCord;\n";a+="vec2 texc;\n";var c=this.cl(b.sa());b.sa()!=b.rc&&0!=b.rc?(b=this.cl(b.rc),a+="vec4 direction1,direction2;\n",a+=c.replace("direction=","direction1="),a+=b.replace("direction=","direction2="),a+="direction=normalize(mix(direction1, direction2,1.0-directionBlend));\n"):a+=c;a+="direction=direction*matRotate;\n";13==d&&(a+="direction.z=-direction.z;",
a+="gl_FragColor = textureCube(cubeTexture, direction.xyz);");4==d&&(a+="float iz=1.0/(direction.z * rectSrcDistance);\n",a+="src.x=-direction.x*iz;\n",a+="src.y= direction.y*iz;\n",a+="texc=src * srcScale + srcOffset;\n",a+="if (",a+="(direction.z<0.0) && ",a+="(texc.x>=0.0) && (texc.x<=1.0) && (texc.y>=0.0) && (texc.y<=1.0)) {\n",a+="  gl_FragColor = texture2D(tileTexture, texc);\n",a+="} else {\n",a+="  discard;\n",a+="}\n");1==d&&(a+="src.x=atan(float(-direction.x), float(-direction.z));",a+=
"src.y=asin(direction.y);\n",a+="texc=src * srcScale + srcOffset;\n",a+="gl_FragColor = texture2D(tileTexture, texc);\n");14==d&&(a+="vec2 cf;\n",a+="if ((direction.z<0.0) && (direction.z<=-abs(direction.x)) && (direction.z<=-abs(direction.y))) {\n",a+="  src.x=-direction.x/direction.z;\n",a+="  src.y=+direction.y/direction.z;\n",a+="  cf.x=1.0;cf.y=3.0;\n",a+="}\n",a+="if ((direction.x>=0.0) && (direction.x>=abs(direction.y)) && (direction.x>=abs(direction.z))) {\n",a+="  src.x=+direction.z/direction.x;\n",
a+="  src.y=-direction.y/direction.x;\n",a+="  cf.x=3.0;cf.y=3.0;\n",a+="}\n",a+="if ((direction.z>=0.0) && (direction.z>=abs(direction.x)) && (direction.z>=abs(direction.y))) {\n",a+="  src.x=-direction.x/direction.z;\n",a+="  src.y=-direction.y/direction.z;\n",a+="  cf.x=5.0;cf.y=3.0;\n",a+="}\n",a+="if ((direction.x<=0.0) && (direction.x<=-abs(direction.y)) && (direction.x<=-abs(direction.z))) {\n",a+="  src.x=+direction.z/direction.x;\n",a+="  src.y=+direction.y/direction.x;\n",a+="  cf.x=1.0;cf.y=1.0;\n",
a+="}\n",a+="if ((direction.y>=0.0) && (direction.y>=abs(direction.x)) && (direction.y>=abs(direction.z))) {\n",a+="  src.x=+direction.x/direction.y;\n",a+="  src.y=-direction.z/direction.y;\n",a+="  cf.x=5.0;cf.y=1.0;\n",a+="}\n",a+="if ((direction.y<=0.0) && (direction.y<=-abs(direction.x)) && (direction.y<=-abs(direction.z))) {\n",a+="  src.x=-direction.x/direction.y;\n",a+="  src.y=-direction.z/direction.y;\n",a+="  cf.x=3.0;cf.y=1.0;\n",a+="}\n",a+="texc.x=(cf.x+src.x*srcScale.x) / 6.0;\n",a+=
"texc.y=(cf.y+src.y*srcScale.y) / 4.0;\n",a+="gl_FragColor = texture2D(tileTexture, texc);\n");return a+="}\n"};f.prototype.cl=function(d){var b="";switch(d){case 4:b+="direction.x=dst.x*rectDstDistance;\ndirection.y=dst.y*rectDstDistance;\ndirection.z=-1.0;\n";break;case 12:b+="float r,ph,ro;\nr=length(dst.xy)*0.5;\nro=atan(float(dst.x),float(-dst.y));\nph=r / fisheyeDistance;\ndirection.x= sin(ph) * sin(ro);\ndirection.y=-sin(ph) * cos(ro);\ndirection.z=-cos(ph);\n";break;case 9:b+="float n;\nvec2 ind;\nind=dst*stereoDistance;\nn=1.0 + ind.x*ind.x + ind.y*ind.y;\ndirection.x=2.0*ind.x/n;\ndirection.y=2.0*ind.y/n;\ndirection.z=(n-2.0)/n;\n"}return b+
"direction.w=0.0;\ndirection=normalize(direction);\n"};f.prototype.nl=function(d){var b,a,c=this.g,k=this.g.H;this.Si=k.createBuffer();k.bindBuffer(k.ARRAY_BUFFER,this.Si);var l=[-1,-1,1,1,-1,1,1,1,1,-1,1,1];for(b=0;12>b;b++)2>b%3&&(l[b]*=d);k.bufferData(k.ARRAY_BUFFER,new Float32Array(l),k.STATIC_DRAW);this.je=k.createBuffer();k.bindBuffer(k.ARRAY_BUFFER,this.je);var e=[1,0,0,0,0,1,1,1];k.bufferData(k.ARRAY_BUFFER,new Float32Array(e),k.STATIC_DRAW);this.Yc=k.createBuffer();k.bindBuffer(k.ELEMENT_ARRAY_BUFFER,
this.Yc);var m=[0,1,2,0,2,3];k.bufferData(k.ELEMENT_ARRAY_BUFFER,new Uint16Array(m),k.STATIC_DRAW);l=[];m=[];e=[];var f=new p.xa;for(d=0;6>d;d++){var g=d%3;var q=3>d?1:0;for(a=0;4>a;a++){f.x=-1;f.y=-1;f.z=1;for(b=0;b<a;b++)f.cm();e.push((0>f.x?.33:0)+.33*g,(0>f.y?0:.5)+.5*q);if(4>d)for(b=0;b<d;b++)f.rp();else 5==d?f.qp():f.pp();l.push(f.x,f.y,f.z)}b=4*d;m.push(b,1+b,2+b,b,2+b,3+b)}c.s.bk=k.createBuffer();k.bindBuffer(k.ARRAY_BUFFER,c.s.bk);k.bufferData(k.ARRAY_BUFFER,new Float32Array(l),k.STATIC_DRAW);
c.s.ri=k.createBuffer();k.bindBuffer(k.ARRAY_BUFFER,c.s.ri);k.bufferData(k.ARRAY_BUFFER,new Float32Array(e),k.STATIC_DRAW);c.s.mj=k.createBuffer();k.bindBuffer(k.ELEMENT_ARRAY_BUFFER,c.s.mj);k.bufferData(k.ELEMENT_ARRAY_BUFFER,new Uint16Array(m),k.STATIC_DRAW);this.Co=k.createBuffer();this.Bo=k.createBuffer()};f.prototype.ij=function(d){var b=this;return function(){try{if(d.Vo)return;var a=b.g.H;a.pixelStorei(a.UNPACK_FLIP_Y_WEBGL,1);var c=!1;null!=d.ve&&d.ve.complete?d.il||(a.bindTexture(a.TEXTURE_2D,
d),a.texImage2D(a.TEXTURE_2D,0,a.RGBA,a.RGBA,a.UNSIGNED_BYTE,d.ve),c=d.il=!0):null!=d.qf&&d.qf.complete&&(a.bindTexture(a.TEXTURE_2D,d),a.texImage2D(a.TEXTURE_2D,0,a.RGBA,a.RGBA,a.UNSIGNED_BYTE,d.qf),c=!0);c&&(d.loaded=!0);a.bindTexture(a.TEXTURE_2D,null);a.pixelStorei(a.UNPACK_FLIP_Y_WEBGL,0)}catch(k){b.g.M(k)}b.g.update(2)}};f.prototype.ol=function(){var d=this.g,b=d.H;if(this.Va)for(;0<this.Va.length;)b.deleteTexture(this.Va.pop());this.Va=[];for(var a=0;6>a;a++){var c=b.createTexture();this.fd++;
c.qf=null;c.ve=null;c.il=!1;b.bindTexture(b.TEXTURE_2D,c);b.texImage2D(b.TEXTURE_2D,0,b.RGB,1,1,0,b.RGB,b.UNSIGNED_BYTE,null);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_MIN_FILTER,b.LINEAR);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE);if(d.We[a]){var k=new Image;k.crossOrigin=d.crossOrigin;k.src=d.nc(d.We[a]);c.qf=k;k.addEventListener&&k.addEventListener("load",this.ij(c),!1);d.Sb.push(k)}this.Va.push(c)}for(a=0;6>a;a++)d.th[a]&&
(k=new Image,k.crossOrigin=d.crossOrigin,k.src=d.nc(d.th[a]),k.addEventListener?k.addEventListener("load",this.ij(this.Va[a]),!1):k.onload=this.ij(this.Va[a]),this.Va[a].ve=k,d.Sb.push(k));for(a=0;a<d.I.length;a++)d.I[a].ld||(d.I[a].sc=b.createTexture(),d.fd++,b.bindTexture(b.TEXTURE_2D,d.I[a].sc),b.texImage2D(b.TEXTURE_2D,0,b.RGB,1,1,0,b.RGB,b.UNSIGNED_BYTE,null),b.texParameteri(b.TEXTURE_2D,b.TEXTURE_MIN_FILTER,b.LINEAR),b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE),b.texParameteri(b.TEXTURE_2D,
b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE));d.s.sc=b.createTexture();d.fd++;b.bindTexture(b.TEXTURE_2D,d.s.sc);b.texImage2D(b.TEXTURE_2D,0,b.RGB,1,1,0,b.RGB,b.UNSIGNED_BYTE,null);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_MIN_FILTER,b.LINEAR);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE);b.bindTexture(b.TEXTURE_2D,null)};f.prototype.Aq=function(){var d=this.g;if(d.o.width!=d.D.offsetWidth||d.o.height!=d.D.offsetHeight)d.o.width=d.D.offsetWidth,
d.o.height=d.D.offsetHeight;d.ne&&(d.Ic(0),d.Rc());if(d.H){var b=d.H;this.wi();b.clear(b.DEPTH_BUFFER_BIT);b.useProgram(this.F);this.Df(0);b.uniform1i(this.F.wf,0);b.enableVertexAttribArray(this.F.$);b.enableVertexAttribArray(this.F.Ca);b.bindBuffer(b.ARRAY_BUFFER,this.je);b.vertexAttribPointer(this.F.Ca,2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.Yc);b.uniform1f(this.F.Ci,1E-4);b.vertexAttribPointer(this.F.$,3,b.FLOAT,!1,0,0);p.aa.te(this.sb);p.aa.perspective(d.Ib(),
d.rb.width/d.rb.height,.1,100,this.sb);b.uniformMatrix4fv(this.F.Vd,!1,this.sb);for(d=0;6>d;d++)this.Df(d),b.bindBuffer(b.ARRAY_BUFFER,this.Si),b.vertexAttribPointer(this.F.$,3,b.FLOAT,!1,0,0),b.bindBuffer(b.ARRAY_BUFFER,this.je),b.vertexAttribPointer(this.F.Ca,2,b.FLOAT,!1,0,0),6<=this.Va.length&&this.Va[d].loaded&&(b.activeTexture(b.TEXTURE0),b.bindTexture(b.TEXTURE_2D,this.Va[d]),b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.Yc),b.uniform1i(this.F.wf,0),b.uniformMatrix4fv(this.F.Ig,!1,this.ma),b.uniformMatrix4fv(this.F.Vd,
!1,this.sb),b.drawElements(b.TRIANGLES,6,b.UNSIGNED_SHORT,0))}};f.prototype.wi=function(){var d=this.g;if(d.h.sf&&6<d.h.sf.length){var b=parseInt(d.h.sf);d.H.clearColor((b>>16&255)/255,(b>>8&255)/255,(b>>0&255)/255,1)}};f.prototype.Df=function(d,b){void 0===b&&(b=1);var a=this.g;p.aa.te(this.ma);p.aa.rotate(this.ma,b*-a.O.c*Math.PI/180,[0,0,1]);p.aa.rotate(this.ma,b*-a.i.c*Math.PI/180,[1,0,0]);-1==b?p.aa.rotate(this.ma,-a.pan.c*Math.PI/180,[0,1,0]):p.aa.rotate(this.ma,(180-a.pan.c)*Math.PI/180,[0,
1,0]);a.$a&&(p.aa.rotate(this.ma,-a.$a.pitch*Math.PI/180,[1,0,0]),p.aa.rotate(this.ma,a.$a.O*Math.PI/180,[0,0,1]));4>d?p.aa.rotate(this.ma,-Math.PI/2*d,[0,1,0]):p.aa.rotate(this.ma,Math.PI/2*(5==d?1:-1),[1,0,0])};f.prototype.cq=function(d){var b=this;return function(){b.Xi.push(d)}};f.prototype.zn=function(d){this.g.Da=!0;this.g.$c=!0;d.loaded=!0;d.Fj=0;d.Xd=0;var b=this.g.H;this.Ck();b.pixelStorei(b.UNPACK_FLIP_Y_WEBGL,1);if(null!=d.h&&d.h.complete){d.ib=b.createTexture();this.g.fd++;b.bindTexture(b.TEXTURE_2D,
d.ib);try{b.texImage2D(b.TEXTURE_2D,0,b.RGBA,b.RGBA,b.UNSIGNED_BYTE,d.h)}catch(a){b.texImage2D(b.TEXTURE_2D,0,b.RGBA,1,1,0,b.RGBA,b.UNSIGNED_BYTE,new Uint8Array([128,128,128,250])),this.g.M(a)}}this.g.update(2)};f.prototype.Ck=function(){this.g.Pb&&this.g.Pb--};f.prototype.An=function(){if(0<this.Xi.length){var d=this.Xi.shift();this.zn(d)}};f.prototype.So=function(d){var b=this;return function(){b.g.Da=!0;b.g.$c=!0;var a=b.g.h;try{if(null!=d&&d.complete){var c=a.J[a.J.length-1],k=a.Ja;c.height=c.width=
d.width-2*k;c.L=c.fa=1;for(var l=0;6>l;l++){var e=new p.Id;e.K=document.createElement("canvas");b.g.Z?(e.K.width=c.width+2*k,e.K.height=c.height+2*k):(e.K.width=a.G+2*k,e.K.height=a.G+2*k);e.Pa=e.K.getContext("2d");e.K.style[b.g.Ra+"Origin"]="0% 0%";e.K.style.overflow="hidden";e.K.style.position="absolute";e.h=d;var m=c.width+2*k,f=c.height+2*k;e.Pa&&e.Pa.drawImage(d,0,l*f,m,f,0,0,m,f);if(b.g.Z&&b.g.H){var g=b.g.H;g.pixelStorei(g.UNPACK_FLIP_Y_WEBGL,1);e.ib=g.createTexture();b.g.fd++;g.bindTexture(g.TEXTURE_2D,
e.ib);try{g.texImage2D(g.TEXTURE_2D,0,g.RGBA,g.RGBA,g.UNSIGNED_BYTE,e.K)}catch(q){b.g.M(q)}g.bindTexture(g.TEXTURE_2D,null);g.pixelStorei(g.UNPACK_FLIP_Y_WEBGL,0)}b.g.Sc&&(e.K.Rd=-1,b.g.D.insertBefore(e.K,b.g.D.firstChild));c.U[l]=e}c.loaded=!0}}catch(q){b.g.M(q)}b.g.update(2)}};f.prototype.vm=function(d){var b=this;return function(){b.g.Da=!0;b.g.$c=!0;b.Ck();d.h=null}};f.prototype.yq=function(){var d=this.g,b=d.h,a=d.h.J;d.ne&&(d.Ic(0),d.Rc());if(d.H){var c=d.H;c.useProgram(this.F);this.wi();c.clear(c.DEPTH_BUFFER_BIT);
c.enable(c.DEPTH_TEST);p.aa.te(this.sb);p.aa.perspective(d.Ib(),d.rb.width/d.rb.height,.1,100,this.sb);c.uniformMatrix4fv(this.F.Vd,!1,this.sb);d.Bm();d.tj();var k=d.aj();var l=a.length-1;for(d.tc=0;l>=k;){var e=a[l],m=1;l==a.length-1&&0==b.Ja&&(m=b.G/(b.G-.5));for(var f=0;6>f;f++){var g=d.pb.fb[f];var q=g.mf;if(g.hb&&0<q.Mf&&0<q.gh&&0<q.scale||e.cache){g.Da=!1;g.Se[l]||(g.Se[l]={Ya:0,zb:0,Bb:0,Cb:0});var n=g.Se[l];e.cache?(n.Ya=0,n.zb=0,n.Bb=e.L-1,n.Cb=e.fa-1):d.dl(e,q,n);q=!0;for(var h=n.zb;h<=
n.Cb;h++)for(var r=n.Ya;r<=n.Bb;r++){var v=r+h*e.L+f*e.L*e.fa,u=e.U[v];u||(u=e.U[v]=new p.Id);this.wh()?u.h||(u.Xd?u.Xd--:(this.Qh(u,e,d.He(f,l,r,h)),d.Da=!0)):d.tc++;if(u.ib){if(!u.bf){v=.5*l+1;u.bf=c.createBuffer();c.bindBuffer(c.ARRAY_BUFFER,u.bf);var x=[-1,-1,1,1,-1,1,1,1,1,-1,1,1];x[3]=r*b.G-b.Ja;x[0]=Math.min((r+1)*b.G,e.width)+b.Ja;x[7]=h*b.G-b.Ja;x[1]=Math.min((h+1)*b.G,e.height)+b.Ja;x[4]=x[1];x[6]=x[3];x[9]=x[0];x[10]=x[7];for(var A=0;12>A;A++)x[A]=0==A%3?m*v*(-2*x[A]/e.width+1):1==A%3?
m*v*(-2*x[A]/e.height+1):v;c.bufferData(c.ARRAY_BUFFER,new Float32Array(x),c.STATIC_DRAW)}}else q=!1;u.visible=g.hb}n.rj=q}}l--}for(f=0;6>f;f++)if(g=d.pb.fb[f],g.hb)for(q=g.mf,this.Df(f),c.uniform1i(this.F.wf,0),c.uniformMatrix4fv(this.F.Vd,!1,this.sb),c.uniformMatrix4fv(this.F.Ig,!1,this.ma),c.enableVertexAttribArray(this.F.$),c.enableVertexAttribArray(this.F.Ca),c.bindBuffer(c.ARRAY_BUFFER,this.je),c.vertexAttribPointer(this.F.Ca,2,c.FLOAT,!1,0,0),c.activeTexture(c.TEXTURE0),c.bindBuffer(c.ELEMENT_ARRAY_BUFFER,
this.Yc),c.useProgram(this.F),l=k;l<=a.length-1;){e=a[l];if(g.hb&&0<q.Mf&&g.Se[l]&&0<=g.Se[l].Ya){n=g.Se[l];for(h=n.zb;h<=n.Cb;h++)for(r=n.Ya;r<=n.Bb;r++)v=r+h*e.L+f*e.L*e.fa,(u=e.U[v])&&u.ib&&(c.uniform1f(this.F.Ci,1E-4*(r%2+h%2*2)),c.bindBuffer(c.ARRAY_BUFFER,u.bf),c.vertexAttribPointer(this.F.$,3,c.FLOAT,!1,0,0),c.bindTexture(c.TEXTURE_2D,u.ib),c.texParameteri(c.TEXTURE_2D,c.TEXTURE_MAG_FILTER,c.LINEAR),c.texParameteri(c.TEXTURE_2D,c.TEXTURE_MIN_FILTER,c.LINEAR),c.texParameteri(c.TEXTURE_2D,c.TEXTURE_WRAP_S,
c.CLAMP_TO_EDGE),c.texParameteri(c.TEXTURE_2D,c.TEXTURE_WRAP_T,c.CLAMP_TO_EDGE),c.drawElements(c.TRIANGLES,6,c.UNSIGNED_SHORT,0)),u.visible=g.hb;n.rj&&(l=a.length)}l++}this.Gj();d.$c=!1}};f.prototype.wh=function(){return this.g.Pb<this.g.Vg};f.prototype.Qh=function(d,b,a){var c=this.g;c.lj++;d.h=new Image;d.Fj++;d.Xd=1<<d.Fj;d.h.onload=this.cq(d);d.h.onerror=this.vm(d);d.h.onabort=this.vm(d);d.h.crossOrigin=c.crossOrigin;d.h.setAttribute("src",a);c.M("load "+a);b.cache&&c.Sb.push(d.h);c.Pb++};f.prototype.Lm=
function(){var d=this.g,b=d.h;d.ne&&(d.Ic(0),d.Rc());if(d.H){var a=d.H;this.wi();W&&a.clearColor(.2,0,0,1);a.clear(a.DEPTH_BUFFER_BIT);a.disable(a.DEPTH_TEST);a.disable(a.CULL_FACE);a.bindBuffer(a.ARRAY_BUFFER,this.ei);var c=[0,0];c[2]=d.o.width;c[3]=0;c[4]=d.o.width;c[5]=d.o.height;c[6]=0;c[7]=d.o.height;a.bufferData(a.ARRAY_BUFFER,new Float32Array(c),a.STATIC_DRAW);this.g.tc=0;if(!this.xc||this.ug)0<b.J.length?this.xn():this.yn();d.s.hd?this.bp():(a.enable(a.DEPTH_TEST),a.depthRange(0,1),a.depthFunc(a.LESS),
this.oi=!1,0<b.J.length&&this.zq(),this.xc&&!this.oi&&this.ap())}};f.prototype.Pp=function(d,b,a,c,k,l,e){var m=this.g,f=m.h,g=m.o,q=a*f.G/b.width,n=(a+1)*f.G/b.width;a=c*f.G/b.height;b=(c+1)*f.G/b.height;1<n&&(k*=2,n=1);1<b&&(k*=2,b=1);k=Math.min(this.gk,k);n=(n-q)/k;var h=(b-a)/k;c=b=0;f={x:0,y:0};var r={x:0,y:0},v=0;d.Eh=0;var u=m.Jg,x=new p.xa,A=this.Ko;A.gm();4>l?A.Ce(-90*l):A.Fd(5==l?90:-90);m.$a&&(A.De(m.$a.O),A.Fd(-m.$a.pitch));A.Ce(-m.pan.c);A.Fd(m.i.c);A.De(m.O.c);for(l=0;l<=k;l++)for(var w=
0;w<=k;w++){var B=2*(q+w*n)-1;var z=2*(a+l*h)-1;x.x=1*B;x.y=1*z;x.z=-1;x.normalize();A.Ro(x);B=this.$k(x,f,m.sa());0!=m.rc&&1>u&&(B=B&&this.$k(x,r,m.rc),f.x=f.x*u+r.x*(1-u),f.y=f.y*u+r.y*(1-u));B?-1E10<f.x&&1E10>f.x&&-1E10<f.y&&1E10>f.y?-2<f.x&&2>f.x&&-2<f.y&&2>f.y&&(b+=f.x,c+=f.y,v++):f.x=NaN:f.x=NaN;d.gd[d.Eh++]=f.x;d.gd[d.Eh++]=f.y}0<v?(b/=v,c/=v):e=0;for(a=0;a<d.Eh;a+=2)f.x=d.gd[a],f.y=d.gd[a+1],m=f.x-b,q=f.y-c,f.x+=m*e,f.y+=q*e,d.gd[a]=g.width/2+f.x*g.width/2,d.gd[a+1]=g.height/2-f.y*g.width/
2;this.Qp(d,k)};f.prototype.$k=function(d,b,a){var c=!0;switch(a){case 0:case 4:a=1/(d.z*this.Ej);b.x=-d.x*a;b.y=d.y*a;0<d.z&&(c=!1);break;case 9:1==d.z&&(c=!1);a=1/((1-d.z)*this.Rj);b.x=d.x*a;b.y=-d.y*a;break;case 12:if(a=Math.sqrt(d.x*d.x+d.y*d.y),0==a)b.x=0,b.y=0;else{var k=2*this.cj*Math.acos(-d.z)/a;if(2<a)return!1;b.x=k*d.x;b.y=-k*d.y}}return c};f.prototype.Qp=function(d,b){for(var a=this.g,c=[],k,l=d.yd=0;l<b;l++)for(var e=0;e<b;e++){c[0]=l+e*(b+1);c[1]=l+1+e*(b+1);c[2]=l+(e+1)*(b+1);c[3]=
l+1+(e+1)*(b+1);k=!0;for(var m=0;4>m;m++)isNaN(d.gd[2*c[0]])&&(k=!1);if(k){var f=!1,g=!1,q=!1,n=!1;for(m=0;4>m;m++){var h=d.gd[2*c[m]];h<a.o.width&&(g=!0);0<=h&&(f=!0);h=d.gd[2*c[m]+1];h<a.o.height&&(q=!0);0<=h&&(n=!0)}if(k=k&&g&&f&&q&&n)d.se[d.yd++]=c[0],d.se[d.yd++]=c[3],d.se[d.yd++]=c[2],d.se[d.yd++]=c[0],d.se[d.yd++]=c[1],d.se[d.yd++]=c[3]}}};f.prototype.zq=function(){var d=this.g,b=d.h,a=d.h.J;d.ne&&(d.Ic(0),d.Rc());if(d.H){var c=d.H,k=this.Yl;c.useProgram(k);this.Yj(k);c.enable(c.CULL_FACE);
c.cullFace(c.FRONT);c.enable(c.DEPTH_TEST);p.aa.te(this.sb);p.aa.perspective(d.Ib(),d.rb.width/d.rb.height,.1,100,this.sb);c.uniformMatrix4fv(c.getUniformLocation(k,"uPMatrix"),!1,this.sb);this.g.tc=0;d.tj();var l=d.aj(),e=0;var m=a.length-1;for(var f={},g=a[m];g.rf&&0<m;)m--,g=a[m];for(var q=m,n=q,h=0;6>h;h++)for(var r=0;r<g.fa;r++)for(var v=0;v<g.L;v++){var u=v+r*g.L+h*g.L*g.fa;f[u]=1}for(;m>=l;){var x={};g=a[m];var A=null;0<m&&(A=a[m-1]);var w=!0;for(var B in f)if(f.hasOwnProperty(B)){u=Number(B);
var z=g.U[u];h=Number(Math.floor(u/(g.L*g.fa)));r=Math.floor((u-h*g.L*g.fa)/g.L);v=Math.floor(u-(r*g.L+h*g.L*g.fa));if(6<=h)console.log("Grrr...");else{var C=this.g.pb.fb[h];C.Da=!1;z||(z=g.U[u]=new p.Id,d.M("create "+u));this.Pp(z,g,v,r,Math.max(1,this.gk>>q-m),h,-(0!=d.rc)?.3:.1);z.visible=0<z.yd||g.cache;z.Mh=3;z.yg=Date.now();z.visible&&!z.ib&&(w=!1,this.wh()?z.h||(z.Xd?z.Xd--:(this.Qh(z,g,d.He(h,m,v,r)),d.Da=!0)):this.g.tc++);if(A&&(z.visible||A.cache)){z=(v*b.G+1)/g.width;v=Math.min(1,((v+1)*
b.G-1)/g.width);var D=(r*b.G+1)/g.height;r=Math.min(1,((r+1)*b.G-1)/g.height);u=b.G/A.width;C=b.G/A.height;var E=D;D=Math.floor(D*A.height/b.G);do{var F=z,H=Math.floor(z*A.width/b.G);do{var I=H+D*A.L+h*A.L*A.fa;H<A.L&&D<A.fa?x[I]=1:d.M("Grrrr");H++;F+=u}while(F<v);D++;E+=C}while(E<r)}}}w&&(n=m,20>d.f.c&&m<this.Ve&&(this.oi=!0));f=x;m--}this.Gj();c.uniform1i(c.getUniformLocation(k,"tileTexture"),0);c.activeTexture(c.TEXTURE0);m=l;for(l=-1;m<=Math.min(n,this.Ve-1);){g=a[m];for(B in g.U)if(g.U.hasOwnProperty(B)){f=
Number(B);z=g.U[f];h=Math.floor(f/(g.L*g.fa));r=Math.floor((f-h*g.L*g.fa)/g.L);v=Math.floor(f-(r*g.L+h*g.L*g.fa));l!=h&&(l=h,this.xi(h,k));if(e>d.we){d.M("Excided painted tiles");this.oi=!1;break}z.ib&&(f=h=b.G,v==g.L-1&&(h=g.width-b.G*v),r==g.fa-1&&(f=g.height-b.G*r),h=(h+2*b.Ja)/b.G,f=(f+2*b.Ja)/b.G,c.bindTexture(c.TEXTURE_2D,z.ib),c.uniform2f(c.getUniformLocation(k,"uCanvasDimensions"),d.o.width,d.o.height),q=c.getUniformLocation(k,"srcScale"),c.uniform2f(q,.5*g.width/b.G/h,.5*g.height/b.G/f),
q=c.getUniformLocation(k,"srcOffset"),c.uniform2f(q,(.5*g.width+b.Ja-b.G*v)/b.G/h,-(.5*g.height+b.Ja-b.G*r)/b.G/f+1),q=c.getUniformLocation(k,"zOffset"),c.uniform1f(q,(m+1)/(a.length+5)),h=c.getAttribLocation(k,"aVertexPosition"),c.disableVertexAttribArray(0),c.disableVertexAttribArray(1),c.disableVertexAttribArray(2),c.enableVertexAttribArray(h),c.activeTexture(c.TEXTURE0),c.texParameteri(c.TEXTURE_2D,c.TEXTURE_MAG_FILTER,c.LINEAR),c.texParameteri(c.TEXTURE_2D,c.TEXTURE_MIN_FILTER,c.LINEAR),c.texParameteri(c.TEXTURE_2D,
c.TEXTURE_WRAP_S,c.CLAMP_TO_EDGE),c.texParameteri(c.TEXTURE_2D,c.TEXTURE_WRAP_T,c.CLAMP_TO_EDGE),c.bindBuffer(c.ARRAY_BUFFER,this.Co),c.vertexAttribPointer(h,2,c.FLOAT,!1,0,0),c.bufferData(c.ARRAY_BUFFER,new Float32Array(z.gd),c.DYNAMIC_DRAW),c.bindBuffer(c.ELEMENT_ARRAY_BUFFER,this.Bo),c.bufferData(c.ELEMENT_ARRAY_BUFFER,new Uint16Array(z.se),c.DYNAMIC_DRAW),c.drawElements(c.TRIANGLES,z.yd,c.UNSIGNED_SHORT,0),e++)}m++}c.disable(c.CULL_FACE);c.cullFace(c.FRONT_AND_BACK);d.$c=!1}};f.prototype.xi=function(d,
b){var a=this.g.H;p.aa.te(this.ma);this.Df(d,-1);a.uniformMatrix4fv(a.getUniformLocation(b,"matRotate"),!1,this.ma)};f.prototype.bp=function(){var d=this.g;if(d.H){var b=d.H,a=this.Zl;b.useProgram(a);this.xi(0,a);b.uniform2f(b.getUniformLocation(a,"uCanvasDimensions"),d.o.width,d.o.height);if(1==d.s.format){var c=b.getUniformLocation(a,"srcScale");b.uniform2f(c,-.5/Math.PI,(d.s.ej?-1:1)/Math.PI)}14==d.s.format&&(c=b.getUniformLocation(a,"srcScale"),b.uniform2f(c,1-2*d.s.Qe/(d.s.width/3),1-2*d.s.Qe/
(d.s.height/2)));c=b.getUniformLocation(a,"srcOffset");b.uniform2f(c,.5,.5);this.Yj(a);c=b.getUniformLocation(a,"cubeTexture");b.uniform1i(c,0);c=b.getAttribLocation(a,"aVertexPosition");b.disableVertexAttribArray(0);b.disableVertexAttribArray(1);b.disableVertexAttribArray(2);b.enableVertexAttribArray(c);b.bindBuffer(b.ARRAY_BUFFER,this.ei);b.vertexAttribPointer(c,2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindTexture(b.TEXTURE_2D,d.s.sc);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE);
b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_MIN_FILTER,b.LINEAR);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_MAG_FILTER,b.LINEAR);b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.Yc);b.drawElements(b.TRIANGLES,6,b.UNSIGNED_SHORT,0)}};f.prototype.Yj=function(d){var b=this.g,a=b.H,c=this.g.o,k=c.width/c.height;switch(b.f.mode){case 1:k=1;break;case 2:k=c.width/Math.sqrt(c.width*c.width+c.height*c.height);break;case 3:4*c.height/3<c.width&&(k=4/3)}c=a.getUniformLocation(d,
"rectDstDistance");this.Ej=Math.tan(Math.min(b.f.c,179)/2*Math.PI/180)*k;a.uniform1f(c,this.Ej);c=a.getUniformLocation(d,"fisheyeDistance");this.cj=180/(b.f.c*Math.PI*k);a.uniform1f(c,this.cj);c=a.getUniformLocation(d,"stereoDistance");this.Rj=Math.tan(Math.min(b.f.c,359)/4*Math.PI/180)*k;a.uniform1f(c,this.Rj);c=a.getUniformLocation(d,"directionBlend");a.uniform1f(c,b.Jg)};f.prototype.ap=function(){var d=this.g,b=d.H,a=this.Xl;b.useProgram(a);b.enable(b.DEPTH_TEST);this.xi(0,a);b.uniform2f(b.getUniformLocation(a,
"uCanvasDimensions"),d.o.width,d.o.height);d=b.getUniformLocation(a,"srcScale");b.uniform2f(d,1,1);d=b.getUniformLocation(a,"srcOffset");b.uniform2f(d,0,0);d=b.getUniformLocation(a,"zOffset");b.uniform1f(d,.9999);this.Yj(a);this.xi(0,a);d=b.getUniformLocation(a,"cubeTexture");b.uniform1i(d,0);a=b.getAttribLocation(a,"aVertexPosition");b.disableVertexAttribArray(0);b.disableVertexAttribArray(1);b.disableVertexAttribArray(2);b.enableVertexAttribArray(a);b.bindBuffer(b.ARRAY_BUFFER,this.ei);b.vertexAttribPointer(a,
2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindTexture(b.TEXTURE_CUBE_MAP,this.xc);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_MIN_FILTER,b.LINEAR);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_MAG_FILTER,b.LINEAR);b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.Yc);b.drawElements(b.TRIANGLES,6,b.UNSIGNED_SHORT,0)};f.prototype.yn=function(){for(var d=this.g,b=d.H,
a=[1,3,5,4,0,2],c=!0,k=!0,l=!1,e=0;6>e;e++)this.Va[e].ve.complete?this.sh[e]||(l=!0):c=!1,this.Va[e].qf.complete||(k=!1);if(k||c)if(!k||c||!this.xc||l){e=Math.round(d.uc/d.Gf);k=(d.uc-e)/2;d.M("paint cube single - isMain: "+c+" overlap: "+k);this.Ve=0;this.xc||(this.xc=b.createTexture());d.fd++;b.bindTexture(b.TEXTURE_CUBE_MAP,this.xc);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE);b.pixelStorei(b.UNPACK_FLIP_Y_WEBGL,
1);l=document.createElement("canvas");l.width=e;l.height=e;var m=l.getContext("2d");for(e=0;6>e;e++){var f=a[e];this.Va[f].ve.complete?this.sh[f]||(m.drawImage(this.Va[f].ve,-k,-k),b.texImage2D(b.TEXTURE_CUBE_MAP_POSITIVE_X+e,0,b.RGBA,b.RGBA,b.UNSIGNED_BYTE,l),this.sh[f]=!0):(m.drawImage(this.Va[f].qf,-k,-k,d.uc,d.uc),b.texImage2D(b.TEXTURE_CUBE_MAP_POSITIVE_X+e,0,b.RGBA,b.RGBA,b.UNSIGNED_BYTE,l))}this.ug=!c}};f.prototype.xn=function(){var d=this.g,b=this.g.h,a=d.h.J,c=d.H,k;var l=a.length-1;if(!(0>
l)){a[l].rf&&l--;var e=512;d.tg&&(e=256);!d.vf&&2<=d.devicePixelRatio&&(e=512);for((k=c.getParameter(c.MAX_CUBE_MAP_TEXTURE_SIZE))&&k<e&&(e=k);0<l&&a[l-1].width<=e;)l--;e=a[l];if(0!=e.L){k=l;var m=this.tn(l);this.ug&&m&&(this.ug=!1);m||(l=a.length-1,e=a[l],m||(e.rf?(m=e.loaded,this.sj(l-1)&&(--l,m=!0)):m=this.sj(l)),this.ug=!0);this.sj(k);if(m&&this.Ve>l){e=a[l];d.M("paint cube level "+l);this.Ve=l;a=d.h.Ja;l=0<a||1<e.L||1<e.fa;m=k=void 0;l&&(m=document.createElement("canvas"),m.width=e.width,m.height=
e.height,2048>e.width&&(1500<e.width?(m.width=2048,m.height=2048):700<e.width?(m.width=1024,m.height=1024):(m.width=512,m.height=512)),k=m.getContext("2d"));this.xc=c.createTexture();d.fd++;c.bindTexture(c.TEXTURE_CUBE_MAP,this.xc);c.texParameteri(c.TEXTURE_CUBE_MAP,c.TEXTURE_WRAP_S,c.CLAMP_TO_EDGE);c.texParameteri(c.TEXTURE_CUBE_MAP,c.TEXTURE_WRAP_T,c.CLAMP_TO_EDGE);c.pixelStorei(c.UNPACK_FLIP_Y_WEBGL,1);var f=[1,3,5,4,0,2];b=b.G;for(var g=0;6>g;g++){for(var h=0;h<e.fa;h++)for(var n=0;n<e.L;n++){var p=
n+h*e.L+f[g]*e.L*e.fa,r=e.U[p],v=r.h;r.K&&(v=r.K);v?l?(p=m.width/e.width,k.drawImage(v,p*(n*b-a),p*(h*b-a),p*v.width,p*v.height)):c.texImage2D(c.TEXTURE_CUBE_MAP_POSITIVE_X+g,0,c.RGBA,c.RGBA,c.UNSIGNED_BYTE,v):(d.M("WTF?!"),d.M(p),d.M(r))}l&&c.texImage2D(c.TEXTURE_CUBE_MAP_POSITIVE_X+g,0,c.RGBA,c.RGBA,c.UNSIGNED_BYTE,m)}}}}};f.prototype.sj=function(d){var b=this.g,a=b.h.J[d];if(0==a.L)return!1;var c=!0;a.cache=!0;for(var k=0;6>k;k++)for(var l=0;l<a.fa;l++)for(var e=0;e<a.L;e++){var m=e+l*a.L+k*a.L*
a.fa,f=a.U[m];f||(f=a.U[m]=new p.Id);this.wh()?f.h||(f.Xd?f.Xd--:(this.Qh(f,a,b.He(k,d,e,l)),b.Da=!0)):this.g.tc++;f.ib||(c=!1,b.Da=!0)}c&&(a.loaded=!0);return c};f.prototype.tn=function(d){d=this.g.h.J[d];if(0==d.L)return!1;for(var b=0;6>b;b++)for(var a=0;a<d.fa;a++)for(var c=0;c<d.L;c++){var k=d.U[c+a*d.L+b*d.L*d.fa];if(!k||!k.ib)return!1}return d.loaded=!0};f.prototype.ready=function(){return null!=this.xc};f.prototype.Gj=function(){for(var d=this.g,b=d.h.J,a=d.H,c=Date.now(),k=b.length-1;0<=k;k--){var l=
b[k];if(!l.cache)for(var e in l.U)if(l.U.hasOwnProperty(e)){var m=l.U[e];0<m.Mh&&m.Mh--;m.visible||0<m.Mh?(m.visible&&(m.yg=c),m=this.Pd.indexOf(m),-1!==m&&this.Pd.splice(m,1)):-1===this.Pd.indexOf(m)&&(m.level=l,this.Pd.push(m))}}if(this.Pd.length>1.1*d.tm)for(this.Pd.sort(function(a,c){return c.yg-a.yg});this.Pd.length>d.tm;)m=this.Pd.pop(),m.ib&&(a.deleteTexture(m.ib),d.fd--,m.ib=0),m.h=null,m.bf&&(a.deleteBuffer(m.bf),m.bf=0),e=m.level.U.indexOf(m),d.M("delete "+e+" "+(c-m.yg)),delete m.level.U[e]};
f.prototype.sq=function(){var d=this.g;if(d.H){var b=this.g.H;b.disable(b.DEPTH_TEST);var a;for(a=0;a<d.I.length;a++){var c=d.I[a];if(!c.ld){p.aa.te(this.ma);p.aa.rotate(this.ma,-d.O.c*Math.PI/180,[0,0,1]);p.aa.rotate(this.ma,-d.i.c*Math.PI/180,[1,0,0]);p.aa.rotate(this.ma,(180-d.pan.c)*Math.PI/180,[0,1,0]);p.aa.rotate(this.ma,c.pan*Math.PI/180,[0,1,0]);p.aa.rotate(this.ma,-c.i*Math.PI/180,[1,0,0]);p.aa.translate(this.ma,[0,0,1]);p.aa.rotate(this.ma,c.mb*Math.PI/180,[0,0,1]);p.aa.rotate(this.ma,-c.Ea*
Math.PI/180,[0,1,0]);p.aa.rotate(this.ma,c.wa*Math.PI/180,[1,0,0]);var k=Math.tan(c.f/2*Math.PI/180),l=c.ee;l||(l=16/9);p.aa.scale(this.ma,[k,k/l,1]);p.aa.translate(this.ma,[0,0,-1]);b.bindBuffer(b.ARRAY_BUFFER,this.Si);b.vertexAttribPointer(this.F.$,3,b.FLOAT,!1,0,0);b.bindBuffer(b.ARRAY_BUFFER,this.je);b.vertexAttribPointer(this.F.Ca,2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindTexture(b.TEXTURE_2D,c.sc);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_MAG_FILTER,b.LINEAR);b.texParameteri(b.TEXTURE_2D,
b.TEXTURE_MIN_FILTER,b.LINEAR);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE);b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.Yc);b.uniform1i(this.F.wf,0);b.uniformMatrix4fv(this.F.Ig,!1,this.ma);b.uniformMatrix4fv(this.F.Vd,!1,this.sb);b.drawElements(b.TRIANGLES,6,b.UNSIGNED_SHORT,0)}}b.enable(b.DEPTH_TEST)}};f.prototype.rq=function(){var d=this.g,b;if(d.o.width!=d.D.offsetWidth||d.o.height!=d.D.offsetHeight)d.o.width=d.D.offsetWidth,
d.o.height=d.D.offsetHeight;d.ne&&(d.Ic(0),d.Rc());if(d.H){var a=d.H;a.useProgram(this.F);p.aa.te(this.sb);p.aa.perspective(d.Ib(),d.rb.width/d.rb.height,.1,100,this.sb);a.uniformMatrix4fv(this.F.Vd,!1,this.sb);this.Df(0);a.uniform1i(this.F.wf,0);a.uniformMatrix4fv(this.F.Vd,!1,this.sb);a.uniformMatrix4fv(this.F.Ig,!1,this.ma);a.enableVertexAttribArray(this.F.$);a.enableVertexAttribArray(this.F.Ca);a.bindBuffer(a.ARRAY_BUFFER,this.je);a.vertexAttribPointer(this.F.Ca,2,a.FLOAT,!1,0,0);a.activeTexture(a.TEXTURE0);
a.bindBuffer(a.ELEMENT_ARRAY_BUFFER,this.Yc);a.uniform1f(this.F.Ci,1E-4);a.vertexAttribPointer(this.F.$,3,a.FLOAT,!1,0,0);a.bindTexture(a.TEXTURE_2D,d.s.sc);for(b=0;1>b;b++)this.Df(0),a.bindBuffer(a.ARRAY_BUFFER,d.s.bk),a.vertexAttribPointer(this.F.$,3,a.FLOAT,!1,0,0),a.bindBuffer(a.ARRAY_BUFFER,d.s.ri),a.vertexAttribPointer(this.F.Ca,2,a.FLOAT,!1,0,0),a.activeTexture(a.TEXTURE0),a.bindBuffer(a.ELEMENT_ARRAY_BUFFER,d.s.mj),a.uniform1i(this.F.wf,0),a.uniformMatrix4fv(this.F.Ig,!1,this.ma),a.uniformMatrix4fv(this.F.Vd,
!1,this.sb),a.drawElements(a.TRIANGLES,36,a.UNSIGNED_SHORT,0)}};f.prototype.qq=function(){var d=this.g,b=d.H,a=d.s;if(0<d.I.length)for(var c=0;c<d.I.length;c++){var k=d.I[c];if(!k.ld&&k.vl&&k.Dh!=k.b.currentTime&&(k.Dh=k.b.currentTime,!k.ee&&0<k.b.videoHeight&&(k.ee=k.b.videoWidth/k.b.videoHeight),d.dh))try{k.sc&&(b.bindTexture(b.TEXTURE_2D,k.sc),b.texImage2D(b.TEXTURE_2D,0,b.RGB,b.RGB,b.UNSIGNED_BYTE,k.b),d.update())}catch(e){d.M(e)}}if(a.b&&(c=Number(a.b.currentTime),a.Dh!=c)){a.Dh=c;try{var l=
0<a.b.readyState;d.Ih&&a.hd&&(l=l&&0<a.b.currentTime);a.sc&&a.Kh&&l&&(a.hd=!0,a.width=a.b.videoWidth,a.height=a.b.videoHeight,b.pixelStorei(b.UNPACK_FLIP_Y_WEBGL,d.s.ej),b.bindTexture(b.TEXTURE_2D,a.sc),b.texImage2D(b.TEXTURE_2D,0,b.RGB,b.RGB,b.UNSIGNED_BYTE,a.b),a.sm=!0,d.update())}catch(e){d.M(e)}}};f.prototype.Nl=function(){var d,b,a=this.g,c=this.g.H;a.ya.style.visibility="hidden";a.A.wg!=a.A.mode&&(a.A.wg=a.A.mode);if((0<=a.A.mode||0<a.A.nb.length)&&!a.B.Wg){var k=1;0>=a.A.mode&&(k=0);3==a.A.mode&&
(k=a.A.qa);for(d=0;d<a.P.length;d++){var l=a.P[d];if("poly"==l.type){var e=l.Wd,m=k;2==a.A.mode&&(m=l.qa);var f=a.A.nb.indexOf(l.id);-1!=f&&(m=a.A.Ob[f]);if(0<e.length){f=[];for(b=0;b<e.length;b++)f.push(e[b].Mb),f.push(e[b].vb),f.push(0);c.useProgram(this.Dd);c.enable(c.BLEND);c.blendFuncSeparate(c.SRC_ALPHA,c.ONE_MINUS_SRC_ALPHA,c.SRC_ALPHA,c.ONE);c.disable(c.DEPTH_TEST);e=c.createBuffer();c.bindBuffer(c.ARRAY_BUFFER,e);c.bufferData(c.ARRAY_BUFFER,new Float32Array(f),c.STATIC_DRAW);c.uniform2f(c.getUniformLocation(this.Dd,
"uCanvasDimensions"),a.o.width,a.o.height);e=c.getUniformLocation(this.Dd,"uColor");b=l.kc;c.uniform3f(e,(b>>16&255)/255,(b>>8&255)/255,(b&255)/255);var g=c.getUniformLocation(this.Dd,"uAlpha");c.uniform1f(g,l.jc*m);c.vertexAttribPointer(this.Dd.$,3,c.FLOAT,!1,0,0);c.drawArrays(c.LINE_LOOP,0,f.length/3);b=l.hc;c.uniform3f(e,(b>>16&255)/255,(b>>8&255)/255,(b&255)/255);c.uniform1f(g,l.gc*m);c.enable(c.STENCIL_TEST);c.clearStencil(0);c.clear(c.STENCIL_BUFFER_BIT);c.colorMask(!1,!1,!1,!1);c.stencilFunc(c.ALWAYS,
1,1);c.stencilOp(c.INCR,c.INCR,c.INCR);c.drawArrays(c.TRIANGLE_FAN,0,f.length/3);c.colorMask(!0,!0,!0,!0);c.stencilFunc(c.EQUAL,1,1);c.stencilOp(c.ZERO,c.ZERO,c.ZERO);c.drawArrays(c.TRIANGLE_FAN,0,f.length/3);c.disable(c.BLEND);c.enable(c.DEPTH_TEST);c.disable(c.STENCIL_TEST);c.useProgram(this.F)}}}}};f.prototype.Xj=function(){var d=this.g,b=d.h;if(d.o.width!=d.D.offsetWidth||d.o.height!=d.D.offsetHeight)d.o.width=d.D.offsetWidth,d.o.height=d.D.offsetHeight;d.ne&&(d.Ic(0),d.Rc());if(d.H){var a=d.H;
this.wi();a.clear(a.COLOR_BUFFER_BIT|a.DEPTH_BUFFER_BIT);a.disable(a.DEPTH_TEST);a.disable(a.CULL_FACE);a.useProgram(this.dg);var c=a.getUniformLocation(this.dg,"uRect");a.uniform2f(a.getUniformLocation(this.dg,"uCanvasDimensions"),d.o.width,d.o.height);a.activeTexture(a.TEXTURE0);a.bindBuffer(a.ELEMENT_ARRAY_BUFFER,this.Yc);var k=a.getAttribLocation(this.dg,"aVertexPosition");a.disableVertexAttribArray(0);a.disableVertexAttribArray(1);a.disableVertexAttribArray(2);a.enableVertexAttribArray(k);a.bindBuffer(a.ARRAY_BUFFER,
this.je);a.vertexAttribPointer(k,2,a.FLOAT,!1,0,0);d.tc=0;var l=100/d.f.c;var e=b.width/b.height;k=d.o.height*l*e;l*=d.o.height;e=(d.pan.c/100/e-.5)*k+d.o.width/2;for(var m=(d.i.c/100-.5)*l+d.o.height/2,f,g,h,n=0;b.J.length>=n+2&&b.J[n+1].width>k;)n++;var y;var r=[];for(y=b.J.length-1;y>=n;){var v=b.J[y];if(v.cache){var u={Ya:0,zb:0};u.Bb=v.L-1;u.Cb=v.fa-1}else{u={};f=-m/l*(v.height/d.h.G);g=(-e+d.o.width)/k*(v.width/d.h.G);var x=(-m+d.o.height)/l*(v.height/d.h.G);u.Ya=Math.min(Math.max(0,Math.floor(-e/
k*(v.width/d.h.G))),v.L-1);u.zb=Math.min(Math.max(0,Math.floor(f)),v.fa-1);u.Bb=Math.min(Math.max(0,Math.floor(g)),v.L-1);u.Cb=Math.min(Math.max(0,Math.floor(x)),v.fa-1)}r[y]=u;var A=!0;for(g=u.zb;g<=u.Cb;g++)for(f=u.Ya;f<=u.Bb;f++)h=f+g*v.L,x=v.U[h],x||(x=new p.Id,v.U[h]=x),this.wh()?x.h||(this.Qh(x,v,d.He(0,y,f,g)),d.Da=!0):this.g.tc++,x.h&&x.h.complete||(A=!1),x.visible=!0;u.rj=A;y--}for(y=b.J.length-1;y>=n;){v=b.J[y];if(r[y]&&0<=r[y].Ya)for(u=r[y],g=u.zb;g<=u.Cb;g++)for(f=u.Ya;f<=u.Bb;f++)h=f+
g*v.L,(x=v.U[h])&&x.h&&x.h.complete&&(d=e+(-b.Ja+b.G*f)*k/v.width,a.uniform4f(c,d,m+(-b.Ja+b.G*g)*l/v.height,x.h.width*k/v.width,x.h.height*l/v.height),x&&x.ib&&(a.bindBuffer(a.ELEMENT_ARRAY_BUFFER,this.Yc),a.bindTexture(a.TEXTURE_2D,x.ib),a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MAG_FILTER,a.LINEAR),a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MIN_FILTER,a.LINEAR),a.texParameteri(a.TEXTURE_2D,a.TEXTURE_WRAP_S,a.CLAMP_TO_EDGE),a.texParameteri(a.TEXTURE_2D,a.TEXTURE_WRAP_T,a.CLAMP_TO_EDGE),a.drawElements(a.TRIANGLES,
6,a.UNSIGNED_SHORT,0)));y--}this.Gj()}};f.prototype.fi=function(){var d=this.g.H;if(d&&this.Va)for(;0<this.Va.length;){var b=this.Va.pop();b.Vo=!0;d.deleteTexture(b)}this.xc&&(d.deleteTexture(this.xc),this.xc=null);this.Ve=1E6;this.sh=[!1,!1,!1,!1,!1,!1]};return f}();p.Ym=f})(ggP2VR||(ggP2VR={}));
(function(p){var f=function(){return function(){this.mf={fg:1,gg:1,Dg:0,Eg:0,Mf:0,gh:0,scale:1};this.hb=!0;this.Se=[]}}(),h=function(){function d(){var b;this.fb=Array(6);for(b=0;6>b;b++)this.fb[b]=new f}d.prototype.un=function(b,a,c,d){for(var k=0;6>k;k++){var e;if(e=this.fb[k]){var m=[];m.push(new p.xa(-1,-1,-1,0,0));m.push(new p.xa(1,-1,-1,1,0));m.push(new p.xa(1,1,-1,1,1));m.push(new p.xa(-1,1,-1,0,1));for(var f=0;f<m.length;f++)4>k?m[f].Ea(-Math.PI/2*k):m[f].wa(Math.PI/2*(4===k?-1:1)),d&&(m[f].mb(d.O*
Math.PI/180),m[f].wa(-d.pitch*Math.PI/180)),m[f].Ea(-b*Math.PI/180),m[f].wa(a*Math.PI/180),m[f].mb(c*Math.PI/180);e.hb=0<m.length}}};return d}();p.Um=h})(ggP2VR||(ggP2VR={}));
(function(p){p.Tm=function(){return function(){this.J=[];this.sf="0x000000";this.Sl=!1;this.Gl=this.Fl=.4;this.G=512;this.Ja=1;this.qj=0;this.Hl="";this.am=this.height=this.width=0}}();p.hk=function(){return function(){this.height=this.width=0;this.rf=this.cache=!1;this.fa=this.L=0;this.loaded=!1;this.U=[]}}();p.Id=function(){return function(){this.loaded=this.visible=!1;this.Xd=this.Fj=0;this.gd=[];this.Eh=0;this.se=[];this.Mh=this.yg=this.yd=0}}()})(ggP2VR||(ggP2VR={}));
(function(p){p.Pm=function(){return function(f,h){this.g=f;this.ta=h;var d=this.__div=document.createElement("div");var b=document.createElement("img");b.setAttribute("src","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA5xJREFUeNqclmlIVFEUx997TjrplFQW2WKBBSYtRFlpWUILSSsRZRQIBdGHCFqIoKIvQRsUFRJC9LEgaSFbMMpcWi1pLzOLsjItKms0U5t5/c/wH7nc5o2jF374xrv87z33nHOPaRsRtbFgDpgJxoD+wATfwDNQDK6CyrCr5OcbhgiGIRsUAZt4QTWoIFXgp9JfAhY7rgdBl8NeBoLDYBloA+dBOagFTcDHcVEgDgwBGWA+OAcugvXgvb5wKMGJoAAMp9BpUA96EBf/Btsf8BI8AWfAErAcpHHDZeriliY2AVwDg8AucAQ0Ag+I4XhTm2Oxz8PT46KMbTx5EZjuJDgAnAVusJUm9DhYwalFcc59sIXXIaceFkowDySBPTRPL20xm+b7zYXa+N3CPrWJ6GuwGySA40HLBHc/GywFhbS5R1lEBrZy7FQwiSaX9pmnqeAYt+KUcew7BVZw/QKTq0ocpYPVvDOXItZCk2xgDIZqL8BR8Ab0VDbr4yZOgLeIwzQx6WiQxcCt1+6sld66L4yYtFSwF4yg2dU7/cEwGW9YVkAwmycp1dzdpvgm0DcCh4kHmxWzBls0uBX4qqmZJ4KzePm1IeJLgjmlC16aDKZpp5Q168B3o6wsSwTHgU+MIUs74RSj6y1d+212HKimJlUE+tFRfJpYtOKNXWmJTASqWf2Bu/R6+4TKHOrOzG4IhptjWgHbGkZvepQ6SQK7oRuCXzjX1DJavBEX1ygfT8FgBqpfm1zRDcEKbR2bsZlkJCdXieB1ZhZ5YtqVgXIPN+m9kbY6hpdb+d9fPncJRmZmqQheZkemJmgxyxykl3XWJEkcAl7N21s7PDcl5ZJ0PAa3wVwmWtVbZafPwQ7wLozYB7ATPNJO56d/LAikP9u+66KNJS1d4IOZp7wU0hfLukUyzgwm70T2N/DOxIy/eFdqawa5DL2NEGwP5k15Ja4woz9glvcomd9NzyvkFcQo5gomaLfm5c0svnKZ2k7q7+FauvR2MJKZR3+sY5WgtvkdG6JyELGhNHMTXyGfLviRJ5Tcd4Dlhle7086Sgp8CqVxDkn4OqHaqacr5ekjy3Q/W0FRNNGmoMtamdzdxsytZC0lqXKhEgWPVVgImg2NgFT1MHOoOk3yLEtgWN5TEOYvoIFI1rGM19//2wpAD7imF7lfwENwAxaASNCj90pcLLKdC2Iyw1M9gnEplMEp5kOU1f8WwKGJm8oUr9f8JMAAVMDM6HSDa9QAAAABJRU5ErkJggg%3D%3D");
b.setAttribute("style","position: absolute;width: 28px; height: 28px;top: -14px;left: -14px; "+f.Ia+"user-select: none;");b.ondragstart=function(){return!1};d.appendChild(b);b="position:absolute;"+(f.Ia+"user-select: none;");b+=f.Ia+"touch-callout: none;";b+=f.Ia+"tap-highlight-color: rgba(0,0,0,0);";f.Jc&&!f.Z&&(b+=f.Ia+"transform: translateZ(9999999px);");d.setAttribute("style",b);d.onclick=function(){f.xf(h);f.yj(h.url,h.target);f.ba&&f.ba.hotspotProxyClick&&f.ba.hotspotProxyClick(h.id,h.url)};
d.ondblclick=function(){f.ba&&f.ba.hotspotProxyDoubleClick&&f.ba.hotspotProxyDoubleClick(h.id,h.url)};var a=f.A.Uj;if(a.enabled){var c=document.createElement("div");b="position:absolute;top:\t 20px;";b=a.Bi?b+"white-space: pre-wrap;":b+"white-space: nowrap;";b+=f.Ia+"transform-origin: 50% 50%;";c.setAttribute("style",b+"visibility: hidden;overflow: hidden;padding: 0px 1px 0px 1px;font-size: 13px;");c.style.color=this.g.ga(a.Vj,a.Tj);a.background?c.style.backgroundColor=this.g.ga(a.hc,a.gc):c.style.backgroundColor=
"transparent";c.style.border="solid "+this.g.ga(a.kc,a.jc)+" "+a.Ji+"px";c.style.borderRadius=a.Ii+"px";c.style.textAlign="center";0<a.width?(c.style.left=-a.width/2+"px",c.style.width=a.width+"px"):c.style.width="auto";c.style.height=0<a.height?a.height+"px":"auto";c.style.overflow="hidden";c.innerHTML=h.title;d.onmouseover=function(){0==a.width&&(c.style.left=-c.offsetWidth/2+"px");c.style.visibility="inherit";f.ba&&f.ba.hotspotProxyOver&&f.ba.hotspotProxyOver(h.id,h.url)};d.onmouseout=function(){c.style.visibility=
"hidden";f.ba&&f.ba.hotspotProxyOut&&f.ba.hotspotProxyOut(h.id,h.url)};d.appendChild(c)}}}()})(ggP2VR||(ggP2VR={}));(function(p){p.Ec=function(){return function(){this.value=this.time=0;this.ak="";this.ie=this.he=this.Vc=this.ge=this.ub=this.type=this.xb=0;this.mh=""}}();p.ek=function(){return function(){this.kq=this.cn=this.length=0}}();p.dk=function(){return function(){}}()})(ggP2VR||(ggP2VR={}));
(function(p){var f=function(){function f(d){this.g=d;this.enabled=!1;this.Tf=1;this.Re=0;this.type="crossdissolve";this.ec=this.Oa=this.Bc=0;this.Of=5;this.de=1;this.Pf=!1;this.Ge=this.Fe=this.Qj=0;this.Hd=70;this.Om=0;this.yb=this.Nm=1;this.kh=this.jh=.5;this.fe=this.Ai=this.Th=this.Lh=!1;this.Vi=1}f.prototype.rg=function(){var d=this.g.H,b=d.createShader(d.VERTEX_SHADER);d.shaderSource(b,"attribute vec3 aVertexPosition;\nattribute vec2 aTextureCoord;\nvarying vec2 vTextureCoord;\nuniform bool uZoomIn;\nuniform float uZoomFactor;\nuniform vec2 uZoomCenter;\nvoid main(void) {\n\t gl_Position = vec4(aVertexPosition, 1.0);\n\t if(!uZoomIn) {\n\t \n\t   vTextureCoord = aTextureCoord;\n\t }\n\t else {\n\t   vTextureCoord = (aTextureCoord - vec2(0.5, 0.5)) * (1.0/uZoomFactor) + uZoomCenter;\n\t }\n}\n");
d.compileShader(b);d.getShaderParameter(b,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(b)),b=null);var a=d.createShader(d.FRAGMENT_SHADER);d.shaderSource(a,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\nuniform float uAlpha;\nuniform sampler2D uSampler;\nvoid main(void) {\n vec4 textureColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n gl_FragColor = vec4(textureColor.x, textureColor.y, textureColor.z, uAlpha);\n}\n");
d.compileShader(a);d.getShaderParameter(a,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(a)),a=null);this.oa=d.createProgram();d.attachShader(this.oa,b);d.attachShader(this.oa,a);d.linkProgram(this.oa);d.getProgramParameter(this.oa,d.LINK_STATUS)||alert("Could not initialise shaders");this.oa.$=d.getAttribLocation(this.oa,"aVertexPosition");d.enableVertexAttribArray(this.oa.$);this.oa.Ca=d.getAttribLocation(this.oa,"aTextureCoord");d.enableVertexAttribArray(this.oa.Ca);a=d.createShader(d.FRAGMENT_SHADER);
d.shaderSource(a,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\nuniform float uColorPercent;\nuniform float uAlpha;\nuniform vec3 uDipColor;\nuniform sampler2D uSampler;\nvoid main(void) {\n vec4 textureColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n gl_FragColor = vec4(textureColor.x * (1.0 - uColorPercent) + uDipColor.x * uColorPercent, textureColor.y * (1.0 - uColorPercent) + uDipColor.y * uColorPercent, textureColor.z * (1.0 - uColorPercent) + uDipColor.z * uColorPercent, uAlpha);\n}\n");
d.compileShader(a);d.getShaderParameter(a,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(a)),a=null);this.Ta=d.createProgram();d.attachShader(this.Ta,b);d.attachShader(this.Ta,a);d.linkProgram(this.Ta);d.getProgramParameter(this.Ta,d.LINK_STATUS)||alert("Could not initialise shaders");this.Ta.$=d.getAttribLocation(this.Ta,"aVertexPosition");d.enableVertexAttribArray(this.Ta.$);this.Ta.Ca=d.getAttribLocation(this.Ta,"aTextureCoord");d.enableVertexAttribArray(this.Ta.Ca);a=d.createShader(d.FRAGMENT_SHADER);
d.shaderSource(a,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\nuniform bool uRound;\nuniform float uRadius;\nuniform vec2 uRectDim;\nuniform vec2 uIrisCenter;\nuniform float uSoftEdge;\nuniform sampler2D uSampler;\nvoid main(void) {\n float alpha = 0.0;\n vec4 textureColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n if (uRound) {\n\t  vec2 diff = uIrisCenter - gl_FragCoord.xy;\n\t   float distFromCenter = sqrt( (diff.x * diff.x) + (diff.y * diff.y) );\n\t   if (distFromCenter > uRadius) {\n      alpha = 1.0;\n    } else {\n      alpha = 1.0 - ((uRadius - distFromCenter) / uSoftEdge);\n    };\n }\n else {\n    float alphaFromLeft = 1.0 - ((gl_FragCoord.x -(uIrisCenter.x - uRectDim.x)) / uSoftEdge);\n    float alphaFromRight = 1.0 - (((uIrisCenter.x + uRectDim.x) - gl_FragCoord.x) / uSoftEdge);\n    float alphaFromTop = 1.0 - ((gl_FragCoord.y -(uIrisCenter.y - uRectDim.y)) / uSoftEdge);\n    float alphaFromBottom = 1.0 - (((uIrisCenter.y + uRectDim.y) - gl_FragCoord.y) / uSoftEdge);\n    alpha = max(max(alphaFromLeft, alphaFromRight), max(alphaFromTop, alphaFromBottom));\n }\n gl_FragColor = vec4(textureColor.x, textureColor.y, textureColor.z, alpha);\n}\n");
d.compileShader(a);d.getShaderParameter(a,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(a)),a=null);this.Ha=d.createProgram();d.attachShader(this.Ha,b);d.attachShader(this.Ha,a);d.linkProgram(this.Ha);d.getProgramParameter(this.Ha,d.LINK_STATUS)||alert("Could not initialise shaders");this.Ha.$=d.getAttribLocation(this.Ha,"aVertexPosition");d.enableVertexAttribArray(this.Ha.$);this.Ha.Ca=d.getAttribLocation(this.Ha,"aTextureCoord");d.enableVertexAttribArray(this.Ha.Ca);a=d.createShader(d.FRAGMENT_SHADER);
d.shaderSource(a,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\nuniform float uPercent;\nuniform int uDirection;\nuniform vec2 uCanvasDimensions;\nuniform float uSoftEdge;\nuniform sampler2D uSampler;\nvoid main(void) {\n vec4 textureColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n float alpha = 0.0;\n if (uDirection == 1) {\n\t if (gl_FragCoord.x > uPercent) {\n    alpha = 1.0; \n  } else {\n    alpha = 1.0 - ((uPercent - gl_FragCoord.x) / uSoftEdge);\n  }\n }\n if (uDirection == 2) {\n\t if (gl_FragCoord.x < uCanvasDimensions.x - uPercent) {\n    alpha = 1.0; \n  } else {\n    alpha = 1.0 - ((gl_FragCoord.x - (uCanvasDimensions.x - uPercent)) / uSoftEdge);\n  }\n }\n if (uDirection == 3) {\n\t if (gl_FragCoord.y < uCanvasDimensions.y - uPercent) {\n    alpha = 1.0; \n  } else {\n    alpha = 1.0 - ((gl_FragCoord.y - (uCanvasDimensions.y - uPercent)) / uSoftEdge);\n  }\n }\n if (uDirection == 4) {\n\t if (gl_FragCoord.y > uPercent) {\n    alpha = 1.0; \n  } else {\n    alpha = 1.0 - ((uPercent - gl_FragCoord.y) / uSoftEdge);\n  }\n }\n gl_FragColor = vec4(textureColor.x, textureColor.y, textureColor.z, alpha);\n}\n");
d.compileShader(a);d.getShaderParameter(a,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(a)),a=null);this.Na=d.createProgram();d.attachShader(this.Na,b);d.attachShader(this.Na,a);d.linkProgram(this.Na);d.getProgramParameter(this.Na,d.LINK_STATUS)||alert("Could not initialise shaders");this.Na.$=d.getAttribLocation(this.Na,"aVertexPosition");d.enableVertexAttribArray(this.Na.$);this.Na.Ca=d.getAttribLocation(this.Na,"aTextureCoord");d.enableVertexAttribArray(this.Na.Ca)};f.prototype.Ic=function(){var d=
this.g.H;if(!d)return!1;if(this.dc=d.createFramebuffer()){d.bindFramebuffer(d.FRAMEBUFFER,this.dc);this.dc.width=1024;this.dc.height=1024;this.Je=d.createTexture();d.bindTexture(d.TEXTURE_2D,this.Je);d.texParameteri(d.TEXTURE_2D,d.TEXTURE_MIN_FILTER,d.LINEAR);d.texParameteri(d.TEXTURE_2D,d.TEXTURE_MAG_FILTER,d.LINEAR);d.texImage2D(d.TEXTURE_2D,0,d.RGBA,this.dc.width,this.dc.height,0,d.RGBA,d.UNSIGNED_BYTE,null);var b=d.createRenderbuffer();d.bindRenderbuffer(d.RENDERBUFFER,b);d.renderbufferStorage(d.RENDERBUFFER,
d.DEPTH_COMPONENT16,this.dc.width,this.dc.height);d.framebufferTexture2D(d.FRAMEBUFFER,d.COLOR_ATTACHMENT0,d.TEXTURE_2D,this.Je,0);d.framebufferRenderbuffer(d.FRAMEBUFFER,d.DEPTH_ATTACHMENT,d.RENDERBUFFER,b);d.bindTexture(d.TEXTURE_2D,null);d.bindRenderbuffer(d.RENDERBUFFER,null);d.bindFramebuffer(d.FRAMEBUFFER,null);this.lb=d.createBuffer();d.bindBuffer(d.ARRAY_BUFFER,this.lb);d.bufferData(d.ARRAY_BUFFER,new Float32Array([-1,-1,0,1,-1,0,-1,1,0,1,1,0]),d.STATIC_DRAW);this.lb.Kc=3;this.lb.Ud=4;this.tf=
d.createBuffer();d.bindBuffer(d.ARRAY_BUFFER,this.tf);d.bufferData(d.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,1,1]),d.STATIC_DRAW);return!0}return!1};f.prototype.Ol=function(d){var b=this.g.H,a=this.g.rb;if(this.rd){b.useProgram(this.oa);b.bindBuffer(b.ARRAY_BUFFER,this.lb);b.vertexAttribPointer(this.oa.$,this.lb.Kc,b.FLOAT,!1,0,0);b.bindBuffer(b.ARRAY_BUFFER,this.tf);b.vertexAttribPointer(this.oa.Ca,2,b.FLOAT,!1,0,0);b.enableVertexAttribArray(this.oa.$);b.enableVertexAttribArray(this.oa.Ca);b.activeTexture(b.TEXTURE0);
b.bindTexture(b.TEXTURE_2D,this.Je);a=1+(this.yb-1)*d;var c=b.getUniformLocation(this.oa,"uAlpha");b.uniform1f(c,1);c=b.getUniformLocation(this.oa,"uZoomIn");b.uniform1i(c,1);c=b.getUniformLocation(this.oa,"uZoomCenter");var k=.5+(this.jh-.5)*Math.sqrt(d),l=.5+(this.kh-.5)*Math.sqrt(d);0>k-.5/a&&(k=.5/a);0>l-.5/a&&(l=.5/a);1<k+.5/a&&(k=1-.5/a);1<l+.5/a&&(l=1-.5/a);b.uniform2f(c,k,l);k=b.getUniformLocation(this.oa,"uZoomFactor");b.uniform1f(k,a);b.uniform1i(b.getUniformLocation(this.oa,"uSampler"),
0);b.drawArrays(b.TRIANGLE_STRIP,0,this.lb.Ud);b.useProgram(this.g.na.F)}else{this.g.Zg();b.blendFuncSeparate(b.SRC_ALPHA,b.ONE_MINUS_SRC_ALPHA,b.SRC_ALPHA,b.ONE);b.enable(b.BLEND);b.disable(b.DEPTH_TEST);k=.5+(this.jh-.5);l=.5+(this.kh-.5);0>k-.5/this.yb&&(k=.5/this.yb);0>l-.5/this.yb&&(l=.5/this.yb);1<k+.5/this.yb&&(k=1-.5/this.yb);1<l+.5/this.yb&&(l=1-.5/this.yb);if("crossdissolve"==this.type)b.useProgram(this.oa),b.bindBuffer(b.ARRAY_BUFFER,this.lb),b.vertexAttribPointer(this.oa.$,this.lb.Kc,
b.FLOAT,!1,0,0),b.bindBuffer(b.ARRAY_BUFFER,this.tf),b.vertexAttribPointer(this.oa.Ca,2,b.FLOAT,!1,0,0),b.activeTexture(b.TEXTURE0),b.bindTexture(b.TEXTURE_2D,this.Je),c=b.getUniformLocation(this.oa,"uAlpha"),b.uniform1f(c,1-d),c=b.getUniformLocation(this.oa,"uZoomIn"),b.uniform1i(c,1==this.Oa||2==this.Oa?1:0),c=b.getUniformLocation(this.oa,"uZoomCenter"),b.uniform2f(c,k,l),k=b.getUniformLocation(this.oa,"uZoomFactor"),b.uniform1f(k,this.yb),b.uniform1i(b.getUniformLocation(this.oa,"uSampler"),0);
else if("diptocolor"==this.type)b.useProgram(this.Ta),b.bindBuffer(b.ARRAY_BUFFER,this.lb),b.vertexAttribPointer(this.Ta.$,this.lb.Kc,b.FLOAT,!1,0,0),b.bindBuffer(b.ARRAY_BUFFER,this.tf),b.vertexAttribPointer(this.Ta.Ca,2,b.FLOAT,!1,0,0),b.activeTexture(b.TEXTURE0),b.bindTexture(b.TEXTURE_2D,this.Je),b.uniform1f(b.getUniformLocation(this.Ta,"uColorPercent"),Math.min(2*d,1)),c=b.getUniformLocation(this.Ta,"uAlpha"),b.uniform1f(c,1-Math.max(2*(d-.5),0)),b.uniform3f(b.getUniformLocation(this.Ta,"uDipColor"),
(this.Re>>16&255)/255,(this.Re>>8&255)/255,(this.Re&255)/255),c=b.getUniformLocation(this.Ta,"uZoomIn"),b.uniform1i(c,1==this.Oa||2==this.Oa?1:0),c=b.getUniformLocation(this.Ta,"uZoomCenter"),b.uniform2f(c,k,l),k=b.getUniformLocation(this.Ta,"uZoomFactor"),b.uniform1f(k,this.yb),b.uniform1i(b.getUniformLocation(this.Ta,"uSampler"),0);else if("irisround"==this.type||"irisroundcenter"==this.type||"irisrectangular"==this.type||"irisrectangularcenter"==this.type){b.useProgram(this.Ha);b.bindBuffer(b.ARRAY_BUFFER,
this.lb);b.vertexAttribPointer(this.Ha.$,this.lb.Kc,b.FLOAT,!1,0,0);b.bindBuffer(b.ARRAY_BUFFER,this.tf);b.vertexAttribPointer(this.Ha.Ca,2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindTexture(b.TEXTURE_2D,this.Je);if(1==this.Oa||2==this.Oa||"irisroundcenter"==this.type||"irisrectangularcenter"==this.type)var e=c=.5;else c=this.jh,e=this.kh;var f=c*a.width,t=e*a.height;f=Math.max(f,a.width-f);t=Math.max(t,a.height-t);"irisround"==this.type||"irisroundcenter"==this.type?b.uniform1f(b.getUniformLocation(this.Ha,
"uRadius"),(Math.sqrt(f*f+t*t)+this.Bc)*d):(f>t?(t=a.height/a.width*f+this.Bc,f+=this.Bc):(f=a.width/a.height*t+this.Bc,t+=this.Bc),b.uniform2f(b.getUniformLocation(this.Ha,"uRectDim"),f*d,t*d));d=b.getUniformLocation(this.Ha,"uSoftEdge");b.uniform1f(d,this.Bc);b.uniform1i(b.getUniformLocation(this.Ha,"uRound"),"irisround"==this.type||"irisroundcenter"==this.type?1:0);b.uniform2f(b.getUniformLocation(this.Ha,"uIrisCenter"),c*a.width,e*a.height);c=b.getUniformLocation(this.Ha,"uZoomIn");b.uniform1i(c,
1==this.Oa||2==this.Oa?1:0);c=b.getUniformLocation(this.Ha,"uZoomCenter");b.uniform2f(c,k,l);k=b.getUniformLocation(this.Ha,"uZoomFactor");b.uniform1f(k,this.yb);b.uniform1i(b.getUniformLocation(this.Ha,"uSampler"),0)}else if("wipeleftright"==this.type||"wiperightleft"==this.type||"wipetopbottom"==this.type||"wipebottomtop"==this.type||"wiperandom"==this.type)b.useProgram(this.Na),b.bindBuffer(b.ARRAY_BUFFER,this.lb),b.vertexAttribPointer(this.Na.$,this.lb.Kc,b.FLOAT,!1,0,0),b.bindBuffer(b.ARRAY_BUFFER,
this.tf),b.vertexAttribPointer(this.Na.Ca,2,b.FLOAT,!1,0,0),b.activeTexture(b.TEXTURE0),b.bindTexture(b.TEXTURE_2D,this.Je),b.uniform1f(b.getUniformLocation(this.Na,"uPercent"),3>this.Vi?d*(a.width+this.Bc):d*(a.height+this.Bc)),d=b.getUniformLocation(this.Na,"uSoftEdge"),b.uniform1f(d,this.Bc),b.uniform1i(b.getUniformLocation(this.Na,"uDirection"),this.Vi),b.uniform2f(b.getUniformLocation(this.Na,"uCanvasDimensions"),a.width,a.height),c=b.getUniformLocation(this.Na,"uZoomIn"),b.uniform1i(c,1==this.Oa||
2==this.Oa?1:0),c=b.getUniformLocation(this.Na,"uZoomCenter"),b.uniform2f(c,k,l),k=b.getUniformLocation(this.Na,"uZoomFactor"),b.uniform1f(k,this.yb),b.uniform1i(b.getUniformLocation(this.Na,"uSampler"),0);b.drawArrays(b.TRIANGLE_STRIP,0,this.lb.Ud);b.useProgram(this.g.na.F);b.disable(b.BLEND);b.enable(b.DEPTH_TEST)}};return f}();p.Rm=f})(ggP2VR||(ggP2VR={}));
(function(p){var f=function(){function f(d){this.Xg=[];this.g=d;this.enabled=!1;this.xb=2;this.zk=!1}f.prototype.Hk=function(d){if(2==d.mode||3==d.mode||5==d.mode){var b=this.g.pa.currentTime,a=d.Wb.gain.value,c=d.Ub.gain.value,k=d.Vb.gain.value;d.oc.gain.linearRampToValueAtTime(d.oc.gain.value,b);d.oc.gain.linearRampToValueAtTime(0,b+this.xb);d.Wb.gain.linearRampToValueAtTime(a,b);d.Wb.gain.linearRampToValueAtTime(0,b+this.xb);d.Ub.gain.linearRampToValueAtTime(c,b);d.Ub.gain.linearRampToValueAtTime(0,
b+this.xb);d.Vb.gain.linearRampToValueAtTime(k,b);d.Vb.gain.linearRampToValueAtTime(0,b+this.xb)}else b=this.g.pa.currentTime,d.cd.gain.linearRampToValueAtTime(d.cd.gain.value,b),d.cd.gain.linearRampToValueAtTime(0,b+this.xb);d.$i=!0;setTimeout(function(){d.Be()},1E3*this.xb+5)};f.prototype.Zp=function(){for(var d=0;d<this.g.N.length;d++){var b=this.g.N[d];-1==this.g.Pc.indexOf(b.id)&&(-1==this.g.Pc.indexOf("_main")||-1!=this.g.ce.indexOf(b.id))&&!this.g.Xb(b.id)&&-1<b.loop&&4!=b.mode&&6!=b.mode&&
(b.la?b.Md():(b.b.play(),b.b.currentTime=0))}};f.prototype.Fn=function(){var d=(this.g.pa.currentTime-this.Vp)/this.xb;d=Math.min(1,d);for(var b=0;b<this.g.N.length;b++){var a=this.g.N[b];this.g.Xb(a.id)&&1>a.ka&&(a.ka=d)}1==d&&clearInterval(this.Up)};return f}();p.Sm=f})(ggP2VR||(ggP2VR={}));
(function(p){var f=function(){function f(d){this.Cg=[];this.Xc=null;this.bc=[];this.Tb=[];this.cc=[];this.Mj=!0;this.g=d;this.wn()}f.prototype.rg=function(){var d=this.g.H,b=d.createShader(d.VERTEX_SHADER);d.shaderSource(b,"attribute vec3 aVertexPosition;\nvoid main(void) {\n gl_Position = vec4(aVertexPosition, 1.0);\n}\n");d.compileShader(b);d.getShaderParameter(b,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(b)),b=null);var a=d.createShader(d.FRAGMENT_SHADER);d.shaderSource(a,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec4 vColor;\nuniform vec2 uCanvasDimensions;\nuniform vec2 uFlareCenterPosition;\nuniform float uBlindingValue;\nuniform float uAspectRatio;\nvoid main(void) {\n float canvasDiag = sqrt( (uCanvasDimensions.x * uCanvasDimensions.x) + (uCanvasDimensions.y * uCanvasDimensions.y) );\n vec2 diff = uFlareCenterPosition - gl_FragCoord.xy;\n diff.y = diff.y * uAspectRatio;\n float distFromFlarePoint = sqrt( (diff.x * diff.x) + (diff.y * diff.y) );\n float factor = (distFromFlarePoint / canvasDiag) / 10.0;\n gl_FragColor = vec4(1.0, 1.0, 1.0, pow(((1.0 - factor) * 0.8) * uBlindingValue, 2.0));\n}\n");
d.compileShader(a);d.getShaderParameter(a,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(a)),a=null);this.ic=d.createProgram();d.attachShader(this.ic,b);d.attachShader(this.ic,a);d.linkProgram(this.ic);d.getProgramParameter(this.ic,d.LINK_STATUS)||alert("Could not initialise shaders");this.ic.$=d.getAttribLocation(this.ic,"aVertexPosition");d.enableVertexAttribArray(this.ic.$);a=d.createShader(d.VERTEX_SHADER);b=d.createShader(d.VERTEX_SHADER);d.shaderSource(a,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nattribute vec3 aVertexPosition;\nvarying vec4 vColor;\nuniform vec2 uCirclePosition;\nuniform float uCircleRadius;\nuniform vec2 uCanvasDimensions2;\nuniform float uAspectRatio;\nvoid main(void) {\n vec2 circleOnScreen = aVertexPosition.xy * uCircleRadius + uCirclePosition;\n circleOnScreen.y = circleOnScreen.y / uAspectRatio;\n vec2 circleNorm = (circleOnScreen / uCanvasDimensions2) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(circleNorm.x, circleNorm.y, 0.0, 1.0);\n}\n");
d.compileShader(a);d.getShaderParameter(a,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(a)),a=null);d.shaderSource(b,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nattribute vec3 aVertexPosition;\nvarying vec4 vColor;\nuniform vec2 uCirclePosition;\nuniform float uCircleRadius;\nuniform vec2 uCanvasDimensions2;\nuniform float uAspectRatio;\nvoid main(void) {\n vec2 circleOnScreen = aVertexPosition.xy * uCircleRadius + uCirclePosition;\n circleOnScreen.y = circleOnScreen.y / uAspectRatio;\n vec2 circleNorm = (circleOnScreen / uCanvasDimensions2) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(circleNorm.x, circleNorm.y, 0.0, 1.0);\n}\n");
d.compileShader(b);d.getShaderParameter(b,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(b)),a=null);var c=d.createShader(d.FRAGMENT_SHADER);d.shaderSource(c,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec4 vColor;\nuniform vec2 uCircleTexturePosition;\nuniform vec3 uCircleColor;\nuniform float uCircleRadius;\nuniform float uCircleAlpha;\nuniform float uCircleSoftness;\nuniform float uAspectRatio;\nvoid main(void) {\n vec2 diff = uCircleTexturePosition - gl_FragCoord.xy;\n diff.y = diff.y * uAspectRatio;\n float distFromCircleCenter = sqrt( (diff.x * diff.x) + (diff.y * diff.y) );\n float softnessDistance = uCircleRadius * (1.0 - uCircleSoftness);\n if (distFromCircleCenter > uCircleRadius)\n {\n\t  gl_FragColor = vec4(uCircleColor, 0.0);\n }\n else if (distFromCircleCenter <= (softnessDistance))\n {\n\t  float factor = distFromCircleCenter / softnessDistance;\n\t  gl_FragColor = vec4(uCircleColor, pow((1.0 - (0.2 * factor)) * uCircleAlpha, 1.8));\n }\n else\n {\n\t  float factor = (distFromCircleCenter - softnessDistance) / (uCircleRadius - softnessDistance);\n\t  gl_FragColor = vec4(uCircleColor, pow((0.8 - (0.8 * factor)) * uCircleAlpha, 1.8));\n }\n}\n");
d.compileShader(c);d.getShaderParameter(c,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(c)),c=null);this.ra=d.createProgram();d.attachShader(this.ra,a);d.attachShader(this.ra,c);d.linkProgram(this.ra);d.getProgramParameter(this.ra,d.LINK_STATUS)||alert("Could not initialise shaders");this.ra.$=d.getAttribLocation(this.ra,"aVertexPosition");d.enableVertexAttribArray(this.ra.$);a=d.createShader(d.FRAGMENT_SHADER);d.shaderSource(a,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec4 vColor;\nuniform vec2 uRingTexturePosition;\nuniform float uRingRadius;\nuniform float uRingAlpha;\nuniform float uAspectRatio;\nuniform sampler2D uSampler;\nvoid main(void) {\n vec2 diff = uRingTexturePosition - gl_FragCoord.xy;\n diff.y = diff.y * uAspectRatio;\n float distFromRingCenter = sqrt( (diff.x * diff.x) + (diff.y * diff.y) );\n float factor = distFromRingCenter / uRingRadius;\n if (distFromRingCenter > uRingRadius)\n {\n\t gl_FragColor = vec4(1.0, 1.0, 1.0, 0.0);\n }\n else\n {\n vec4 textureColor = texture2D(uSampler, vec2(factor / uAspectRatio, 0.5));\n gl_FragColor = vec4(textureColor.x, textureColor.y, textureColor.z, uRingAlpha);\n }\n}\n");
d.compileShader(a);d.getShaderParameter(a,d.COMPILE_STATUS)||(alert(d.getShaderInfoLog(a)),a=null);this.wb=d.createProgram();d.attachShader(this.wb,b);d.attachShader(this.wb,a);d.linkProgram(this.wb);d.getProgramParameter(this.wb,d.LINK_STATUS)||alert("Could not initialise shaders");this.wb.$=d.getAttribLocation(this.wb,"aVertexPosition")};f.prototype.Ic=function(){var d=this.g.H;this.wd=d.createBuffer();d.bindBuffer(d.ARRAY_BUFFER,this.wd);d.bufferData(d.ARRAY_BUFFER,new Float32Array([-1,-1,0,1,
-1,0,1,1,0,-1,1,0]),d.STATIC_DRAW);this.wd.Kc=3;this.wd.Ud=4;this.$e=d.createBuffer();d.bindBuffer(d.ARRAY_BUFFER,this.$e);for(var b=[0,0,0],a=2*Math.PI/6,c=Math.PI/180*35,k=1,l=c;l<=c+2*Math.PI;l+=a)b.push(Math.sin(l)),b.push(-Math.cos(l)),b.push(0),k++;d.bufferData(d.ARRAY_BUFFER,new Float32Array(b),d.STATIC_DRAW);this.$e.Kc=3;this.$e.Ud=k;this.bm=d.createTexture();d.bindTexture(d.TEXTURE_2D,this.bm);d.texParameteri(d.TEXTURE_2D,d.TEXTURE_MIN_FILTER,d.LINEAR);d.texParameteri(d.TEXTURE_2D,d.TEXTURE_MAG_FILTER,
d.LINEAR);d.texParameteri(d.TEXTURE_2D,d.TEXTURE_WRAP_S,d.CLAMP_TO_EDGE);d.texParameteri(d.TEXTURE_2D,d.TEXTURE_WRAP_T,d.CLAMP_TO_EDGE);b=document.createElement("canvas");b.width=100;b.height=1;a=b.getContext("2d");a.width=100;a.height=1;c=a.createLinearGradient(0,0,100,0);c.addColorStop(0,this.g.ga(16777215,0));c.addColorStop(.88,this.g.ga(0,0));c.addColorStop(.9,this.g.ga(16654848,1));c.addColorStop(.92,this.g.ga(16776448,1));c.addColorStop(.94,this.g.ga(4849466,1));c.addColorStop(.96,this.g.ga(131071,
1));c.addColorStop(.98,this.g.ga(8190,1));c.addColorStop(1,this.g.ga(0,0));a.fillStyle=c;a.fillRect(0,0,100,1);d.texImage2D(d.TEXTURE_2D,0,d.RGBA,d.RGBA,d.UNSIGNED_BYTE,b)};f.prototype.np=function(){for(;0<this.Cg.length;)this.Cg.pop()};f.prototype.wn=function(){var d=[],b=[],a=[];var c={m:14,alpha:.2,color:11390415,j:.27};d.push(c);c={m:20,alpha:.25,color:11390415,j:.4};d.push(c);c={m:10,alpha:.2,color:12442332,j:.6};d.push(c);c={m:15,alpha:.2,color:11390415,j:.8};d.push(c);c={m:10,alpha:.2,color:12442332,
j:1.5};d.push(c);c={m:15,alpha:.2,color:11390415,j:1.8};d.push(c);c={m:8,alpha:.2,color:12575203,v:.8,j:.7};b.push(c);c={m:7,alpha:.4,color:12575203,v:.5,j:1.6};b.push(c);c={m:5,alpha:.4,color:12575203,v:.6,j:.9};b.push(c);c={m:8,alpha:.3,color:12575203,v:.4,j:1.1};b.push(c);this.bc.push(d);this.Tb.push(b);this.cc.push(a);d=[];b=[];a=[];c={m:30,alpha:.3,color:11390415,j:.5};d.push(c);c={m:10,alpha:.3,color:11390415,j:1};d.push(c);c={m:20,alpha:.3,color:11390415,j:1.3};d.push(c);c={m:10,alpha:.3,color:11390415,
j:1.5};d.push(c);c={m:15,alpha:.3,color:11390415,j:1.8};d.push(c);c={m:10,alpha:.3,color:15506856,v:.8,j:.7};b.push(c);c={m:20,alpha:.5,color:15506856,v:.5,j:1.6};b.push(c);c={m:5,alpha:.5,color:15506856,v:.6,j:.9};b.push(c);c={m:60,alpha:.4,color:15506856,v:.2,j:1.1};b.push(c);a.push({m:220,alpha:.035,j:2});this.bc.push(d);this.Tb.push(b);this.cc.push(a);d=[];b=[];a=[];c={m:30,alpha:.5,color:15465727,j:.5};d.push(c);c={m:40,alpha:.28,color:15726842,j:.8};d.push(c);c={m:25,alpha:.32,color:15726842,
j:1.1};d.push(c);c={m:15,alpha:.25,color:15726842,j:1.35};d.push(c);c={m:10,alpha:.28,color:15465727,j:1.65};d.push(c);c={m:10,alpha:.45,color:15465727,v:.8,j:.7};b.push(c);c={m:7,alpha:.5,color:15465727,v:.4,j:.9};b.push(c);c={m:40,alpha:.4,color:15465727,v:.3,j:.38};b.push(c);c={m:50,alpha:.4,color:15465727,v:.5,j:1.25};b.push(c);c={m:18,alpha:.2,color:15465727,v:.5,j:1.25};b.push(c);c={m:10,alpha:.34,color:15726842,v:.8,j:1.5};b.push(c);c={m:38,alpha:.37,color:15465727,v:.3,j:-.5};b.push(c);this.bc.push(d);
this.Tb.push(b);this.cc.push(a);d=[];b=[];a=[];c={m:16,alpha:.5,color:16363159,j:.1};d.push(c);c={m:26,alpha:.3,color:16091819,j:.32};d.push(c);c={m:29,alpha:.2,color:16091819,j:1.32};d.push(c);c={m:20,alpha:.18,color:16363159,j:1.53};d.push(c);c={m:27,alpha:.13,color:16425092,j:1.6};d.push(c);c={m:20,alpha:.1,color:16091819,j:1.75};d.push(c);c={m:12,alpha:.45,color:16312238,v:.45,j:.2};b.push(c);c={m:8,alpha:.25,color:16434209,v:.7,j:.33};b.push(c);c={m:9,alpha:.25,color:16091819,v:.4,j:.7};b.push(c);
c={m:7,alpha:.2,color:16091819,v:.4,j:.85};b.push(c);c={m:60,alpha:.23,color:16091819,v:.55,j:1.05};b.push(c);c={m:37,alpha:.1,color:16091819,v:.55,j:1.22};b.push(c);c={m:10,alpha:.25,color:16363159,v:.65,j:1.38};b.push(c);c={m:7,alpha:.2,color:16434209,v:.5,j:1.45};b.push(c);c={m:3,alpha:.2,color:16416033,v:.5,j:1.78};b.push(c);c={m:6,alpha:.18,color:16434209,v:.45,j:1.9};b.push(c);c={m:4,alpha:.14,color:16766514,v:.45,j:2.04};b.push(c);c={m:30,alpha:.14,color:16766514,v:.8,j:.04};b.push(c);this.bc.push(d);
this.Tb.push(b);this.cc.push(a);d=[];b=[];a=[];c={m:9,alpha:.3,color:14346999,v:.3,j:.3};b.push(c);c={m:5,alpha:.5,color:14148072,v:.8,j:.6};b.push(c);c={m:3,alpha:.37,color:14346999,v:.66,j:.8};b.push(c);c={m:45,alpha:.2,color:14346999,v:.36,j:1.2};b.push(c);c={m:13,alpha:.2,color:14346999,v:.36,j:1.23};b.push(c);c={m:11,alpha:.2,color:14148072,v:.36,j:1.28};b.push(c);c={m:27,alpha:.16,color:14346999,v:.36,j:1.55};b.push(c);c={m:6,alpha:.36,color:14148072,v:.8,j:1.7};b.push(c);this.bc.push(d);this.Tb.push(b);
this.cc.push(a);d=[];b=[];a=[];c={m:24,alpha:.2,color:15186464,j:.2};d.push(c);c={m:7,alpha:.26,color:15186464,j:.35};d.push(c);c={m:23,alpha:.18,color:15186464,j:.65};d.push(c);c={m:13,alpha:.2,color:15186464,j:.8};d.push(c);c={m:11,alpha:.15,color:15186464,j:1.4};d.push(c);c={m:15,alpha:.11,color:15451904,j:1.6};d.push(c);c={m:6,alpha:.45,color:15579138,v:.45,j:.22};b.push(c);c={m:3,alpha:.3,color:15451904,v:.25,j:.4};b.push(c);c={m:4,alpha:.2,color:15451904,v:.25,j:.45};b.push(c);c={m:65,alpha:.17,
color:15186464,v:.25,j:.5};b.push(c);c={m:5,alpha:.45,color:15579138,v:.45,j:.88};b.push(c);c={m:140,alpha:.18,color:15579138,v:.32,j:.95};b.push(c);c={m:12,alpha:.22,color:15579138,v:.32,j:1.1};b.push(c);c={m:8,alpha:.32,color:15451904,v:.72,j:1.2};b.push(c);c={m:55,alpha:.2,color:15451904,v:.45,j:1.33};b.push(c);c={m:4,alpha:.3,color:15451904,v:.25,j:1.42};b.push(c);this.bc.push(d);this.Tb.push(b);this.cc.push(a);d=[];b=[];a=[];c={m:16,alpha:.4,color:10933495,j:.32};d.push(c);c={m:14,alpha:.3,color:11007484,
j:.36};d.push(c);c={m:10,alpha:.3,color:4037331,j:.58};d.push(c);c={m:14,alpha:.22,color:8835068,j:.68};d.push(c);c={m:10,alpha:.27,color:11007484,j:.82};d.push(c);c={m:11,alpha:.27,color:10867450,j:1};d.push(c);c={m:9,alpha:.2,color:6158332,j:1.05};d.push(c);c={m:10,alpha:.17,color:10867450,j:1.78};d.push(c);c={m:10,alpha:.3,color:4037331,j:-.23};d.push(c);c={m:8,alpha:.45,color:8835068,v:.45,j:.175};b.push(c);c={m:7,alpha:.4,color:12574715,v:.55,j:.46};b.push(c);c={m:3,alpha:.3,color:10867450,v:.35,
j:.5};b.push(c);c={m:60,alpha:.37,color:4031699,v:.75,j:.75};b.push(c);c={m:3,alpha:.25,color:4031699,v:.25,j:.75};b.push(c);c={m:3,alpha:.2,color:6158332,v:.25,j:.9};b.push(c);c={m:7,alpha:.45,color:8835068,v:.45,j:1.3};b.push(c);c={m:32,alpha:.22,color:8835068,v:.75,j:1.62};b.push(c);c={m:9,alpha:.45,color:4031699,v:.65,j:1.6};b.push(c);c={m:8,alpha:.25,color:4031699,v:.65,j:1.83};b.push(c);c={m:7,alpha:.4,color:12574715,v:.55,j:-.18};b.push(c);this.bc.push(d);this.Tb.push(b);this.cc.push(a);d=
[];b=[];a=[];c={m:16,alpha:.4,color:16389120,j:.32};d.push(c);c={m:26,alpha:.22,color:16389120,j:.4};d.push(c);c={m:26,alpha:.25,color:16389120,j:.65};d.push(c);c={m:18,alpha:.3,color:16389120,j:1.23};d.push(c);c={m:14,alpha:.26,color:16389120,j:1.33};d.push(c);c={m:17,alpha:.18,color:16389120,j:1.7};d.push(c);c={m:30,alpha:.16,color:16389120,j:2.15};d.push(c);c={m:100,alpha:.25,color:16389120,v:.22,j:1.45};b.push(c);c={m:7,alpha:.5,color:15628151,v:.3,j:1.5};b.push(c);c={m:3,alpha:.5,color:15628151,
v:.3,j:1.52};b.push(c);c={m:4,alpha:.5,color:16389120,v:.3,j:1.745};b.push(c);c={m:9,alpha:.22,color:16389120,v:.3,j:1.8};b.push(c);this.bc.push(d);this.Tb.push(b);this.cc.push(a);d=[];b=[];a=[];c={m:16,alpha:.4,color:10933495,j:.32};d.push(c);c={m:14,alpha:.3,color:11007484,j:.36};d.push(c);c={m:10,alpha:.3,color:4037331,j:.58};d.push(c);c={m:14,alpha:.22,color:8835068,j:.68};d.push(c);c={m:10,alpha:.27,color:11007484,j:.82};d.push(c);c={m:11,alpha:.27,color:10867450,j:1};d.push(c);c={m:9,alpha:.2,
color:6158332,j:1.05};d.push(c);c={m:10,alpha:.17,color:10867450,j:1.78};d.push(c);c={m:10,alpha:.3,color:4037331,j:-.23};d.push(c);c={m:8,alpha:.45,color:8835068,v:.45,j:.175};b.push(c);c={m:7,alpha:.4,color:12574715,v:.55,j:.46};b.push(c);c={m:3,alpha:.3,color:10867450,v:.35,j:.5};b.push(c);c={m:60,alpha:.37,color:4031699,v:.75,j:.75};b.push(c);c={m:3,alpha:.25,color:4031699,v:.25,j:.75};b.push(c);c={m:3,alpha:.2,color:6158332,v:.25,j:.9};b.push(c);c={m:7,alpha:.45,color:8835068,v:.45,j:1.3};b.push(c);
c={m:32,alpha:.22,color:8835068,v:.75,j:1.62};b.push(c);c={m:9,alpha:.45,color:4031699,v:.65,j:1.6};b.push(c);c={m:8,alpha:.25,color:4031699,v:.65,j:1.83};b.push(c);c={m:7,alpha:.4,color:12574715,v:.55,j:-.18};b.push(c);this.bc.push(d);this.Tb.push(b);this.cc.push(a);d=[];b=[];a=[];c={m:16,alpha:.4,color:16389120,j:.32};d.push(c);c={m:26,alpha:.22,color:16389120,j:.4};d.push(c);c={m:26,alpha:.25,color:16389120,j:.65};d.push(c);c={m:18,alpha:.3,color:16389120,j:1.23};d.push(c);c={m:14,alpha:.26,color:16389120,
j:1.33};d.push(c);c={m:17,alpha:.18,color:16389120,j:1.7};d.push(c);c={m:30,alpha:.16,color:16389120,j:2.15};d.push(c);c={m:100,alpha:.25,color:16389120,v:.22,j:1.45};b.push(c);c={m:7,alpha:.5,color:15628151,v:.3,j:1.5};b.push(c);c={m:3,alpha:.5,color:15628151,v:.3,j:1.52};b.push(c);c={m:4,alpha:.5,color:16389120,v:.3,j:1.745};b.push(c);c={m:9,alpha:.22,color:16389120,v:.3,j:1.8};b.push(c);this.bc.push(d);this.Tb.push(b);this.cc.push(a);d=[];b=[];a=[];c={m:24,alpha:.2,color:15186464,j:.2};d.push(c);
c={m:7,alpha:.26,color:15186464,j:.35};d.push(c);c={m:23,alpha:.18,color:15186464,j:.65};d.push(c);c={m:13,alpha:.2,color:15186464,j:.8};d.push(c);c={m:11,alpha:.15,color:15186464,j:1.4};d.push(c);c={m:15,alpha:.11,color:15451904,j:1.6};d.push(c);c={m:6,alpha:.45,color:15579138,v:.45,j:.22};b.push(c);c={m:3,alpha:.3,color:15451904,v:.25,j:.4};b.push(c);c={m:4,alpha:.2,color:15451904,v:.25,j:.45};b.push(c);c={m:65,alpha:.17,color:15186464,v:.25,j:.5};b.push(c);c={m:5,alpha:.45,color:15579138,v:.45,
j:.88};b.push(c);c={m:140,alpha:.18,color:15579138,v:.32,j:.95};b.push(c);c={m:12,alpha:.22,color:15579138,v:.32,j:1.1};b.push(c);c={m:8,alpha:.32,color:15451904,v:.72,j:1.2};b.push(c);c={m:55,alpha:.2,color:15451904,v:.45,j:1.33};b.push(c);c={m:4,alpha:.3,color:15451904,v:.25,j:1.42};b.push(c);this.bc.push(d);this.Tb.push(b);this.cc.push(a)};f.prototype.Zo=function(){if(this.Mj){var d=this.g.H,b,a=new p.xa(0,0,-100),c=this.g.dd(),k=0,l=0,e=!1;if(this.g.Z){var f=this.g.rb.width;var t=this.g.rb.height;
this.g.B.Wg&&(f=this.g.B.dc.width,t=this.g.B.dc.height)}else{this.S||(this.S=this.Xc.getContext("2d"));if(this.S.width!==this.g.o.width||this.S.height!==this.g.o.height)this.S.width=this.g.o.width,this.S.height=this.g.o.height;this.S.clear?this.S.clear():this.S.clearRect(0,0,this.Xc.width,this.Xc.height);f=this.S.width;t=this.S.height}var g=Math.sqrt(f*f+t*t),h=g/800;for(b=0;b<this.Cg.length;b++){var n=this.Cg[b];a.Za(0,0,-100);a.wa(-n.i*Math.PI/180);a.Ea(n.pan*Math.PI/180);a.Ea(-this.g.pan.c*Math.PI/
180);a.wa(this.g.i.c*Math.PI/180);a.mb(this.g.O.c*Math.PI/180);if(-.01>a.z){l=-c/a.z;k=a.x*l;l*=a.y;var y=Math.max(f,t);Math.abs(k)<y/2+100&&Math.abs(l)<y/2+100&&(e=!0,k+=f/2,l+=t/2)}if(e){this.g.Z&&(d.blendFunc(d.SRC_ALPHA,d.DST_ALPHA),d.enable(d.BLEND),d.disable(d.DEPTH_TEST));y=f/2;var r=t/2;var v=Math.sqrt((y-k)*(y-k)+(r-l)*(r-l));var u=g/2;r=f>t?f:t;y=n.pk/100*((u-v)/u);0>y&&(y=0);if(this.g.Z){d.useProgram(this.ic);d.bindBuffer(d.ARRAY_BUFFER,this.g.B.lb);d.vertexAttribPointer(this.ic.$,this.g.B.lb.Kc,
d.FLOAT,!1,0,0);var x=d.getUniformLocation(this.ic,"uCanvasDimensions");d.uniform2f(x,d.drawingBufferWidth,d.drawingBufferHeight);d.uniform2f(d.getUniformLocation(this.ic,"uFlareCenterPosition"),d.drawingBufferWidth/f*k,t-d.drawingBufferHeight/t*l);d.uniform1f(d.getUniformLocation(this.ic,"uBlindingValue"),y);x=d.getUniformLocation(this.ic,"uAspectRatio");d.uniform1f(x,this.g.B.Wg?d.drawingBufferWidth/d.drawingBufferHeight:d.drawingBufferWidth/d.drawingBufferHeight/(f/t));d.drawArrays(d.TRIANGLE_STRIP,
0,this.g.B.lb.Ud)}else x=this.S.createRadialGradient(k,l,1,k,l,r),x.addColorStop(0,"rgba(255, 255, 255, "+y+")"),x.addColorStop(.5,"rgba(255, 255, 255, "+.8*y+")"),x.addColorStop(1,"rgba(255, 255, 255, "+.6*y+")"),this.S.fillStyle=x,this.S.fillRect(0,0,this.S.width,this.S.height);if(0!=Number(n.type)&&!this.g.B.Wg){y=f/2-k;r=t/2-l;var A=1,w=Number(n.type)-1;v<.35*u&&(A=v/(.35*u),A*=A);v>.7*u&&(A=(u-v)/(.3*u));A*=n.alpha/100;if(0<this.bc[w].length)for(v=0;v<this.bc[w].length;v++){var B=this.bc[w][v];
u=B.m*h;var z=B.alpha*A;0>z&&(z=0);var C=B.color;if(8==w||9==w||10==w)C=n.color;if(this.g.Z)d.useProgram(this.ra),d.bindBuffer(d.ARRAY_BUFFER,this.$e),d.vertexAttribPointer(this.ra.$,this.$e.Kc,d.FLOAT,!1,0,0),x=d.getUniformLocation(this.ra,"uCanvasDimensions2"),d.uniform2f(x,d.drawingBufferWidth,d.drawingBufferHeight),d.uniform2f(d.getUniformLocation(this.ra,"uCirclePosition"),d.drawingBufferWidth/f*(k+y*B.j),d.drawingBufferWidth/f*(t-(l+r*B.j))),d.uniform2f(d.getUniformLocation(this.ra,"uCircleTexturePosition"),
d.drawingBufferWidth/f*(k+y*B.j),t-(l+r*B.j)),d.uniform1f(d.getUniformLocation(this.ra,"uCircleRadius"),u),d.uniform3f(d.getUniformLocation(this.ra,"uCircleColor"),(C>>16&255)/255,(C>>8&255)/255,(C&255)/255),d.uniform1f(d.getUniformLocation(this.ra,"uCircleAlpha"),z),d.uniform1f(d.getUniformLocation(this.ra,"uCircleSoftness"),.1),x=d.getUniformLocation(this.ra,"uAspectRatio"),d.uniform1f(x,d.drawingBufferWidth/d.drawingBufferHeight/(f/t)),d.drawArrays(d.TRIANGLE_FAN,0,this.$e.Ud);else{this.S.save();
this.S.translate(k+y*B.j,l+r*B.j);x=this.S.createRadialGradient(0,0,1,0,0,1.1*u);x.addColorStop(0,this.g.ga(C,z));x.addColorStop(.65,this.g.ga(C,.9*z));x.addColorStop(.8,this.g.ga(C,.7*z));x.addColorStop(1,this.g.ga(C,.2*z));this.S.beginPath();C=2*Math.PI/6;B=Math.PI/180*35;var D=!0;for(z=B;z<=B+2*Math.PI;z+=C)D?(this.S.moveTo(u*Math.sin(z),u*Math.cos(z)),D=!1):this.S.lineTo(u*Math.sin(z),u*Math.cos(z));this.S.closePath();this.S.fillStyle=x;this.S.fill();this.S.restore()}}if(0<this.Tb[w].length)for(v=
0;v<this.Tb[w].length;v++){B=this.Tb[w][v];u=B.m*h;z=B.alpha*A;0>z&&(z=0);C=B.color;if(8==w||9==w||10==w)C=n.color;this.g.Z?(d.useProgram(this.ra),d.bindBuffer(d.ARRAY_BUFFER,this.wd),d.vertexAttribPointer(this.ra.$,this.wd.Kc,d.FLOAT,!1,0,0),x=d.getUniformLocation(this.ra,"uCanvasDimensions2"),d.uniform2f(x,d.drawingBufferWidth,d.drawingBufferHeight),x=d.getUniformLocation(this.ra,"uCirclePosition"),d.uniform2f(x,d.drawingBufferWidth/f*(k+y*B.j),d.drawingBufferWidth/f*(t-(l+r*B.j))),x=d.getUniformLocation(this.ra,
"uCircleTexturePosition"),d.uniform2f(x,d.drawingBufferWidth/f*(k+y*B.j),t-(l+r*B.j)),x=d.getUniformLocation(this.ra,"uCircleRadius"),d.uniform1f(x,u),d.uniform3f(d.getUniformLocation(this.ra,"uCircleColor"),(C>>16&255)/255,(C>>8&255)/255,(C&255)/255),d.uniform1f(d.getUniformLocation(this.ra,"uCircleAlpha"),z),d.uniform1f(d.getUniformLocation(this.ra,"uCircleSoftness"),B.v),x=d.getUniformLocation(this.ra,"uAspectRatio"),d.uniform1f(x,d.drawingBufferWidth/d.drawingBufferHeight/(f/t)),d.drawArrays(d.TRIANGLE_FAN,
0,this.wd.Ud)):(this.S.save(),this.S.translate(k+y*B.j,l+r*B.j),x=this.S.createRadialGradient(0,0,1,0,0,u),x.addColorStop(0,this.g.ga(C,z)),x.addColorStop(1-B.v,this.g.ga(C,.8*z)),x.addColorStop(1,this.g.ga(C,0)),this.S.beginPath(),this.S.arc(0,0,u,0,2*Math.PI,!1),this.S.closePath(),this.S.fillStyle=x,this.S.fill(),this.S.restore())}if(0<this.cc[w].length)for(v=0;v<this.cc[w].length;v++)n=this.cc[w][v],u=n.m*h,z=n.alpha*A,0>z&&(z=0),this.g.Z?(d.useProgram(this.wb),d.activeTexture(d.TEXTURE0),d.bindTexture(d.TEXTURE_2D,
this.bm),d.bindBuffer(d.ARRAY_BUFFER,this.wd),d.vertexAttribPointer(this.wb.$,this.wd.Kc,d.FLOAT,!1,0,0),x=d.getUniformLocation(this.wb,"uCanvasDimensions2"),d.uniform2f(x,f,t),x=d.getUniformLocation(this.wb,"uCirclePosition"),d.uniform2f(x,k+y*n.j,t-(l+r*n.j)),x=d.getUniformLocation(this.wb,"uRingTexturePosition"),d.uniform2f(x,d.drawingBufferWidth/f*(k+y*n.j),t-(l+r*n.j)),x=d.getUniformLocation(this.wb,"uCircleRadius"),d.uniform1f(x,u),d.uniform2f(d.getUniformLocation(this.wb,"uRingPosition"),k+
y*n.j,t-(l+r*n.j)),d.uniform1f(d.getUniformLocation(this.wb,"uRingRadius"),u),d.uniform1f(d.getUniformLocation(this.wb,"uRingAlpha"),z),x=d.getUniformLocation(this.wb,"uAspectRatio"),d.uniform1f(x,d.drawingBufferWidth/d.drawingBufferHeight/(f/t)),d.uniform1i(d.getUniformLocation(this.wb,"uSampler"),0),d.drawArrays(d.TRIANGLE_FAN,0,this.wd.Ud)):(this.S.save(),this.S.translate(k+y*n.j,l+r*n.j),x=this.S.createRadialGradient(0,0,0,0,0,u),x.addColorStop(0,this.g.ga(16777215,0)),x.addColorStop(.88,this.g.ga(0,
0)),x.addColorStop(.9,this.g.ga(16654848,z)),x.addColorStop(.92,this.g.ga(16776448,z)),x.addColorStop(.94,this.g.ga(4849466,z)),x.addColorStop(.96,this.g.ga(131071,z)),x.addColorStop(.98,this.g.ga(8190,z)),x.addColorStop(1,this.g.ga(0,0)),this.S.beginPath(),this.S.arc(0,0,u,0,2*Math.PI,!1),this.S.closePath(),this.S.fillStyle=x,this.S.fill(),this.S.restore())}this.g.Z&&(d.useProgram(this.g.na.F),d.disable(d.BLEND),d.enable(d.DEPTH_TEST))}}}};return f}();p.Qm=f})(ggP2VR||(ggP2VR={}));var W=!1;
(function(p){var f=function(){return function(){this.f=this.i=this.pan=0}}(),h=function(){return function(){this.Qi=-1E7}}(),d=function(){function b(a,c){this.Io="TGljZW5zZTogQktZTS5DT00=";this.pan={c:0,Qa:0,min:0,max:360,d:0,xj:0,Fc:0};this.i={c:0,Qa:0,min:-90,max:90,d:0,Fc:0};this.O={c:0,Qa:0,min:-180,max:180,d:0};this.wc={pan:0,i:-90,O:0,f:170,Eb:9};this.f={c:70,Qa:70,min:1,Gg:0,max:170,uj:360,vj:270,pf:0,d:0,mode:0,nm:0,Bk:0};this.$a={O:0,pitch:0};this.o={width:10,height:10};this.kb=0;this.Ti=new p.xa;this.crossOrigin=
"anonymous";this.Ka=this.ai=4;this.fd=this.ih=this.Jg=this.rc=0;this.Km={};this.X={start:{x:0,y:0},ea:{x:0,y:0},Cd:{x:0,y:0},c:{x:0,y:0},da:{x:0,y:0}};this.R={Xa:!1,Jk:0,startTime:0,start:{x:0,y:0},ea:{x:0,y:0},Cd:{x:0,y:0},c:{x:0,y:0},da:{x:0,y:0}};this.Ze=!0;this.Ba={enabled:!0,ea:{x:0,y:0},da:{x:0,y:0},Hj:0,f:{active:!1,mc:0}};this.jb={alpha:0,beta:0,gamma:0,orientation:0,Pl:0,hi:!0,cg:0};this.og={alpha:0,beta:0,gamma:0,orientation:0};this.s={src:[],Qe:4,width:640,height:480,hd:!1,Kh:!1,Pj:!0,
om:!1,me:"loop",b:HTMLVideoElement=null,sm:!1,sc:WebGLTexture=null,bk:WebGLBuffer=null,ri:WebGLBuffer=null,mj:WebGLBuffer=null,format:1,Dh:0,ej:1};this.Nh=0;this.ia=this.ya=this.Fa=this.T=this.lc=this.bb=this.D=null;this.oe="pano";this.dj="flashcontainer";this.Ui="";this.control=null;this.Sb=[];this.Da=!1;this.$f=1;this.ja=null;this.Jd=this.Ne=this.df=!1;this.Qf=0;this.td=.02;this.Fi=0;this.Gi=!1;this.Ei=this.nh=this.Rf=this.Me=this.nk=0;this.mk=-1;this.Lb="";this.uf=this.Ac=!1;this.bi=0;this.th=
[];this.We=[];this.Gf=this.uc=1;this.Uf=1024;this.vf=!1;this.we=200;this.Pb=0;this.Vg=5;this.tc=0;this.tm=50;this.lj=this.um=0;this.l={enabled:!1,timeout:5,active:!1,Ug:!1,speed:.4,ti:0,Yh:0,wj:!0,eh:!1,Kf:!1,oh:"",Kd:"Animation01",Pe:!1,Uh:!1,Sj:!1,startTime:0,jd:0,eg:!1,uh:!1,Ph:0,Bd:0,Ag:0,Bg:0,zg:0,Dl:0};this.u={active:!1,aborted:!1,speed:.1,pan:0,i:0,O:0,f:70,Hd:70,Ag:0,Bg:0,El:0,zg:0,Eb:0,Mg:0,pj:0,$j:!1,Hg:!1,lk:0,kk:0,Fh:!1};this.jf={pan:-1,i:-1,f:-1};this.Bl=0;this.ba=null;this.Lf={};this.fh=
{};this.Od=[];this.Sh={};this.zc={};this.lf={};this.A={mode:1,wg:-1,qa:0,ab:0,Wc:.05,kc:255,jc:1,hc:255,gc:.3,cf:!0,Uj:{enabled:!0,width:180,height:20,Vj:0,Tj:1,background:!0,hc:16777215,gc:1,kc:0,jc:1,Ii:3,Ji:1,Bi:!0},nb:[],Ob:[],Oc:[],ni:[]};this.Aa=null;this.P=[];this.N=[];this.I=[];this.Sa=[];this.Ie=[];this.Ma=[];this.za=[];this.Pc=[];this.ce=[];this.Ee=[];this.V=1;this.na=this.md=this.Gb=this.Nd=null;this.ag={};this.pd={};this.h=new p.Tm;this.Yo={target:0,current:0,Wc:.01,Bn:2,Yi:0,vh:!1,gn:!1};
this.margin={left:0,top:0,right:0,bottom:0};this.C={ue:!1,Jo:!1,Ab:!1,kd:!1,Ad:!0,pl:!1,mm:1,dm:!1,Wi:!0,Zf:!0,rh:!1,ef:!1,em:!0,sensitivity:8};this.Fg=[];this.$c=!0;this.ua={x:0,y:0};this.dh=this.Fb=this.$g=this.Sc=this.Z=!1;this.zi=this.Fm=!0;this.kj=!1;this.ne=!0;this.ah=this.jj=!1;this.Gm=!0;this.Ia=this.Ld="";this.bd="transition";this.Ra="transform";this.vd="perspective";this.Ek="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAA5JREFUeNpiYBgeACDAAADIAAE3iTbkAAAAAElFTkSuQmCC";
this.rb={width:0,height:0};this.Nk=new p.xa;this.Mk=new p.xa;this.Ok=new p.xa;this.Pk=new p.xa;this.Lk=new p.xa;this.gf=!1;this.Cl=this.Wa="";this.ck=[];this.vi=[];this.tg=this.rl=this.sg=this.sl=this.Sd=this.nj=this.Ij=this.Jc=this.Ih=this.ql=this.oj=this.tl=this.ul=this.xl=!1;this.bh=!0;this.gi=this.Hh=!1;this.ll=[];this.devicePixelRatio=1;this.ha=this.B=null;this.Vf=!1;this.La=null;this.qb={enabled:!1,speed:1,nd:!1,bj:!0};this.Ll=!1;this.Rh="<<LOG>>";this.pb=new p.Um;this.Zj=!1;this.Yf=function(a,
c){if(0==a.length)return a;var b;var d=[];var k=c.ci(a[0])-0;for(b=0;b<a.length;b++){var e=b;var l=b+1;l==a.length&&(l=0);var f=c.ci(a[l])-0;if(0<=k&&0<=f)d.push(a[e]);else if(0<=k||0<=f){var h=f/(f-k);0>h&&(h=0);1<h&&(h=1);var v=new p.xa;v.zd(a[e],a[l],h);0>k||d.push(a[e]);d.push(v)}k=f}return d};this.Tl=new f;this.Oj=0;this.Oh=-1;this.jg=function(a){return a?a.pageX||a.pageY?{x:a.pageX,y:a.pageY}:a.clientX||a.clientY?{x:a.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,y:a.clientY+
document.body.scrollTop+document.documentElement.scrollTop}:a.touches&&a.touches[0]?{x:a.touches[0].pageX,y:a.touches[0].pageY}:{x:0,y:0}:{x:0,y:0}};this.Wh=1;this.Gn=this.Ah=this.zl=this.Zi=this.Dj=this.di=0;this.Al=!1;this.Qd=!0;this.eb=new p.lh(this);this.eb.cf=!1;this.Dk();if(7<this.Rh.length){var d=b.Sf("bG9n");window[b.Sf("Y29uc29sZQ==")][d](b.qk(this.Rh))}this.xf(this.eb);this.checkLoaded=this.Sb;this.isLoaded=!1;c&&c.hasOwnProperty("useFlash")&&c.useFlash&&(this.Fb=!0,this.Z=this.Sc=!1,c.hasOwnProperty("flashPlayerId")?
this.oe=c.flashPlayerId:this.oe="pano",c.hasOwnProperty("flashContainerId")?this.dj=c.flashContainerId:this.dj=a+"flash");c&&c.hasOwnProperty("webGLFlags")&&c.webGLFlags&&(this.Km=c.webGLFlags);this.va();this.Fb||(this.Ga=new p.Qm(this));this.yk(a);this.fn();this.userdata=this.Lf=this.bg();this.emptyHotspot=this.eb;this.mouse=this.ua;this.B=new p.Rm(this);this.La=new p.Sm(this);this.na=new p.Ym(this);this.Rc()}b.prototype.el=function(a){return-99991===a?this.Io:"6.0.4/17319"};b.prototype.Ri=
function(){this.B.enabled=this.ha.enabled;this.B.type=this.ha.type;this.B.Oa=this.ha.zoomin;this.B.ec=this.ha.zoomout;this.B.Tf=this.ha.blendtime;this.B.Pf=this.ha.zoomoutpause;this.B.Of=this.ha.zoomfov;this.B.de=this.ha.zoomspeed;this.B.Re=this.ha.blendcolor;this.B.Bc=this.ha.softedge;this.ha=null};b.prototype.Kp=function(a){this.ha={};this.ha.enabled=!0;this.ha.type=this.B.type;this.ha.zoomin=this.B.Oa;this.ha.zoomout=this.B.ec;this.ha.blendtime=this.B.Tf;this.ha.zoomoutpause=this.B.Pf;this.ha.zoomfov=
this.B.Of;this.ha.zoomspeed=this.B.de;this.ha.blendcolor=this.B.Re;this.ha.softedge=this.B.Bc;if(a.hasOwnProperty("type")){var c=a.type;if("cut"==c||"crossdissolve"==c||"diptocolor"==c||"irisround"==c||"irisrectangular"==c||"wipeleftright"==c||"wiperightleft"==c||"wipetopbottom"==c||"wipebottomtop"==c||"wiperandom"==c)this.ha.type=c}a.hasOwnProperty("before")&&(c=Number(a.before),0==c||2==c)&&(this.ha.zoomin=c);a.hasOwnProperty("after")&&(c=Number(a.after),0==c||2==c||3==c||4==c)&&(this.ha.zoomout=
c);a.hasOwnProperty("transitiontime")&&(c=Number(a.transitiontime),0<=c&&50>=c&&(this.ha.blendtime=c));a.hasOwnProperty("waitfortransition")&&(this.ha.zoomoutpause=1==a.waitfortransition);a.hasOwnProperty("zoomedfov")&&(c=Number(a.zoomedfov),.01<=c&&50>=c&&(this.ha.zoomfov=c));a.hasOwnProperty("zoomspeed")&&(c=Number(a.zoomspeed),.01<=c&&99>=c&&(this.ha.zoomspeed=c));a.hasOwnProperty("dipcolor")&&(this.ha.blendcolor=a.dipcolor);a.hasOwnProperty("softedge")&&(a=Number(a.softedge),0<=a&&1E3>=a&&(this.ha.softedge=
a));this.Vf||this.Ri()};b.prototype.Nc=function(a,c,b){var d=c?Number(c):0;if(0!=a&&4!=a&&12!=a&&9!=a)this.Lc("Unsupported projection type: "+a);else if(c&&0!==d&&4!==d&&12!==d&&9!==d)this.Lc("Unsupported projection2 type: "+d);else if(a==d&&(d=0),this.Jg=b?Number(b):1,this.Ka!=a||this.rc!=d)this.Ka=a,this.rc=d,this.na.Gh()};b.prototype.sa=function(){return 0==this.Ka?4:this.Ka};b.prototype.Ni=function(a,c){if(0!=a&&4!=a&&12!=a&&9!=a)this.Lc("Unsupported projection type: "+a);else if(this.Z||0==a||
4==a||this.Lc("Projection changes require WebGL!"),this.sa()!=a){var b={};b.pan=this.pan.c;b.tilt=this.i.c;b.fov=this.f.c;b.projection=a;b.timingFunction=3;b.speed=c;a=this.ig(a);b.fov=Math.min(a,b.fov);this.Vh(b)}};b.prototype.addListener=function(a,c){(this.ag[a]=this.ag[a]||[]).push(c)};b.prototype.Y=function(a,c){if(a=this.ag[a])for(var b=a.length,d=0;d<b;d++)try{a[d].apply(null,[c])}catch(e){this.Lc(e)}};b.prototype.removeEventListener=function(a,c){var b=this.ag[a];if(b){var d;var e=0;for(d=
b.length;e<d;e++)if(b[e]===c){1===d?delete this.ag[a]:b.splice(e,1);break}}};b.prototype.op=function(){this.Da=!0};b.prototype.zo=function(){return this.H};b.prototype.bn=function(a,c,b,d){if(!this.pd.hasOwnProperty(a)){var k=new h;this.pd[a]=k;k.type=c;"undefined"!==typeof b&&this.Zd(a,b);"object"===typeof d&&this.im(a,d);return!0}return!1};b.prototype.im=function(a,c){if(this.pd.hasOwnProperty(a)&&"object"===typeof c){var b=this.pd[a];c.hasOwnProperty("cookiePath")&&(b.wk=String(c.cookiePath));
c.hasOwnProperty("cookieExpireDays")&&(b.Qi=parseFloat(c.cookieExpireDays));if(c.hasOwnProperty("keep")&&(b.yl=!!c.keep,b.yl)){var d="ggvar_"+a;0<document.cookie.length&&(c=document.cookie.indexOf(d+"="),-1!=c&&(c=c+d.length+1,d=document.cookie.indexOf(";",c),-1==d&&(d=document.cookie.length),c=decodeURIComponent(document.cookie.substring(c,d)),0==b.type&&this.Zd(a,c),1==b.type&&this.Zd(a,parseFloat(c)),2==b.type&&this.Zd(a,"true"==c)))}return!0}return!1};b.prototype.Zd=function(a,c){if(this.pd.hasOwnProperty(a)){var b=
this.pd[a];if(0==b.type&&"string"===typeof c||1==b.type&&"number"===typeof c||2==b.type&&"boolean"===typeof c){if(b.yl){var d="ggvar_"+a+"="+encodeURIComponent(c.toString());-1<=b.Qi&&(d+="; max-age="+86400*b.Qi);b.wk&&(d+="; path="+b.wk);document.cookie=d}b.value!=c&&(b.value=c,this.Y("varchanged_"+a,{value:c}));return!0}}return!1};b.prototype.to=function(a){if(this.pd.hasOwnProperty(a))return this.pd[a].value};b.Sf=function(a){var c="",b=0;a=a.replace(/[^A-Za-z0-9\+\/=]/g,"");do{var d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(b++));
var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(b++));var f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(b++));var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(b++));d=d<<2|e>>4;e=(e&15)<<4|f>>2;var g=(f&3)<<6|t;c+=String.fromCharCode(d);64!=f&&(c+=String.fromCharCode(e));64!=t&&(c+=String.fromCharCode(g))}while(b<a.length);return c};b.En=function(a){for(var c=[1,1,1,1,2,2,
3,0],b=a.length,d="",e=0;e<b;){var f=a.charCodeAt(e++);if(f&128){var t=c[f>>3&7];if(!(f&64&&t)||e+t>b)return null;for(f&=63>>t;0<t;--t){var g=a.charCodeAt(e++);if(128!=(g&192))return null;f=f<<6|g&63}}d+=String.fromCharCode(f)}return d};b.qk=function(a){return b.En(b.Sf(a))};b.prototype.Dk=function(){this.devicePixelRatio=window.devicePixelRatio||1;this.xl=!!navigator.userAgent.match(/(Windows|Win)/g);this.ul=!!navigator.userAgent.match(/(Mac|Macintosh|Mac_powerpc)/g);this.tl=!!navigator.userAgent.match(/(Linux|Ubuntu)/g);
this.oj=!!navigator.userAgent.match(/(MSIE)/g);this.ql=!!navigator.userAgent.match(/(Edge|EdgA)/g);this.Ih=!!navigator.userAgent.match(/(Firefox)/g);if(this.Jc=!!navigator.userAgent.match(/(Safari)/g)){var a=navigator.userAgent.indexOf("Safari");this.od=navigator.userAgent.substring(a+7);a=navigator.userAgent.indexOf("Version");-1!=a&&(this.od=navigator.userAgent.substring(a+8));this.od=this.od.substring(0,this.od.indexOf(" "));this.od=this.od.substring(0,this.od.indexOf("."));this.Ij=!0}if(this.nj=
!!navigator.userAgent.match(/(Chrome)/g))this.Jc=!1;this.Sd=!!navigator.userAgent.match(/(iPad|iPhone|iPod)/g);this.sl=!!navigator.userAgent.match(/(iPhone|iPod)/g);this.sg=!!navigator.userAgent.match(/(android)/i);this.rl=!!navigator.userAgent.match(/(IEMobile)/i);this.tg=this.Sd||this.sg||this.rl;/iP(hone|od|ad)/.test(navigator.platform)&&(a=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/),this.ll=[parseInt(a[1],10),parseInt(a[2],10),parseInt(a[3]||"0",10)]);this.Hh=!window.requestAnimationFrame;
a=["Webkit","Moz","O","ms","Ms"];this.Ia="";this.bd="transition";this.Ra="transform";this.vd="perspective";for(var c=0;c<a.length;c++)"undefined"!==typeof document.documentElement.style[a[c]+"Transform"]&&(this.Ia="-"+a[c].toLowerCase()+"-",this.bd=a[c]+"Transition",this.Ra=a[c]+"Transform",this.vd=a[c]+"Perspective");this.kj=U();this.Z=V();this.Sc=this.kj;this.Z&&(this.Sc=!1);this.Ac=!0;this.uf=!1;if(this.Sd||this.sg)this.hm(80),this.Vg=2;this.Lc("Pano2VR player "+this.el()+" - Prefix:"+this.Ia+
", "+(this.kj?"CSS 3D available":"CSS 3D not available")+", "+(this.Z?"WebGL available":"WebGL not available"));W&&this.M("Pano2VR Debug version!");try{window.AudioContext=window.AudioContext||window.webkitAudioContext,this.pa=new AudioContext}catch(k){this.pa=null}this.Jc&&(!this.Ij||9>Number(this.od))&&(this.pa=null);this.Jc&&!this.Sd&&12<=Number(this.od)&&(this.pa=null);this.bh=this.sl?this.Jc&&this.Ij&&10<=Number(this.od)?!0:!1:!0};b.prototype.M=function(a){if(W){var c=document.getElementById("gg_debug");
c&&(c.innerHTML=a.toString()+"<br />");window.console&&window.console.log(a)}};b.prototype.Lc=function(a){var c=document.getElementById("gg_debug");c&&(c.innerHTML=a+"<br />");window.console&&window.console.log(a)};b.prototype.hm=function(a){this.we=a};b.prototype.tp=function(a){this.crossOrigin=a};b.prototype.vp=function(a){this.ff=a};b.prototype.lo=function(){return this.bi};b.prototype.sp=function(a){this.Ld=a};b.prototype.Kn=function(){return this.Ld};b.prototype.Tn=function(){return this.tg};
b.prototype.Un=function(){return this.gf};b.prototype.Qn=function(){return this.l.active};b.prototype.wp=function(a){this.tg=!!a};b.prototype.Bh=function(){return this.isLoaded};b.prototype.Sn=function(){return!this.isLoaded};b.prototype.dd=function(){return Number(this.o.height)/(2*Math.tan(Math.PI/180*(this.Ib()/2)))};b.prototype.jm=function(a,c){this.isFullscreen&&(a=window.innerWidth,c=window.innerHeight);var b=a-this.margin.left-this.margin.right,d=c-this.margin.top-this.margin.bottom;if(!(10>
b||10>d)){var e=window.devicePixelRatio||1;this.vf&&(e=1);this.D.style.width=b+"px";this.D.style.height=d+"px";this.D.style.left=this.margin.left+"px";this.D.style.top=this.margin.top+"px";if(this.Z)try{this.bb&&(this.bb.style.position="absolute",this.bb.style.display="inline",this.bb.style.width=b+"px",this.bb.style.height=d+"px",this.bb.width=b*e,this.bb.height=d*e),this.H&&(this.rb.width=b*e,this.rb.height=d*e,this.H.viewport(0,0,this.H.drawingBufferWidth,this.H.drawingBufferHeight))}catch(m){alert(m)}this.lc&&
(this.lc.style.width=a+"px",this.lc.style.height=c+"px",this.lc.width=a,this.lc.height=c);this.Fa&&(this.Fa.style.width=a+"px",this.Fa.style.height=c+"px",this.ya.style.width=a+"px",this.ya.style.height=c+"px",this.ya.width=a,this.ya.height=c,this.ya.style.left=this.margin.left+"px",this.ya.style.top=this.margin.top+"px",this.ja&&this.ja!=this.Fa&&(this.ja.style.width=a+"px",this.ja.style.height=c+"px"));this.Ga&&(b=this.Ga.Xc,b.style.width=a+"px",b.style.height=c+"px",b.width=a,b.height=c,b.style.left=
this.margin.left+"px",b.style.top=this.margin.top+"px");this.df&&(this.Da=!0);b=this.D.offsetWidth;d=this.D.offsetHeight;if(this.o.width!=b||this.o.height!=d)this.o.width=b,this.o.height=d;this.tq();this.ja&&this.ja.ggUpdateSize&&this.ja.ggUpdateSize(a,c);this.Y("sizechanged",{hh:a,pg:c})}};b.prototype.Ke=function(){this.Zj=!0};b.prototype.Rc=function(){this.jm(this.Nd.offsetWidth,this.Nd.offsetHeight)};b.prototype.yo=function(){var a={width:0,height:0};a.width=this.o.width;a.height=this.o.height;
return a};b.prototype.qe=function(){var a={x:0,y:0},c=this.D;if(c.offsetParent){do a.x+=c.offsetLeft,a.y+=c.offsetTop,c=c.offsetParent;while(c)}return a};b.prototype.Ip=function(a){this.ba=a};b.prototype.yp=function(a,c,b,d){this.margin.left=a;this.margin.top=c;this.margin.right=b;this.margin.bottom=d;this.ba=this.skinObj;this.Ke()};b.prototype.qn=function(a){0==a&&(this.C.Ad=!1);1==a&&(this.C.Ad=!0);2==a&&(this.C.Ad=!this.C.Ad);this.Y("viewmodechanged",{})};b.prototype.wo=function(){return 1==this.C.Ad?
1:0};b.prototype.uk=function(a,c){this.A.mode=1==c&&0<this.A.mode?0:Math.round(a);this.update();this.ia&&(this.ia.changePolygonMode(a,c),this.ia.update());this.Y("polymodechanged",{})};b.prototype.km=function(a){var c=this.A.nb.indexOf(a);-1==c?(this.A.nb.push(a),this.A.Ob.push(0),this.A.Oc.push(1)):this.A.Oc[c]=1;this.update()};b.prototype.jl=function(a){var c=this.A.nb.indexOf(a);-1!=c&&(this.A.Oc[c]=0,this.A.ni.push(a),this.update())};b.prototype.fq=function(a){var c=this.A.nb.indexOf(a);-1==c||
-1!=c&&0==this.A.Oc[c]?this.km(a):this.jl(a);this.update()};b.prototype.nn=function(a,c,b,d,e){for(var k=0;k<this.P.length;k++){var l=this.P[k];"poly"!=l.type||l.id!=a&&""!=a||(l.hc=c,l.gc=b,l.kc=d,l.jc=e)}""==a&&(this.A.hc=c,this.A.gc=b,this.A.kc=d,this.A.jc=e);this.update()};b.prototype.ln=function(a){this.Ga&&(this.Ga.Mj=0==a?!0:1==a?!1:!this.Ga.Mj,this.update())};b.prototype.no=function(){return this.A.mode};b.prototype.rn=function(){this.Y("viewstatechanged",{})};b.prototype.xo=function(){return 0};
b.prototype.$n=function(a){return(a=this.Sh[a])?a.type:"web"};b.prototype.Zn=function(a){return(a=this.Sh[a])?a:{}};b.prototype.fo=function(a,c){var b=[];a||(a=this.Wa);var d=this.Od[a];d&&(a=d);""===a&&0<Object.keys(this.zc).length&&(a=Object.keys(this.zc)[0]);this.zc[a]&&this.zc[a][c]&&(b.push(this.zc[a][c].y),b.push(this.zc[a][c].x));return b};b.prototype.ho=function(a,c){var b=[];a||(a=this.Wa);var d=this.Od[a];d&&(a=d);""===a&&0<Object.keys(this.zc).length&&(a=Object.keys(this.zc)[0]);this.lf[a]&&
this.lf[a][c]&&(b.push(this.lf[a][c].x),b.push(this.lf[a][c].y));return b};b.prototype.Yn=function(a){var c=this.Od[a];c&&(a=c);c=[];for(var b in this.Sh)c.push(b);for(b=0;b<c.length;b++)if(this.zc[a][c[b]])return c[b];return""};b.prototype.ml=function(a,c,b){a=Math.atan2(a+1,b);var d=Math.atan2(c+1,b);c=Math.sin(a);b=Math.sin(d);a=Math.cos(a);d=Math.cos(d);this.Nk.Za(0,0,-1);this.Mk.Za(a,0,-c);this.Ok.Za(-a,0,-c);this.Pk.Za(0,d,-b);this.Lk.Za(0,-d,-b)};b.prototype.Pi=function(a){a=this.Yf(a,this.Nk);
a=this.Yf(a,this.Mk);a=this.Yf(a,this.Ok);a=this.Yf(a,this.Pk);return a=this.Yf(a,this.Lk)};b.prototype.ym=function(a){if(!this.Ac&&this.Ho!=a){this.Ho=a;var c=this.margin.left+this.o.width/2+"px ";c+=this.margin.top+this.o.height/2+"px ";this.Fa.style[this.vd]=a+"px";this.Fa.style[this.vd+"Origin"]=c;this.D.style[this.vd]=a+"px";this.D.style[this.vd+"Origin"]=c}};b.prototype.Uk=function(){return this.B.be||this.B.rd||this.Z&&(4!=this.Ka||0!=this.rc)?!1:!0};b.prototype.Yg=function(){var a=new p.xa(0,
0,-100),c=this.dd();var b=100/this.f.c;var d=this.h.width/this.h.height;var e=this.o.height*b*d;b*=this.o.height;for(var f=this.Uk(),t=0;t<this.P.length;t++){var g=this.P[t];if("point"==g.type){var h=!1;if(2==this.kb){var n=(this.pan.c-g.pan)/100/d*e;var y=(this.i.c-g.i)/100*b;Math.abs(n)<this.o.width/2+500&&Math.abs(y)<this.o.height/2+500&&(h=!0)}else a.Za(0,0,-100),a.wa(-g.i*Math.PI/180),a.Ea(g.pan*Math.PI/180),a.Ea(-this.pan.c*Math.PI/180),a.wa(this.i.c*Math.PI/180),a.mb(this.O.c*Math.PI/180),
.01>a.z?(y=-c/a.z,n=a.x*y,y*=a.y,Math.abs(n)<this.o.width/2+500&&Math.abs(y)<this.o.height/2+500&&(h=!0)):y=n=0;g.Mb=n+this.o.width/2;g.vb=y+this.o.height/2;g.visible=h;g.px=g.Mb;g.py=g.vb;g.visible=g.visible;if(g.b&&g.b.onUpdatePosition)g.b.onUpdatePosition(this,g);else g.b&&g.b.__div&&("none"!=g.b.__div.style[this.bd]&&(g.b.__div.style[this.bd]="none"),g.b.ggUse3d?(this.Ac||this.ym(c),2==this.kb?(g.b.__div.style[this.Ra]="scale("+(100/this.f.c*500/g.b.gg3dDistance).toFixed(10)+")",g.b.__div.style.left=
this.margin.left+n+this.o.width/2+"px",g.b.__div.style.top=this.margin.top+y+this.o.height/2+"px"):(g.b.__div.style.width="1px",g.b.__div.style.height="1px",n="",this.Ac&&(n+="perspective("+c+"px) "),n+="translate3d(0px,0px,"+c+"px) ",n+="rotateZ("+this.O.c.toFixed(10)+"deg) ",n+="rotateX("+this.i.c.toFixed(10)+"deg) ",n+="rotateY("+(-this.pan.c).toFixed(10)+"deg) ",n+="rotateY("+g.pan.toFixed(10)+"deg) ",n+="rotateX("+(-g.i).toFixed(10)+"deg) ",n+="translate3d(0px,0px,"+(-1*g.b.gg3dDistance).toFixed(10)+
"px) ",g.b.__div.style[this.Ra+"Origin"]="0% 0%",g.b.__div.style[this.Ra]=n,g.b.__div.style.left=this.margin.left+this.o.width/2+"px",g.b.__div.style.top=this.margin.top+this.o.height/2+"px")):h&&f?(g.b.__div.style.left=this.margin.left+n+this.o.width/2+"px",g.b.__div.style.top=this.margin.top+y+this.o.height/2+"px"):(g.b.__div.style.left="-1000px",g.b.__div.style.top="-1000px"))}if("poly"==g.type){var r=[];if(2==this.kb)for(g.Wd=[],h=0;h<g.Nf.length;h++)y=g.Nf[h],n=(this.pan.c-y.pan)/100/d*e,y=(this.i.c-
y.i)/100*b,n+=this.margin.left+this.o.width/2,y+=this.margin.top+this.o.height/2,g.Wd.push({Mb:n,vb:y});else{for(h=0;h<g.Nf.length;h++)y=g.Nf[h],a.Za(0,0,-100),a.wa(-y.i*Math.PI/180),a.Ea(y.pan*Math.PI/180),a.Ea(-this.pan.c*Math.PI/180),a.wa(this.i.c*Math.PI/180),a.mb(this.O.c*Math.PI/180),r.push(a.clone());r=this.Pi(r);if(0<r.length)for(h=0;h<r.length;h++)a=r[h],.1>a.z?(y=-c/a.z,n=this.o.width/2+a.x*y,y=this.o.height/2+a.y*y):y=n=0,a.Mb=n,a.vb=y;g.Wd=r}}}};b.prototype.Mn=function(){for(var a=[],
c=0;c<this.P.length;c++){var b=this.P[c];"point"==b.type&&b.b&&b.b.__div&&a.push(b.b.__div)}return a};b.prototype.ga=function(a,c){a=Number(a);isNaN(c)&&(c=0);0>c&&(c=0);1<c&&(c=1);return"rgba("+(a>>16&255)+","+(a>>8&255)+","+(a&255)+","+c+")"};b.prototype.$o=function(){var a;if(this.ya&&(this.A.wg!=this.A.mode&&(this.A.wg=this.A.mode,this.ya.style.visibility=0<this.A.mode?"inherit":"hidden"),0<=this.A.mode||0<this.A.nb.length)){this.ca||(this.ca=this.ya.getContext("2d"));if(this.ca.width!=this.o.width||
this.ca.height!=this.o.height)this.ca.width=this.o.width,this.ca.height=this.o.height;this.ca.clear?this.ca.clear():this.ca.clearRect(0,0,this.ya.width,this.ya.height);var c=1;0>=this.A.mode&&(c=0);3==this.A.mode&&(c=this.A.qa);for(a=0;a<this.P.length;a++){var b=this.P[a];var d=c;if("poly"==b.type){var e=b.Wd;2==this.A.mode&&(d=b.qa);var f=this.A.nb.indexOf(b.id);-1!=f&&(d=this.A.Ob[f]);this.ca.fillStyle=this.ga(b.hc,b.gc*d);this.ca.strokeStyle=this.ga(b.kc,b.jc*d);if(0<e.length){this.ca.beginPath();
for(b=0;b<e.length;b++)d=e[b],0==b?this.ca.moveTo(d.Mb,d.vb):this.ca.lineTo(d.Mb,d.vb);this.ca.closePath();this.ca.stroke();this.ca.fill()}}}}};b.prototype.kl=function(a,c,b){var d,k=!1;var f=0;for(d=a.length-1;f<a.length;d=f++){var h=a[f];d=a[d];h.vb>b!=d.vb>b&&c<(d.Mb-h.Mb)*(b-h.vb)/(d.vb-h.vb)+h.Mb&&(k=!k)}return k};b.prototype.qh=function(a,c){var b=-1;if((0<=this.A.mode||0<this.A.nb.length)&&this.Eo())for(var d=0;d<this.P.length;d++){var e=this.P[d];"poly"==e.type&&e.Wd&&0<e.Wd.length&&(-1!=
this.A.mode||-1!=this.A.nb.indexOf(e.id))&&this.kl(e.Wd,a,c)&&(b=d,e.Mb=a,e.vb=c)}return 0<=b?this.P[b]:!1};b.prototype.Eo=function(){return 4==this.sa()&&0==this.rc};b.prototype.Ib=function(){var a=0,c=this.sa(),b=this.o;switch(this.f.mode){case 0:a=this.f.c/2;break;case 1:a=4==c?180*Math.atan(b.height/b.width*Math.tan(this.f.c/2*Math.PI/180))/Math.PI:b.height/b.width*this.f.c/2;break;case 2:a=Math.sqrt(b.width*b.width+b.height*b.height);a=4==c?180*Math.atan(b.height/a*Math.tan(this.f.c/2*Math.PI/
180))/Math.PI:b.height/a*this.f.c/2;break;case 3:a=4*b.height/3>b.width?this.f.c/2:4==c?180*Math.atan(4*b.height/(3*b.width)*Math.tan(this.f.c/2*Math.PI/180))/Math.PI:4*b.height/(3*b.width)*(this.f.c/2)}return 2*a};b.prototype.On=function(a,c){a||(a=this.Ib());c||(c=this.sa());return 4==c?180*Math.atan(this.ee()*Math.tan(a/2*Math.PI/180))/Math.PI:a*this.ee()};b.prototype.ee=function(){return this.o.width/this.o.height};b.prototype.rk=function(a){var c=this.f.c;a/=2;var b=this.sa();switch(this.f.mode){case 0:c=
2*a;break;case 1:a=4==b?180*Math.atan(this.o.width/this.o.height*Math.tan(a*Math.PI/180))/Math.PI:this.o.width/this.o.height*a;c=2*a;break;case 2:c=Math.sqrt(this.o.width*this.o.width+this.o.height*this.o.height);a=4==b?180*Math.atan(c/this.o.height*Math.tan(a*Math.PI/180))/Math.PI:c/this.o.height*a;c=2*a;break;case 3:4*this.o.height/3>this.o.width?c=2*a:(c=3*this.o.width/(4*this.o.height),a=4==b?180*Math.atan(c*Math.tan(a*Math.PI/180))/Math.PI:c*a,c=2*a)}return c};b.prototype.li=function(a){this.f.c=
this.rk(a)};b.prototype.Xf=function(){var a=this.Tl;a.pan=this.pan.c;a.i=this.i.c;a.f=this.f.c;this.Ue(a);this.Ue(a);this.Ue(a);this.pan.c=a.pan;this.i.c=a.i;this.f.c=a.f};b.prototype.Ue=function(a){var c=this.o.width/this.o.height;if(2==this.kb){if(0<this.f.Gg){var b=this.uc;this.h.J&&0<this.h.J.length&&(b=this.h.J[0].height);this.f.min=100*this.o.height/(b*this.f.Gg)}var d=a.f/2;b=d*c;var e=this.h.width/this.h.height*50;c=this.C.em?2*Math.min(50,e/c):2*Math.max(50,e/c);a.f<this.f.min&&(a.f=this.f.min);
a.f>c&&(a.f=c);50<d?a.i=0:(50<a.i+d&&(a.i=50-d),-50>a.i-d&&(a.i=-50+d));b>e?a.pan=0:(a.pan+b>e&&(a.pan=e-b,this.l.active&&(this.l.speed=-this.l.speed,this.pan.d=0)),a.pan-b<-e&&(a.pan=-e+b,this.l.active&&(this.l.speed=-this.l.speed,this.pan.d=0)))}else{0<this.f.Gg&&(b=this.uc,this.h.J&&0<this.h.J.length&&(b=this.h.J[0].height),this.f.min=360*Math.atan2(this.o.height/2,b/2*this.f.Gg)/Math.PI);a.f<this.f.min&&(a.f=this.f.min);e=this.f.max;var f=179;d=this.Ib()/2;b=c*d;4==this.sa()?b=180*Math.atan(c*
Math.tan(d*Math.PI/180))/Math.PI:9==this.sa()?(e=this.f.vj,f=355):12==this.sa()&&(e=this.f.uj,f=360);this.Z||(e=Math.max(160,e));a.f>e&&(a.f=e);12==this.sa()&&(2*b>f&&(a.f=f/c),d=this.Ib()/2,2*d>f&&(a.f=f),d=this.Ib()/2,b=c*d);2*d>this.i.max-this.i.min&&180>this.i.max-this.i.min&&(d=(this.i.max-this.i.min)/2,a.f=this.rk(2*d));90>this.i.max?a.i+d>this.i.max&&(a.i=this.i.max-d):a.i>this.i.max&&(a.i=this.i.max);-90<this.i.min?a.i-d<this.i.min&&(a.i=this.i.min+d):a.i<this.i.min&&(a.i=this.i.min);c=this.pan.max-
this.pan.min;if(359.99>c){e=90;f=Math.tan(d*Math.PI/180);var h=Math.tan((Math.abs(a.i)+d)*Math.PI/180);h=Math.sqrt(h*h+1)/Math.sqrt(f*f+1);d=180*Math.atan(h*Math.tan(b*Math.PI/180))/Math.PI;2*d>c&&(h=Math.tan(c*Math.PI/360)/Math.tan(b*Math.PI/180),c=h*Math.sqrt(f*f+1),h=Math.sqrt(c*c-1),e=180/Math.PI*Math.atan(h));a.pan+d>this.pan.max&&(a.pan=this.pan.max-d,this.l.active&&(this.l.speed=-this.l.speed,this.pan.d=0));a.pan-d<this.pan.min&&(a.pan=this.pan.min+d,this.l.active&&(this.l.speed=-this.l.speed,
this.pan.d=0));a.i+b>e&&(a.i=e-b);a.i-b<-e&&(a.i=-e+b)}}};b.prototype.update=function(a){void 0===a&&(a=0);this.Da=!0;a&&(this.$f=Math.max(1*a,this.$f))};b.prototype.Vk=function(){return this.ia?!!this.ia.isTileLoading:0<this.Pb||0<this.tc};b.prototype.Zg=function(){var a=Date.now();if(this.Fb){if(this.ia)if(this.mq(),2===this.kb)this.Xf(),this.Yg();else if(0===this.kb){var c=this.dd();this.ml(this.o.width/2,this.o.height/2,c);this.Yg()}}else 2===this.kb?(this.Yg(),this.Z?(this.na.Xj(),this.na.Nl()):
this.Xj()):0===this.kb&&(!this.Z||4==this.Ka&&0==this.rc?(c=this.dd(),this.ml(this.o.width/2,this.o.height/2,c),this.Yg(),this.dh?this.na.sq():this.Fm&&this.Em(),this.zm(),this.Z?(this.s.hd?14==this.s.format?this.na.rq():this.na.Lm():0<this.h.J.length?this.na.yq():this.na.Aq(),this.na.Nl()):(this.Sc?0<this.h.J.length?this.pq():this.oq():this.$g&&this.lq(),this.$o()),this.Ga&&this.Ga.Zo()):(this.na.Lm(),this.Yg(),this.Do()));c=Date.now();50<c-a?(this.M("Time between frames: "+(c-a)),this.vf||(2<this.Oj?
(this.vf=!0,this.M("disabling HighDPI rendering"),this.Rc()):this.Oj++)):this.Oj=0;this.df&&this.h.am++};b.prototype.oq=function(){var a=!1;if(this.o.width!=this.D.offsetWidth||this.o.height!=this.D.offsetHeight)this.o.width=this.D.offsetWidth,this.o.height=this.D.offsetHeight,this.D.style[this.Ra+"OriginX"]=this.o.width/2+"px",this.D.style[this.Ra+"OriginY"]=this.o.height/2+"px",a=!0;var c=Math.round(this.dd());this.xg==c&&!a||this.Ac||(this.xg=c,this.D.style[this.vd]=c+"px");this.pb.un(this.pan.c,
this.i.c,this.O.c,this.$a);for(a=0;6>a;a++){var b;if(b=this.pb.fb[a]){var d="";this.Ac?(d+="translate3d("+this.o.width/2+"px,"+this.o.height/2+"px,0px) ",d+="perspective("+c+"px) ",d+="translate3d(0px,0px,"+c+"px) "):d+="translate3d("+this.o.width/2+"px,"+this.o.height/2+"px,"+c+"px) ";d+="rotateZ("+Number(this.O.c).toFixed(10)+"deg) ";d+="rotateX("+Number(this.i.c).toFixed(10)+"deg) ";d+="rotateY("+Number(-this.pan.c).toFixed(10)+"deg) ";b.fl&&(d+=b.fl,b.hb||(d="translate3d(-10px,-10px,0px) scale(0.001,0.001)"),
b.K.style[this.Ra]=d)}}};b.prototype.lq=function(){this.Xf();var a;this.lc&&(a=this.lc.getContext("2d"));if(this.o.width!==this.D.offsetWidth||this.o.height!==this.D.offsetHeight)this.o.width=this.D.offsetWidth,this.o.height=this.D.offsetHeight;if(a){var c=a.canvas.width/2,b=a.canvas.height/2,d=a.createRadialGradient(c,b,5,c,b,Math.max(c,b));d.addColorStop(0,"#333");d.addColorStop(1,"#fff");a.rect(0,0,a.canvas.width,a.canvas.height);a.fillStyle=d;a.fill();a.fillStyle="#f00";a.font="20px Helvetica";
a.textAlign="center";a.fillText("Pan: "+this.pan.c.toFixed(1),c,b-60);a.fillText("Tilt: "+this.i.c.toFixed(1),c,b-30);a.fillText("Fov: "+this.f.c.toFixed(1),c,b);a.fillText("Node: "+this.Tk(),c,b+30);a.fillText("Title: "+this.Lf.title,c,b+60)}};b.prototype.mq=function(){this.Xf();if(this.o.width!==this.D.offsetWidth||this.o.height!==this.D.offsetHeight)this.o.width=this.D.offsetWidth,this.o.height=this.D.offsetHeight;this.ia&&this.ia.setPan&&(this.ia.setPan(this.pan.c),this.ia.setTilt(this.i.c),this.ia.setFov(this.f.c))};
b.prototype.Xj=function(){this.ya.style.visibility="inherit";this.ca||(this.ca=this.ya.getContext("2d"));if(this.ca.width!=this.o.width||this.ca.height!=this.o.height)this.ca.width=this.o.width,this.ca.height=this.o.height;this.ca.clear?this.ca.clear():this.ca.clearRect(0,0,this.ya.width,this.ya.height);this.tc=0;var a=100/this.f.c;var c=this.h.width/this.h.height;var b=this.o.height*a*c;a*=this.o.height;var d=(this.pan.c/100/c-.5)*b+this.o.width/2;for(var e=(this.i.c/100-.5)*a+this.o.height/2,f,
h,g,q,n=0;this.h.J.length>=n+2&&this.h.J[n+1].width>b;)n++;var y;var r=[];for(y=this.h.J.length-1;y>=n;){c=this.h.J[y];if(c.cache){var v={Ya:0,zb:0};v.Bb=c.L-1;v.Cb=c.fa-1}else{v={};var u=-e/a*(c.height/this.h.G);f=(-d+this.o.width)/b*(c.width/this.h.G);h=(-e+this.o.height)/a*(c.height/this.h.G);v.Ya=Math.min(Math.max(0,Math.floor(-d/b*(c.width/this.h.G))),c.L-1);v.zb=Math.min(Math.max(0,Math.floor(u)),c.fa-1);v.Bb=Math.min(Math.max(0,Math.floor(f)),c.L-1);v.Cb=Math.min(Math.max(0,Math.floor(h)),
c.fa-1)}r[y]=v;var x=!0;for(h=v.zb;h<=v.Cb;h++)for(f=v.Ya;f<=v.Bb;f++)q=f+h*c.L,u=c.U[q],u||(u=new p.Id,c.U[q]=u),this.Pb<this.Vg?u.h||(this.lj++,u.h=new Image,u.h.onload=this.bq(),u.h.onerror=this.si(u),u.h.onabort=this.si(u),u.h.crossOrigin=this.crossOrigin,u.h.setAttribute("src",this.He(0,y,f,h)),c.cache&&this.Sb.push(u.h),this.Pb++,this.Da=!0):this.tc++,u.h&&u.h.complete||(x=!1),u.visible=!0;v.rj=x;y--}for(y=this.h.J.length-1;y>=n;){c=this.h.J[y];if(r[y]&&0<=r[y].Ya)for(v=r[y],h=v.zb;h<=v.Cb;h++)for(f=
v.Ya;f<=v.Bb;f++)q=f+h*c.L,(u=c.U[q])||(u=c.U[q]=new p.Id),u.h&&u.h.complete&&(q=d+(-this.h.Ja+this.h.G*f)*b/c.width,this.ca.drawImage(u.h,q,e+(-this.h.Ja+this.h.G*h)*a/c.height,u.h.width*b/c.width,u.h.height*a/c.height)),u.visible=!0;y--}for(b=0;b<this.h.J.length;b++)if(c=this.h.J[b],!c.cache)for(g in c.U)c.U.hasOwnProperty(g)&&(u=c.U[g],u.visible||(u.h=null,delete c.U[g]));if(0<=this.A.mode||0<this.A.nb.length)for(b=1,0>=this.A.mode&&(b=0),3==this.A.mode&&(b=this.A.qa),g=0;g<this.P.length;g++)if(c=
this.P[g],d=b,"poly"==c.type&&(a=c.Wd,2==this.A.mode&&(d=c.qa),e=this.A.nb.indexOf(c.id),-1!=e&&(d=this.A.Ob[e]),0<a.length)){this.ca.fillStyle=this.ga(c.hc,c.gc*d);this.ca.strokeStyle=this.ga(c.kc,c.jc*d);this.ca.beginPath();for(c=0;c<a.length;c++)d=a[c],0==c?this.ca.moveTo(d.Mb,d.vb):this.ca.lineTo(d.Mb,d.vb);this.ca.closePath();this.ca.stroke();this.ca.fill()}this.$c=!1};b.prototype.aq=function(a){var c=this;return function(){c.update();c.$c=!0;a.loaded=!0;a.h&&!a.K&&c.D.appendChild(a.h);c.Pb&&
c.Pb--;a.h&&a.Pa&&(a.Pa.drawImage(a.h,0,0),a.h=null)}};b.prototype.bq=function(){var a=this;return function(){a.Da=!0;a.$c=!0;a.Pb&&a.Pb--}};b.prototype.si=function(a){var c=this;return function(){c.Da=!0;c.$c=!0;c.Pb&&c.Pb--;a.h=null}};b.prototype.dl=function(a,c,b){b.Ya=a.width/this.h.G*c.fg;b.zb=a.height/this.h.G*c.gg;b.Bb=a.width/this.h.G*c.Dg;b.Cb=a.height/this.h.G*c.Eg;b.Ya=Math.min(Math.max(0,Math.floor(b.Ya)),a.L-1);b.zb=Math.min(Math.max(0,Math.floor(b.zb)),a.fa-1);b.Bb=Math.min(Math.max(0,
Math.floor(b.Bb)),a.L-1);b.Cb=Math.min(Math.max(0,Math.floor(b.Cb)),a.fa-1)};b.prototype.Bp=function(a){a=Math.round(a);this.Ac=0<(a&1);this.uf=0<(a&2);this.zi=0<(a&4);this.vf=0<(a&8);4096<=a&&(this.Sc=0<(a&4096),this.Z=0<(a&8192),this.$g=0<(a&32768))};b.prototype.qo=function(){var a=0;this.Ac&&(a|=1);this.uf&&(a|=2);this.zi&&(a|=4);this.Sc&&(a|=4096);this.Z&&(a|=8192);this.$g&&(a|=32768);return a};b.prototype.Bm=function(){if(!(6>this.pb.fb.length))for(var a=0;6>a;a++){var c=this.pb.fb[a];var b=
[];b.push(new p.xa(-1,-1,-1,0,0));b.push(new p.xa(1,-1,-1,1,0));b.push(new p.xa(1,1,-1,1,1));b.push(new p.xa(-1,1,-1,0,1));for(var d=0;4>d;d++)4>a?b[d].Ea(-Math.PI/2*a):b[d].wa(Math.PI/2*(4==a?-1:1)),this.$a&&(b[d].mb(this.$a.O*Math.PI/180),b[d].wa(-this.$a.pitch*Math.PI/180)),b[d].Ce(-this.pan.c),b[d].Fd(this.i.c),b[d].De(this.O.c);b=this.Pi(b);c.hb=0<b.length;if(c.hb){c=c.mf;c.fg=b[0].sd;c.Dg=b[0].sd;c.gg=b[0].Qb;c.Eg=b[0].Qb;for(d=1;d<b.length;d++)c.fg=Math.min(c.fg,b[d].sd),c.Dg=Math.max(c.Dg,
b[d].sd),c.gg=Math.min(c.gg,b[d].Qb),c.Eg=Math.max(c.Eg,b[d].Qb);c.Mf=c.Dg-c.fg;c.gh=c.Eg-c.gg;c.scale=Math.max(c.Mf,c.gh)}else c.mf.Mf=-1,c.mf.gh=-1}};b.prototype.tj=function(){for(var a=0;a<this.h.J.length;a++){var c=this.h.J[a],b;for(b in c.U)c.U.hasOwnProperty(b)&&(c.U[b].visible=!1)}};b.prototype.aj=function(){var a=0,c=Math.tan(Math.min(this.Ib(),175)*Math.PI/360),b=this.o.height/(2*c);b*=1+this.o.width/this.o.height*c/2;for(b*=Math.pow(2,1<this.devicePixelRatio?this.h.Gl:this.h.Fl);this.h.J.length>=
a+2&&!this.h.J[a+1].rf&&this.h.J[a+1].width>b;)a++;return a};b.prototype.pq=function(){var a=!1,c,b,d;if(this.o.width!==this.D.offsetWidth||this.o.height!==this.D.offsetHeight)this.o.width=this.D.offsetWidth,this.o.height=this.D.offsetHeight,this.D.style[this.Ra+"OriginX"]=this.o.width/2+"px",this.D.style[this.Ra+"OriginY"]=this.o.height/2+"px",a=!0;var e=Math.round(this.dd());if(this.xg!=e||a)this.xg=e,this.Ac||(this.D.style[this.vd]=e+"px",this.D.style[this.vd+"Origin"]="50% 50%");this.tc=0;if(0<
this.h.J.length){this.Bm();this.tj();var f="";for(c=0;6>c;c++){var h=this.pb.fb[c];h.hb&&(f=f+c+",")}f=this.aj();var g;for(g=this.h.J.length-1;g>=f;){a=this.h.J[g];var q=1;g==this.h.J.length-1&&0==this.h.Ja&&(q=this.h.G/(this.h.G-2));for(c=0;6>c;c++){h=this.pb.fb[c];var n=h.mf;if(h.hb&&0<n.Mf&&0<n.gh&&0<n.scale||a.cache){h.Da=!1;var y={};a.cache?(y.Ya=0,y.zb=0,y.Bb=a.L-1,y.Cb=a.fa-1):this.dl(a,n,y);for(d=y.zb;d<=y.Cb;d++)for(b=y.Ya;b<=y.Bb;b++){var r=b+d*a.L+c*a.L*a.fa;(n=a.U[r])||(n=a.U[r]=new p.Id);
if(!n.K&&this.Pb<this.Vg){if(0<this.vi.length){n.K=this.vi.shift();for(r=this.D.firstChild;r&&r.Rd&&(-1==r.Rd||r.Rd>=g);)r=r.nextSibling;this.D.insertBefore(n.K,r);n.Pa=n.K.getContext("2d")}else if(this.um<this.we){this.um++;n.K=document.createElement("canvas");n.K.width=this.h.G+2*this.h.Ja;n.K.height=this.h.G+2*this.h.Ja;n.Pa=n.K.getContext("2d");n.K.style[this.Ra+"Origin"]="0% 0%";n.K.style.overflow="hidden";n.K.style.position="absolute";for(r=this.D.firstChild;r&&r.Rd&&(-1==r.Rd||r.Rd>=g);)r=
r.nextSibling;this.D.insertBefore(n.K,r)}n.K&&(this.lj++,n.h=new Image,n.h.crossOrigin=this.crossOrigin,n.h.style[this.Ra+"Origin"]="0% 0%",n.h.style.position="absolute",n.h.style.overflow="hidden",n.K.Rd=g,n.h.onload=this.aq(n),n.h.onerror=this.si(n),n.h.onabort=this.si(n),n.h.setAttribute("src",this.He(c,g,b,d)),a.cache&&this.Sb.push(n.h),this.Pb++,this.Da=!0)}else this.tc++;if(n.K){r="";this.Ac?(r+="translate3d("+this.o.width/2+"px,"+this.o.height/2+"px,0px) ",r+=" perspective("+e+"px) ",r+="translate3d(0px,0px,"+
e+"px) "):r+="translate3d("+this.o.width/2+"px,"+this.o.height/2+"px,"+e+"px) ";r+="rotateZ("+Number(this.O.c).toFixed(10)+"deg) ";r+="rotateX("+Number(this.i.c).toFixed(10)+"deg) ";r+="rotateY("+Number(-this.pan.c).toFixed(10)+"deg) ";this.$a&&(r+="rotateX("+Number(-this.$a.pitch).toFixed(10)+"deg) ",r+="rotateZ("+Number(this.$a.O).toFixed(10)+"deg) ");r=4>c?r+("rotateY("+-90*c+"deg) "):r+("rotateX("+(4==c?-90:90)+"deg) ");if(this.uf){var v=this.h.G/a.width*(2*g+1)*(this.Uf/this.h.G);v=this.Jc?2/
Math.tan(this.f.c*Math.PI/360)*v:2*v;r+=" scale("+v*q*q+")"}else v=1/(q*q);r+=" translate3d("+(1/q*b*this.h.G-this.h.Ja-a.width/2)+"px,";r+=1/q*d*this.h.G-this.h.Ja-a.width/2+"px,";r+=-a.width*v/2+"px)";h.hb&&(n.visible=!0,n.K?n.K.style[this.Ra]=r:n.h&&(n.h.style[this.Ra]=r))}}}}g--}for(e=0;e<this.h.J.length;e++){a=this.h.J[e];for(var u in a.U)a.U.hasOwnProperty(u)&&(n=a.U[u],!n.visible&&n.K&&(a.cache?n.K?n.K.style[this.Ra]="translate3d(-10px,-10px,0px) scale(0.001,0.001)":n.h&&(n.h.style[this.Ra]=
""):(n.Pa&&n.Pa.clearRect(0,0,n.Pa.canvas.width,n.Pa.canvas.height),this.vi.push(n.K),n.K?(f="translate3d(-10px,-10px,0px) scale(0.001,0.001)",n.K.style[this.Ra]=f,n.K.Rd=-1):n.loaded&&this.D.removeChild(n.h),n.K=null,n.h=null,n.Pa=null,delete a.U[u])))}this.$c=!1}};b.prototype.zm=function(){var a=Math.round(this.dd());this.Ac||this.ym(a);for(var c=0;c<this.Sa.length;c++){var b=this.Sa[c];b.Dm(a);b.b.hidden=!1}};b.prototype.Em=function(){for(var a=Math.round(this.dd()),c=0;c<this.I.length;c++){var b=
this.I[c];b.ld||(b.Dm(a),b.b.hidden=!1)}};b.prototype.Do=function(){for(var a=0;a<this.Sa.length;a++){var c=this.Sa[a];c.Cf(!1)}for(a=0;a<this.I.length;a++)c=this.I[a],c.ld||c.Cf(!1)};b.prototype.tq=function(){for(var a=0;a<this.I.length;a++){var c=this.I[a];c.ld||c.Ke()}for(a=0;a<this.Sa.length;a++)c=this.Sa[a],c.Ke()};b.prototype.Ic=function(a){this.ne=!1;try{a?this.bb=a:this.bb=document.createElement("canvas");var c=this.Nd.offsetWidth-this.margin.left-this.margin.right,b=this.Nd.offsetHeight-
this.margin.top-this.margin.bottom;if(100>c||100>b)b=c=100;var d=window.devicePixelRatio||1;this.vf&&(d=1);this.D.style.width=c+"px";this.D.style.height=b+"px";this.bb.style.width=c+"px";this.bb.style.height=b+"px";this.bb.width=c*d;this.bb.height=b*d;this.bb.style.display="none";this.bb.style.touchAction="none";this.D.insertBefore(this.bb,this.D.firstChild);var e=this.Km;e.stencil=!0;e.depth=!0;e.alpha=this.Jc?!0:!1;this.Sd&&10<=this.ll[0]&&(e.antialias=!1,e.alpha=!1);this.H=this.bb.getContext("webgl",
e);this.H||(this.H=this.bb.getContext("experimental-webgl",e));if(this.H){var f=this.H;this.rb.width=c*d;this.rb.height=b*d;f.clearColor(0,0,0,0);f.enable(this.H.DEPTH_TEST);f.viewport(0,0,500,500);f.clear(f.COLOR_BUFFER_BIT|f.DEPTH_BUFFER_BIT);4096<=f.getParameter(f.MAX_TEXTURE_SIZE)&&!this.tg&&(this.we=1<d?4*this.we:2*this.we);this.M("Max tile cnt: "+this.we);this.na.rg();this.na.Gh();this.na.nl(this.Gf);this.na.ol();this.B&&(this.B.rg(),this.B.Ic());this.Ga&&(this.Ga.rg(),this.Ga.Ic())}}catch(t){this.M(t)}this.H?
(this.Z=!0,this.Y("webglready",{gl:this.H})):alert("Could not initialise WebGL!")};b.prototype.nc=function(a){return a?"{"==a.charAt(0)||"/"==a.charAt(0)||0<a.indexOf("://")||0==a.indexOf("javascript:")?a:this.Ld+a:this.Ld};b.prototype.Yd=function(a,c,b){var d=(new RegExp("%0*"+c,"i")).exec(a.toString());if(d){d=d.toString();var e=b.toString();for(d.charAt(d.length-1)!=c&&(e=(1+b).toString());e.length<d.length-1;)e="0"+e;a=a.replace(d,e)}return a};b.prototype.He=function(a,c,b,d){var e=this.h.qj-
1-c,k=this.h.Hl,l="x";switch(a){case 0:l="f";break;case 1:l="r";break;case 2:l="b";break;case 3:l="l";break;case 4:l="u";break;case 5:l="d"}for(var f=0;3>f;f++)k=this.Yd(k,"c",a),k=this.Yd(k,"s",l),k=this.Yd(k,"r",c),k=this.Yd(k,"l",e),k=this.Yd(k,"x",b),k=this.Yd(k,"y",d),k=this.Yd(k,"v",d),k=this.Yd(k,"h",b);return this.nc(k)};b.prototype.kg=function(){return this.pan.c};b.prototype.ko=function(){return this.u.pan};b.prototype.Yk=function(){for(var a=this.pan.c;-180>a;)a+=360;for(;180<a;)a-=360;
return a};b.prototype.pe=function(){for(var a=this.pan.c-this.pan.xj;-180>a;)a+=360;for(;180<a;)a-=360;return a};b.prototype.Af=function(a){this.va();isNaN(a)||(this.pan.c=Number(a));this.update()};b.prototype.Jj=function(a){this.va();isNaN(a)||(this.pan.c=Number(a)+this.pan.xj);this.update()};b.prototype.tk=function(a,c){isNaN(a)||(this.Af(this.kg()+a),c&&(this.pan.d=a))};b.prototype.mn=function(a,c){this.tk(a*this.zh(),c)};b.prototype.Ch=function(){return this.i.c};b.prototype.ro=function(){return this.u.i};
b.prototype.Bf=function(a){this.va();isNaN(a)||(this.i.c=Number(a));this.update()};b.prototype.vk=function(a,c){this.Bf(this.Ch()+a);c&&(this.i.d=a)};b.prototype.pn=function(a,c){this.vk(a*this.zh(),c)};b.prototype.Kj=function(a){this.va();isNaN(a)||(this.O.c=Number(a));this.update()};b.prototype.al=function(){return this.O.c};b.prototype.fj=function(){return this.f.c};b.prototype.Nn=function(){return this.u.Hd};b.prototype.yf=function(a){this.va();switch(this.sa()){case 4:var c=170;break;case 12:c=
360;break;case 9:c=355;break;default:c=170}2==this.kb&&(c=9999999999);!isNaN(a)&&0<a&&a<c&&(c=this.f.c,this.f.c=1*a,c!=this.f.c&&this.update())};b.prototype.sk=function(a,c){this.yf(this.fj()+a);c&&(this.f.d=a)};b.prototype.Mi=function(a,c){if(!isNaN(a)){var b=a/90*Math.cos(Math.min(this.f.c,90)*Math.PI/360);b=this.f.c*Math.exp(b);this.yf(b);c&&(this.f.d=a)}};b.prototype.Ap=function(a,c){this.va();isNaN(a)||(this.pan.c=a);isNaN(c)||(this.i.c=c);this.update()};b.prototype.ji=function(a,c,b){this.va();
isNaN(a)||(this.pan.c=a);isNaN(c)||(this.i.c=c);isNaN(b)||this.yf(b);this.update()};b.prototype.up=function(){this.ji(this.pan.Qa,this.i.Qa,this.f.Qa)};b.prototype.xp=function(a){this.Og(a);this.Pg(a);this.Ng(a)};b.prototype.Og=function(a){this.C.Ab=a};b.prototype.Ng=function(a){this.C.ue=a};b.prototype.Xn=function(){return this.C.ue};b.prototype.Pg=function(a){this.C.kd=a};b.prototype.Lp=function(a,c){void 0===c&&(c=!0);this.Gm=c;this.ah==!a&&((this.ah=!!a)?this.jb.hi=!0:this.O.c=0,this.Y("gyrochanged",
{}))};b.prototype.so=function(){return this.ah};b.prototype.jo=function(){return this.sg?5:this.Sd?4:this.xl?1:this.ul?2:this.tl?3:0};b.prototype.Ln=function(){return this.oj?5:this.ql?4:this.Ih?2:this.Jc?3:this.nj?1:0};b.prototype.moveTo=function(a,c,b,d,e,f){this.va();if("_blank"!==a&&""!==a){this.u.active=!0;this.u.aborted=!1;this.u.$j=!1;var k=a.toString().split("/");1<k.length&&(a=Number(k[0]),d=Number(c),c=Number(k[1]),2<k.length&&(b=Number(k[2])));isNaN(a)?this.u.pan=this.pan.c:this.u.pan=
Number(a);isNaN(c)?this.u.i=this.i.c:this.u.i=Number(c);!isNaN(b)&&0<b&&180>b?this.u.f=Number(b):this.u.f=this.f.c;this.u.speed=!isNaN(d)&&0<d?Number(d):1;isNaN(e)?this.u.O=this.O.c:this.u.O=Number(e);void 0!==f?!a||4!=f&&12!=f&&9!=f||(this.u.Eb=f):this.u.Eb=this.Ka}};b.prototype.Vh=function(a){this.va();var c=0,b=0,d=70,e=4,f=0,h=1;a.hasOwnProperty("pan")&&(c=Number(a.pan),this.u.pan=c);a.hasOwnProperty("tilt")&&(b=Number(a.tilt),this.u.i=b);a.hasOwnProperty("fov")&&(d=Number(a.fov),this.u.f=d);
a.hasOwnProperty("projection")&&(e=Number(a.projection),this.u.Eb=e);a.hasOwnProperty("timingFunction")&&(f=Number(a.timingFunction));a.hasOwnProperty("speed")&&(h=Number(a.speed));0>=h?(this.ji(c,b,d),this.Nc(e)):(a=new p.dk,a.cb="__AutoMove",a.Ef=this.Yk(),a.Sg=this.i.c,a.Gd=this.f.c,a.Rg=this.Ka,a.Qc=c,a.$d=b,a.Ff=d,a.qd=e,a.Xe=!1,a.ke=!1,a.le=!1,0==f&&(a.ke=!0),1==f&&(a.Xe=!0,a.ke=!0),2==f&&(a.le=!0),a.speed=h,this.u.kk=this.w,this.w=this.Qk(a),this.u.lk=(new Date).getTime(),this.u.$j=!0,this.u.active=
!0,this.u.aborted=!1,this.u.pan=c,this.u.i=b,this.u.f=d,this.Jd=!1)};b.prototype.Oo=function(a){this.moveTo(this.pan.Qa,this.i.Qa,this.f.Qa,a)};b.prototype.Po=function(a,c){var b={};b.pan=this.pan.Qa;b.tilt=this.i.Qa;b.fov=this.f.Qa;b.projection=this.ai;b.timingFunction=c;b.speed=a;this.Vh(b)};b.prototype.an=function(a,c,b,d){var e=new p.lh(this);e.type="point";e.pan=c;e.i=b;e.id=a;e.b={};e.b.player=this;e.Ye();e.b.hotspot=e;e.b.__div=document.createElement("div");e.b.__div.appendChild(d);this.P.push(e);
e.b.__div.style.position="absolute";e.b.__div.style.left="-1000px";e.b.__div.style.top="-1000px";this.Fa.insertBefore(e.b.__div,this.Fa.firstChild);this.Da=!0};b.prototype.nq=function(a,c,b){for(var d=0;d<this.P.length;d++){var e=this.P[d];e.id==a&&(e.pan=c,e.i=b,e.Ye())}this.Da=!0};b.prototype.mp=function(a){for(var c=-1,b,d=0;d<this.P.length;d++)b=this.P[d],b.id==a&&(c=d);-1<c&&(b=this.P.splice(c,1).pop(),b.b&&b.b.__div&&this.Fa.removeChild(b.b.__div))};b.prototype.mo=function(){for(var a=[],c=
0;c<this.P.length;c++){var b=this.P[c];"point"==b.type&&a.push(String(b.id))}return a};b.prototype.Pn=function(a){for(var c=0;c<this.P.length;c++){var b=this.P[c];if(b.id==a)return c={},c.id=a,c.pan=b.pan,c.tilt=b.i,c.url=b.url,c.target=b.target,c.title=b.title,c.description=b.description,c.skinid=b.Nj,b.b&&b.b.__div&&(c.div=b.b.__div),c}};b.prototype.Jm=function(a,c){this.X.start.x=a;this.X.start.y=c;this.X.ea.x=a;this.X.ea.y=c;this.Ba.ea.x=a;this.Ba.ea.y=c;this.Dj++;this.pan.Fc=this.pan.c;this.i.Fc=
this.i.c};b.prototype.Hm=function(a,c){var b=this.Ib();this.pan.Fc+=a*b/this.o.height;this.i.Fc+=c*b/this.o.height;this.pan.c=this.pan.Fc;this.i.c=this.i.Fc};b.prototype.Im=function(a,c){this.X.c.x=a;this.X.c.y=c;this.X.da.x=this.X.c.x-this.X.ea.x;this.X.da.y=this.X.c.y-this.X.ea.y;this.C.Ad&&(this.X.ea.x=this.X.c.x,this.X.ea.y=this.X.c.y,this.update())};b.prototype.va=function(){this.l.active&&(this.l.active=!1,this.Y("autorotatechanged",{}),this.pan.d=0,this.i.d=0,this.f.d=0);this.u.active&&(this.u.active=
!1,this.pan.d=0,this.i.d=0,this.f.d=0);this.Ne=this.u.aborted=!1;this.l.Uh=!1;this.td=.02;this.Qf=0;this.l.Kf&&(this.l.enabled=this.l.Pe);this.hf=(new Date).getTime()};b.prototype.Vn=function(){return this.hf};b.prototype.Zk=function(a,c){a||(a=this.ua.x,c=this.ua.y);var b=this.o.height/(2*Math.tan(this.f.c*Math.PI/360));a-=this.o.width/2;c-=this.o.height/2;var d={};d.pan=180*Math.atan(a/b)/Math.PI;d.tilt=180*Math.atan(-c/Math.sqrt(a*a+b*b))/Math.PI;return d};b.prototype.oo=function(a,c){a||(a=this.ua.x,
c=this.ua.y);if(2===this.kb){var b=this.f.c/this.o.height;a=-(a-this.o.width/2)*b+this.pan.c;c=-(c-this.o.height/2)*b+this.i.c}else{b=new p.xa(0,0,1);a=this.Zk(a,c);b.Fd(-a.tilt);b.Ce(a.pan);b.Fd(-this.i.c);b.Ce(-this.pan.c);b.Fd(-this.$a.pitch);b.De(this.$a.O);for(a=b.dn()-180;-180>a;)a+=360;c=b.en()}b={};b.pan=a;b.tilt=c;return b};b.prototype.yc=function(a){return a==this.control||a&&void 0!==a.ggPermeableMap&&1==a.ggPermeableMap?!0:a&&void 0!==a.ggPermeable&&0==a.ggPermeable?!1:a&&a.ggType&&("container"==
a.ggType||"cloner"==a.ggType||"timer"==a.ggType)?!0:!1};b.prototype.Oi=function(a,c){var b=this.dd(),d,e;for(d=0;d<this.I.length+this.Sa.length;d++){var f=d<this.I.length?this.I[d]:this.Sa[d-this.I.length];if(f.gb)return f}for(d=0;d<this.I.length+this.Sa.length;d++)if(f=d<this.I.length?this.I[d]:this.Sa[d-this.I.length],!f.ld){var h=[],g=new p.xa,q;var n=e=void 0;0<f.f&&(e=Math.tan(f.f/2*Math.PI/180),n=0<f.Hc?e*f.ed/f.Hc:e,f.xd&&1!=f.xd&&(n*=f.xd));for(q=0;4>q;q++){switch(q){case 0:g.Za(-e,-n,0);
break;case 1:g.Za(e,-n,0);break;case 2:g.Za(e,n,0);break;case 3:g.Za(-e,n,0)}g.wa(f.wa*Math.PI/180);g.Ea(-f.Ea*Math.PI/180);g.mb(f.mb*Math.PI/180);g.z=g.z-1;g.wa(-f.i*Math.PI/180);g.Ea(f.pan*Math.PI/180);g.Ea(-this.pan.c*Math.PI/180);g.wa(this.i.c*Math.PI/180);g.mb(this.O.c*Math.PI/180);h.push(g.clone())}h=this.Pi(h);if(0<h.length){for(q=0;q<h.length;q++)g=h[q],.1>g.z?(n=-b/g.z,e=this.o.width/2+g.x*n,n=this.o.height/2+g.y*n):n=e=0,g.Mb=e,g.vb=n;if(this.kl(h,a,c))return f}}return null};b.prototype.Jh=
function(){return document.webkitIsFullScreen||document.mozFullScreen||document.msFullscreenElement&&null!=document.msFullscreenElement||document.fullScreen};b.prototype.No=function(a){this.Am(a);if(this.ad)this.ad.onclick();this.Ze&&this.yh();this.Gb=null;if(!this.C.Ab){a=a?a:window.event;if(a.which||0==a.which||1==a.which){var c=(new Date).getTime();if(this.md){this.Gb=this.md;this.R.Xa=!0;this.R.startTime=c;a.stopPropagation();return}if(this.yc(a.target)){var b;(b=this.Oi(this.ua.x,this.ua.y))&&
b.re&&(this.Gb=b);this.Jm(a.pageX,a.pageY);this.R.Xa=!0;this.R.startTime=c;a.preventDefault();this.va()}}this.X.da.x=0;this.X.da.y=0}};b.prototype.Jf=function(a,c){var b=this.A.Uj;b.enabled&&(this.ta!=this.eb&&0<=a&&0<=c&&""!=this.ta.title?(this.Aa.innerHTML=this.ta.title,this.Aa.style.color=this.ga(b.Vj,b.Tj),b.background?this.Aa.style.backgroundColor=this.ga(b.hc,b.gc):this.Aa.style.backgroundColor="transparent",this.Aa.style.border="solid "+this.ga(b.kc,b.jc)+" "+b.Ji+"px",this.Aa.style.borderRadius=
b.Ii+"px",this.Aa.style.textAlign="center",0<b.width?(this.Aa.style.left=a-b.width/2+this.margin.left+"px",this.Aa.style.width=b.width+"px"):(this.Aa.style.width="auto",this.Aa.style.left=a-this.Aa.offsetWidth/2+this.margin.left+"px"),this.Aa.style.height=0<b.height?b.height+"px":"auto",this.Aa.style.top=c+25+ +this.margin.top+"px",this.Aa.style.visibility="inherit",this.Aa.style.overflow="hidden"):(this.Aa.style.visibility="hidden",this.Aa.innerHTML=""))};b.prototype.Am=function(a){var c=this.qe();
this.Jh()?(this.ua.x=a.pageX-this.margin.left,this.ua.y=a.pageY-this.margin.top):(this.ua.x=a.pageX-c.x,this.ua.y=a.pageY-c.y);return c};b.prototype.xf=function(a){a&&null!==a&&"object"==typeof a?this.ta=a:this.ta=this.eb;this.ta==this.eb&&(a=this.qh(this.ua.x,this.ua.y))&&(a.ab=0);this.ta.Ye&&this.ta.Ye();this.hotspot=this.ta};b.prototype.Mo=function(a){a=a?a:window.event;var c=this.Am(a);if(!this.C.Ab&&!this.md){this.l.active&&(this.l.Ph=(new Date).getTime());this.R.Xa&&(a.preventDefault(),(a.which||
0==a.which||1==a.which)&&this.Im(a.pageX,a.pageY),this.va());var b=!1;if(this.ta==this.eb||"poly"==this.ta.type){var d=this.eb;0<this.P.length&&this.yc(a.target)&&(d=this.qh(this.ua.x,this.ua.y));this.ii(d);this.Jf(a.pageX-c.x,a.pageY-c.y);0!=d&&d!=this.eb&&(b=!0)}c=null;!b&&this.yc(a.target)&&(c=this.Oi(this.ua.x,this.ua.y));this.l.uh&&(this.l.uh=!1);this.Fa.style.cursor=this.ta!=this.eb&&this.ta.cf&&b||c&&c.qg?"pointer":"default"}};b.prototype.ii=function(a){!1===a&&(a=this.eb);this.ta!=a&&(this.ta!=
this.eb&&(0<this.A.mode&&(this.ta.ab=0),this.ba&&this.ba.hotspotProxyOut&&this.ba.hotspotProxyOut(this.ta.id,this.ta.url)),a!=this.eb?(this.xf(a),this.ba&&this.ba.hotspotProxyOver&&this.ba.hotspotProxyOver(this.ta.id,this.ta.url),0<this.A.mode&&(this.A.ab=1,this.ta.ab=1)):(this.xf(this.eb),0<this.A.mode&&(this.A.ab=0)),this.ia&&this.ia.changeCurrentHotspot(this.ta.id))};b.prototype.Lo=function(a){a=a?a:window.event;this.Oh=-1;this.Ze&&this.yh();if(!this.C.Ab&&(this.Gb&&(this.Gb.re(),this.Gb.gb?this.md=
this.Gb:this.md=null),this.R.Xa)){this.va();a.preventDefault();this.R.Xa=!1;a=(new Date).getTime();var c=Math.abs(this.X.start.x-this.X.ea.x)+Math.abs(this.X.start.y-this.X.ea.y);if(400>a-this.R.startTime&&0<=c&&20>c){var b=this.qh(this.ua.x,this.ua.y);b&&this.wm(b);c=Math.abs(this.X.Cd.x-this.X.ea.x)+Math.abs(this.X.Cd.y-this.X.ea.y);700>a-this.vg&&0<=c&&20>c?(b?this.xm(b):this.C.Wi&&this.ui(),this.vg=0):this.vg=a;this.X.Cd.x=this.X.ea.x;this.X.Cd.y=this.X.ea.y}}};b.prototype.Il=function(a){if(!this.C.kd&&
(a=a?a:window.event,this.yc(a.target))){var c=a.detail?-1*a.detail:a.wheelDelta/40;this.C.pl&&(c=-c);a.axis&&(-1==this.Oh?this.Oh=a.axis:this.Oh!=a.axis&&(c=0));var b=0<c?1:-1;a.wheelDeltaX&&a.wheelDeltaY&&Math.abs(a.wheelDeltaX)>Math.abs(a.wheelDeltaY)&&(c=0);0!=c&&(this.Mi(b*this.C.mm,!0),this.update());a.preventDefault();this.va()}};b.prototype.jq=function(a){a||(a=window.event);var c=a.touches,b=this.qe();this.ua.x=c[0].pageX-b.x;this.ua.y=c[0].pageY-b.y;this.ae=this.Gb=null;this.Ze&&this.yh();
if(!this.C.Ab){var d=(new Date).getTime();if(this.md)this.Gb=this.md,this.R.Xa=!0,this.R.startTime=d,a.preventDefault();else{if(!this.R.Xa&&c[0]){this.R.startTime=d;this.R.start.x=c[0].pageX;this.R.start.y=c[0].pageY;this.R.ea.x=c[0].pageX;this.R.ea.y=c[0].pageY;this.ob=c[0].target;if(this.yc(this.ob)){(d=this.Oi(this.ua.x,this.ua.y))&&d.re&&(this.Gb=d);if(d=this.qh(this.ua.x,this.ua.y))this.M(d),this.ae=d,this.ii(d),d=this.jg(a),this.Jf(d.x-b.x,d.y-b.y);this.Jm(c[0].pageX,c[0].pageY);this.R.Jk=c[0].identifier;
this.R.Xa=!0;a.preventDefault();this.va()}if(this.ob){b=this.ob;for(d=!1;b&&b!=this.control;){if(b.onmouseover)b.onmouseover();b.onmousedown&&!d&&(b.onmousedown(),d=!0);b=b.parentNode}d&&a.preventDefault()}}1<c.length&&(this.R.Xa=!1);!this.jj&&2==c.length&&c[0]&&c[1]&&this.yc(this.ob)&&(a=c[0].pageX-c[1].pageX,c=c[0].pageY-c[1].pageY,this.f.nm=Math.sqrt(a*a+c*c),this.f.pf=this.f.c);this.X.da.x=0;this.X.da.y=0}}};b.prototype.yh=function(){try{this.bh&&this.s.b&&(!this.s.hd&&this.s.Pj&&this.s.b.play(),
this.s.b.muted=!1);if(this.pa&&(this.pa.resume(),"suspended"==this.pa.state))return;if(this.Sd&&this.pa&&this.pa.createOscillator){var a=this.pa.createOscillator(),c=this.pa.createGain();a.frequency.value=30;a.type="sine";a.connect(c);c.connect(this.pa.destination);c.gain.value=.01;a.start(0);setTimeout(function(){a.stop()},1E4)}for(c=0;c<this.N.length;c++){var b=this.N[c];(!this.Xb(b.id)||b.la)&&0<=b.loop&&b.autoplay&&(b.la&&b.Te(),this.Ae(b.id,b.loop))}for(c=0;c<this.I.length;c++){var d=this.I[c];
!this.Xb(d.id)&&d.autoplay&&this.bh&&this.Ae(d.id,d.loop);this.Xb(d.id)&&d.autoplay&&this.bh&&(d.b.muted=!1)}this.Ze=!1}catch(e){this.M(e)}};b.prototype.iq=function(a){a||(a=window.event);var c=a.touches,b=this.qe();this.ua.x=c[0].pageX-b.x;this.ua.y=c[0].pageY-b.y;if(this.C.Ab)(this.B.rd||this.B.be)&&a.preventDefault();else{c[0]&&(this.R.ea.x=c[0].pageX,this.R.ea.y=c[0].pageY);if(this.ob){for(var d=this.ob,e=!1;d&&d!=this.control&&!e;)"scrollarea"==d.ggType&&(e=!0),"map"==d.ggType&&(e=!0),"text"==
d.ggType&&(e=!0),d=d.parentNode;e||a.preventDefault()}if(this.R.Xa){a.preventDefault();for(d=0;d<c.length;d++)if(c[d].identifier==this.R.Jk){this.Im(c[d].pageX,c[d].pageY);break}this.ae&&(d=this.jg(a),this.Jf(d.x-b.x,d.y-b.y));this.va()}2==c.length&&c[0]&&c[1]&&(this.R.Xa=!1,!this.jj&&this.yc(this.ob)&&(this.C.kd||(b=c[0].pageX-c[1].pageX,c=c[0].pageY-c[1].pageY,this.f.Bk=Math.sqrt(b*b+c*c),this.Ba.f.active=!0,this.Ba.f.mc=this.f.pf*Math.sqrt(this.f.nm/this.f.Bk),4==this.sa()&&this.Ba.f.mc>this.f.max&&
(this.Ba.f.mc=this.f.max),this.Ba.f.mc<this.f.min&&(this.Ba.f.mc=this.f.min)),this.va(),a.preventDefault()))}};b.prototype.hq=function(a){var c=this.qe(),b=!1;this.Ze&&this.yh();if(!this.C.Ab){this.R.Xa&&(a.preventDefault(),this.va());var d=(new Date).getTime();var e=void 0;var f=!1;e=Math.abs(this.R.start.x-this.R.ea.x)+Math.abs(this.R.start.y-this.R.ea.y);if(0<=e&&20>e){b=!0;this.yc(this.ob)&&(a.preventDefault(),this.Gb&&(this.Gb.re(),this.Gb.gb?this.md=this.Gb:this.md=null));if(this.ob){for(e=
this.ob;e&&e!=this.control;)e.onclick&&(e.onclick(),f=!0,b=!1),e=e.parentNode;f&&a.preventDefault()}e=Math.abs(this.R.Cd.x-this.R.ea.x)+Math.abs(this.R.Cd.y-this.R.ea.y);if(700>d-this.vg&&0<=e&&20>e){if(this.yc(this.ob))if(a.preventDefault(),this.ae)this.xm(this.ae);else if(this.C.Wi){var h=this;setTimeout(function(){h.ui()},1)}if(this.ob){for(e=this.ob;e&&e!=this.control;)e.ondblclick&&(e.ondblclick(),f=!0,b=!1),e=e.parentNode;f&&a.preventDefault()}this.vg=0}else this.vg=d;this.R.Cd.x=this.R.ea.x;
this.R.Cd.y=this.R.ea.y}if(this.ob)for(e=this.ob;e&&e!=this.control;){if(e.onmouseout)e.onmouseout();if(e.onmouseup)e.onmouseup();e=e.parentNode}a=this.jg(a);this.Jf(a.x-c.x,a.y-c.y);this.ae&&b&&this.wm(this.ae);this.ob=null;this.R.Xa=!1;this.ii(this.eb);this.ae=null}};b.prototype.gq=function(a){var c=this.qe();this.C.Ab||(this.R.Xa=!1);this.ae=null;this.ii(this.eb);a=this.jg(a);this.Jf(a.x-c.x,a.y-c.y)};b.prototype.wl=function(){return null!=this.ob||this.R.Xa};b.prototype.Jl=function(a){!this.xe&&
window.MSGesture&&(this.M("setup gesture"),this.xe=new MSGesture,this.xe.target=this.control);this.xe&&this.xe.addPointer(a.pointerId)};b.prototype.Sk=function(a){this.jj=!0;this.Wh=1;this.C.Ab||this.C.kd||(a.touches?(this.ob=a.touches.target,this.yc(a.target)&&(a.preventDefault(),this.f.pf=this.f.c,this.va())):(a.preventDefault(),this.f.pf=this.f.c,this.va()))};b.prototype.In=function(a){this.C.Ab||this.C.kd||!this.yc(a.target)||(a.preventDefault(),this.Ba.f.active=!0,this.Ba.f.mc=this.f.pf/Math.sqrt(a.scale),
4==this.sa()&&this.Ba.f.mc>this.f.max&&(this.Ba.f.mc=this.f.max),this.update(),this.va())};b.prototype.Qo=function(a){this.C.Ab||this.C.kd||(a.preventDefault(),1!=a.scale&&(this.Ba.f.active=!0,this.Wh*=a.scale,this.Ba.f.mc=this.f.pf/Math.sqrt(this.Wh),4==this.sa()&&this.Ba.f.mc>this.f.max&&(this.Ba.f.mc=this.f.max),this.update(),this.va()))};b.prototype.Rk=function(a){this.C.Ab||this.C.kd||(this.Ba.f.active=!1,a.preventDefault(),this.va(),this.xe&&this.xe.reset&&this.xe.reset())};b.prototype.Fo=function(a){this.C.ue||
(this.isFullscreen&&a.preventDefault(),this.Nh=a.keyCode,this.va())};b.prototype.Go=function(a){this.Nh&&(this.Nh=0,a.preventDefault())};b.prototype.Wo=function(){this.Nh=0};b.prototype.$h=function(){this.isFullscreen&&(this.Jh()||this.exitFullscreen(),this.Jh()&&(this.T.style.left="0px",this.T.style.top="0px"))};b.prototype.Xo=function(a,c,b,d){d?(this.og.alpha=a,this.og.beta=c,this.og.gamma=b,this.og.gamma+=90):(this.jb.alpha=a,this.jb.beta=c,this.jb.gamma=b,this.jb.gamma+=90);this.jb.orientation=
window.orientation?parseInt(""+window.orientation,10):0;b=new p.fk;a=this.jb;b.Ce(-a.alpha);b.De(-a.beta);b.Fd(-a.gamma);b.De(90-a.orientation);1>b.Db?-1<b.Db?(c=180/Math.PI*Math.asin(-b.Db),a=180/Math.PI*Math.atan2(b.ac,b.$b),b=180/Math.PI*Math.atan2(b.Zb,b.Yb)):(c=0,a=90,b=-180/Math.PI*Math.atan2(-b.Jb,b.Kb)):(c=0,a=-90,b=180/Math.PI*Math.atan2(-b.Jb,b.Kb));if(this.ah)if(this.wl()||this.u.Fh||this.jb.hi)this.jb.Pl=this.kg()+b,this.jb.cg=0,this.jb.hi=!1;else{d=this.u.active;var e=1;10>this.jb.cg&&
(this.jb.cg+=1,e=.1*this.jb.cg);b=-b+this.jb.Pl;this.Af(e*b+(1-e)*this.kg());this.Bf(e*a+(1-e)*this.Ch());this.Gm?this.Kj(e*c+(1-e)*this.al()):this.Kj(0);this.Xf();this.u.active=d}};b.prototype.wm=function(a){this.ba&&this.ba.hotspotProxyClick&&this.ba.hotspotProxyClick(a.id,a.url);""!=a.url&&(this.yj(a.url,a.target),this.Jf(-1,-1))};b.prototype.xm=function(a){this.ba&&this.ba.hotspotProxyDoubleClick&&this.ba.hotspotProxyDoubleClick(a.id,a.url)};b.prototype.zh=function(){return Math.min(1,2*Math.tan(Math.PI*
Math.min(this.f.c,90)/360))};b.prototype.Ql=function(){var a=this;setTimeout(function(){a.Ql()},100);9!=a.di||a.Hh||window.requestAnimationFrame(function(){a.Kg();a.Lc("restart recover timer")});10<a.di&&1<a.Dj&&(a.Lc("recover timer - disabling requestAnimationFrame"),a.Hh=!0,a.Kg());a.di++};b.prototype.mi=function(a){var c={Cq:{value:0,name:"pan"},Eq:{value:1,name:"tilt"},Bq:{value:2,name:"fov"}},b=0,d=0,e=0,f;for(f in c){for(var h=c[f],g,q=Math.floor(a);!this.af(q,h.value)&&0<q;)q--;q=this.af(q,
h.value);var n=this.bo(q);if(n){g=new p.vc(q.time,q.value);var y=new p.vc(n.time,n.value),r=(a-q.time)/(n.time-q.time);if(0!=q.type||0!=n.type&&3!=n.type)if(3==q.type)g=q.value;else{r=new p.vc;var v=new p.vc,u=n.time-q.time;0==q.type?v.Za(q.time+.3*u,q.value):v.Za(q.he,q.ie);0==n.type||3==n.type?r.Za(n.time-.3*u,n.value):r.Za(n.ge,n.Vc);n=new p.vc;n.Hi(g,y,v,r,a);g=n.y}else n=new p.vc,n.zd(g,y,r),g=n.y}else g=q.value;switch(h.value){case 0:h=this.pan.c;if(this.Jd&&3!=q.type){if(2!=this.kb){for(;360<
g;)g-=360;for(;-360>g;)g+=360}b=g-h;2!=this.kb&&(180<b&&(b-=360),-180>b&&(b+=360));this.pan.c=this.pan.c+b*this.td}else this.pan.c=g;this.l.Ag=this.pan.c;break;case 1:h=this.i.c;this.Jd&&3!=q.type?(d=g-h,this.i.c=this.i.c+d*this.td):this.i.c=g;this.l.Bg=this.i.c;break;case 2:h=this.f.c,this.Jd&&3!=q.type?(e=g-h,this.f.c=this.f.c+e*this.td):this.f.c=g,this.l.zg=this.f.c}}c=this.sa();for(f=Math.floor(a);!this.af(f,3)&&0<f;)f--;f=this.af(f,3);q=a-f.time;this.Jd&&-1!=this.Rf&&this.nh+this.Ei>a?(c=this.ig(this.Rf),
this.f.c>c?this.nh=a:(q=(a-this.nh)/this.Ei,q=Math.min(1,q),this.Nc(this.Ka,this.Rf,1-q))):(0==f.xb||q>f.xb-.3?this.Nc(f.value):(q/=f.xb,this.Nc(c,f.value,1-q)),this.l.Dl=f.value);this.Jd&&(b=Math.sqrt(b*b+d*d+e*e),.3>b&&(this.Jd=!1,this.td=.02,this.Qf=0),0<this.Qf&&b>this.Qf&&(this.td+=.01,this.td=Math.min(this.td,1)),this.Qf=b);f=Math.floor(a);if(f!=this.mk)for(this.mk=f,a=this.Jn(f),b=0;b<a.length;b++)d=a[b],e=d.mh,this.pd.hasOwnProperty(e)&&(c=this.pd[e].type,0==c?this.Zd(e,d.ak):1==c?this.Zd(e,
d.value):2==c&&this.Zd(e,"true"==d.ak));this.update()};b.prototype.cp=function(a){var c=this.u.speed;this.u.pj&&(c=c*(a.getTime()-this.u.pj)/60,5<c&&(c=5),.2>c&&(c=.2));this.u.pj=a.getTime();this.l.eg&&(this.na.ready()||4==this.Ka)&&this.Bh()&&(this.l.eg=!1,this.l.active=!0,this.qb.nd=!0,this.qb.bj=!1);if(this.u.active||0!=this.u.Eb&&this.na.ready()){if(this.u.$j&&"__AutoMove"==this.w.cb){var b=a.getTime()-this.u.lk;c=b/100;if(c>=this.w.length){if(this.mi(this.w.length),this.za.splice(this.za.indexOf(this.w),
1),this.u.active=!1,this.w=this.u.kk,this.u.Eb=0,this.ji(this.u.pan,this.u.i,this.u.f),this.pan.Fc=this.u.pan,this.i.Fc=this.u.i,this.u.Hg&&(this.u.Hg=!1,this.l.Uh=!0,this.l.active=!0,this.Y("autorotatechanged",{})),this.onMoveComplete)this.onMoveComplete()}else this.mi(c)}else{this.pan.d=this.u.pan-this.pan.c;if(360==this.pan.max-this.pan.min){for(;-180>this.pan.d;)this.pan.d+=360;for(;180<this.pan.d;)this.pan.d-=360}this.i.d=this.u.i-this.i.c;this.O.d=this.u.O-this.O.c;this.f.d=this.u.f-this.f.c;
b=c*this.zh();var d=Math.sqrt(this.pan.d*this.pan.d+this.i.d*this.i.d+this.O.d*this.O.d+this.f.d*this.f.d),e=this.pan.c-this.u.Ag,f=this.i.c-this.u.Bg,h=this.O.c-this.u.El,g=this.f.c-this.u.zg;100*Math.sqrt(e*e+f*f+h*h+g*g)<b&&0==this.u.Eb&&(this.u.aborted=!0);this.u.Ag=this.pan.c;this.u.Bg=this.i.c;this.u.El=this.O.c;this.u.zg=this.f.c;if(100*d<b||this.u.aborted){if(this.pan.d=0,this.i.d=0,this.O.d=0,this.f.d=0,this.u.active&&(this.u.active=!1,this.pan.c=this.u.pan,this.i.c=this.u.i,this.O.c=this.u.O,
this.f.c=this.u.f,this.onMoveComplete))this.onMoveComplete()}else d=d>5*b?b/d:.2,this.pan.d*=d,this.i.d*=d,this.f.d*=d;this.pan.c+=this.pan.d;this.i.c+=this.i.d;this.O.c+=this.O.d;this.f.c+=this.f.d;0!=this.u.Eb&&(this.u.Eb!=this.Ka?(c=this.ig(this.u.Eb),this.fj()>c?(this.f.c+=-Math.max((2.5-1.7*Math.min(Math.sqrt(this.pan.d*this.pan.d+this.i.d*this.i.d+this.O.d*this.O.d)/b,1))*b,this.f.d)-this.f.d,this.u.f=this.f.c):(this.rc=this.Ka,this.Ka=this.u.Eb,this.M("New projection from Target:"+this.Ka),
this.Jg=this.u.Mg=0,this.na.Gh())):1>this.u.Mg?(this.u.Mg=Math.min(1,this.u.Mg+.05*c),this.Jg=this.u.Mg):(this.rc=0,this.u.Eb=0,this.na.Gh()))}this.hf=a.getTime();this.update()}else if(this.l.active){b=a.getTime()-this.l.startTime;this.l.Ph<this.l.startTime&&(this.l.Ph=this.l.startTime);if((this.l.Kf||this.qb.nd)&&0<this.za.length){c=b/100;d=!1;if(this.Lb!=this.w.cb||""!=this.w.Oe&&this.l.Kd!=this.w.Oe){for(b=0;b<this.za.length;b++)if(""==this.Lb&&this.za[b].Oe==this.l.Kd||""!=this.Lb&&this.za[b].cb==
this.Lb&&this.za[b].Oe==this.l.Kd){d=!0;this.w=this.za[b];this.Lb=this.w.cb;break}!d&&0<this.za.length&&this.za[0].Oe==this.l.Kd&&(d=!0,this.w=this.za[0],this.Lb=this.w.cb)}else d=!0;if(d)if(b=(e=this.s.b&&this.s.hd)&&this.l.Sj&&!this.qb.nd,this.Ne){d=c;if(b)for(this.s.b.currentTime<this.nk&&this.Gi&&(this.Fi++,this.Gi=!1),d=10*(this.Fi*this.s.b.duration+this.s.b.currentTime),this.nk=this.s.b.currentTime,.05>this.s.b.duration-this.s.b.currentTime&&(this.Gi=!0);d>=10*this.Me;)d-=10*this.Me;if(!e&&
c>=this.w.length||e&&!b&&c>=this.w.length||e&&b&&(this.w.cb!=this.w.Kl||this.w.Xh!=this.Wa)&&c>=this.w.length){this.mi(this.w.length);this.l.Bd=0;this.Ne=!1;if(this.qb.nd){this.fm();return}this.Lb=this.w.Kl;if(this.Lb==this.w.cb&&this.Wa==this.w.Xh){if(1<this.Ma.length&&0<this.l.Yh){if(this.l.wj){b=1E3;do c=this.Ma[Math.floor(Math.random()*this.Ma.length)];while(b--&&c==this.Wa)}else c=this.Xk();this.ye("{"+c+"}");this.l.startTime=a.getTime();this.Ne=!1;this.l.active=!0;this.B.fe=!0}}else this.gf&&
this.w.Xh!=this.Wa&&(this.ye("{"+this.w.Xh+"}",this.w.To),this.B.enabled?(this.l.active=!1,this.B.fe=!0):this.l.active=!0),this.l.startTime=a.getTime()}else this.mi(d),this.l.Bd=d}else if(c=this.w.W[0],d=this.w.W[1],e=this.w.W[2],f=this.w.W[3],3!=f.ub&&(f=0),this.l.Uh||this.u.aborted||this.qb.nd||b){if(this.Ne=!0,0<this.l.Bd?this.l.startTime=a.getTime()-100*this.l.Bd:this.l.startTime=a.getTime(),this.Jd=b){for(this.Me=this.Fi=0;this.Me<this.w.length/10;)this.Me+=this.s.b.duration;d=10*this.s.b.currentTime;
for(b=Math.floor(d);!this.af(b,3)&&0<b;)b--;b=this.af(b,3);b.value==this.Ka?this.Rf=-1:(this.Rf=b.value,this.nh=d,this.Ei=Math.max(5,b.time+b.xb-d))}}else{b={};if(0<this.l.Bd)b.pan=this.l.Ag,b.tilt=this.l.Bg,b.fov=this.l.zg,b.projection=this.l.Dl;else{for(b.pan=c.value;360<b.pan;)b.pan-=360;for(;-360>b.pan;)b.pan+=360;b.tilt=d.value;b.fov=e.value;b.projection=f?f.value:4}b.timingFunction=3;b.speed=1;this.u.Hg=!0;this.Vh(b);this.l.active=!0}}else if(0<this.l.Yh&&this.gf&&b>=1E3*this.l.Yh){if(1<this.Ma.length){if(this.l.wj){b=
1E3;do c=this.Ma[Math.floor(Math.random()*this.Ma.length)];while(b--&&c==this.Wa)}else b=this.Ma.indexOf(this.Wa),b++,b>=this.Ma.length&&(b=0),c=this.Ma[b];this.l.startTime=a.getTime();this.l.jd=a.getTime();this.l.timeout=0;this.ye("{"+c+"}");this.l.active=!0;this.B.fe=!0}}else b=a.getTime(),d=c=1E3/60,0!=this.l.jd&&(d=b-this.l.jd),this.i.d=this.l.ti*(0-this.i.c)/100,this.f.d=this.l.ti*(this.f.Qa-this.f.c)/100,this.pan.d=.95*this.pan.d+-this.l.speed*this.zh()*.05,c=d/c,this.pan.c+=this.pan.d*c,this.i.c+=
this.i.d*c,this.f.c+=this.f.d*c,this.l.jd=b,this.update();3E3<a.getTime()-this.l.Ph&&!this.l.uh&&(this.Fa.style.cursor="none",this.l.uh=!0)}else!this.qb.bj&&1E3<a.getTime()-this.hf&&(this.za.splice(this.za.indexOf(this.w),1),this.w=this.hg(!1),this.Lb=this.w.cb,this.l.active=!1,this.l.eg=!0),this.l.enabled&&!this.R.Xa&&a.getTime()-this.hf>1E3*this.l.timeout&&!this.l.eg&&(this.l.Ug&&this.Bh()||!this.l.Ug)&&(this.l.active=!0,this.l.startTime=a.getTime(),this.l.jd=0,this.Y("autorotatechanged",{}),this.pan.d=
0,this.i.d=0,this.f.d=0),!this.Ba.enabled||this.R.Xa||0==this.pan.d&&0==this.i.d&&0==this.f.d||(this.u.Fh=!0,this.pan.d*=.9,this.i.d*=.9,this.f.d*=.9,this.pan.c+=this.pan.d,this.i.c+=this.i.d,this.Mi(this.f.d),1E-4>this.pan.d*this.pan.d+this.i.d*this.i.d+this.f.d*this.f.d&&(this.pan.d=0,this.i.d=0,this.f.d=0),this.update())};b.prototype.fp=function(a){var c=this.B;if(c.rd){var b=a.getTime()-c.Om;b/=1E3*c.Nm;1<=b?(c.rd=!1,this.Ik(),c.Qj=a.getTime(),this.qm(),c.be=!0,0==c.ec||c.Pf||(4==c.ec?(this.w=
this.hg(!0,c.Fe,c.Ge,c.Hd),this.Lb=this.w.cb,this.l.active=!0,this.qb.nd=!0):this.moveTo(c.Fe,c.Ge,c.Hd,c.de,0,c.qd))):c.Ol(b)}else c.be&&(b=a.getTime()-c.Qj,b/=1E3*c.Tf,1<=b?(c.be=!1,this.hf=a.getTime(),this.update(),0!=c.ec&&c.Pf&&(4==c.ec?(this.w=this.hg(!0,c.Fe,c.Ge,c.Hd),this.Lb=this.w.cb,this.l.active=!0,this.qb.nd=!0):this.moveTo(c.Fe,c.Ge,c.Hd,c.de,0,c.qd)),4!=c.ec&&(this.Og(c.Th),this.Pg(c.Ai),this.Ng(c.Lh),this.l.active=c.fe,this.Y("autorotatechanged",{}),c.fe=!1),this.l.jd=0,this.ha&&this.Ri(),
this.Vf=!1):c.Ol(b));c=this.Yo;c.gn&&(c.vh?a.getTime()-c.Yi>=1E3*c.Bn&&(c.vh=!1):(c.current+=c.Wc,0>c.current&&(c.current=0,c.Wc=-c.Wc,c.vh=!0,c.Yi=a.getTime()),1<c.current&&(c.current=1,c.Wc=-c.Wc,c.vh=!0,c.Yi=a.getTime())))};b.prototype.ip=function(){var a,c=this.A;if(0<c.nb.length){for(a=0;a<c.nb.length;a++)c.Oc[a]!=c.Ob[a]&&(c.Oc[a]>c.Ob[a]?(c.Ob[a]+=.05,c.Oc[a]<c.Ob[a]&&(c.Ob[a]=c.Oc[a])):(c.Ob[a]-=.05,c.Oc[a]>c.Ob[a]&&(c.Ob[a]=c.Oc[a],-1!=c.ni.indexOf(c.nb[a])&&(c.ni.splice(c.ni.indexOf(c.nb[a]),
1),c.nb.splice(a,1),c.Oc.splice(a,1),c.Ob.splice(a,1)))));this.update()}if(2==c.mode)for(a=0;a<this.P.length;a++){var b=this.P[a];"poly"==b.type&&b.ab!=b.qa&&(b.ab>b.qa?(b.qa+=c.Wc,b.ab<b.qa&&(b.qa=b.ab)):(b.qa-=c.Wc,b.ab>b.qa&&(b.qa=b.ab)),this.update())}3==c.mode&&c.ab!=c.qa&&(c.ab>c.qa?(c.qa+=c.Wc,c.ab<c.qa&&(c.qa=c.ab)):(c.qa-=c.Wc,c.ab>c.qa&&(c.qa=c.ab)),this.update())};b.prototype.hp=function(){var a=this.Ba;this.R.Xa&&(this.C.Ad?(a.da.x=.4*(this.X.ea.x-a.ea.x),a.da.y=.4*(this.X.ea.y-a.ea.y),
a.ea.x+=a.da.x,a.ea.y+=a.da.y):(a.da.x=.1*-this.X.da.x*this.C.sensitivity/8,a.da.y=.1*-this.X.da.y*this.C.sensitivity/8),this.Hm(a.da.x,a.da.y),this.update());a.f.active&&(this.sk(.4*(a.f.mc-this.f.c)),.001>Math.abs(a.f.mc-this.f.c)/this.f.c&&(a.f.active=!1),this.update());if(a.enabled&&(0!=a.da.x||0!=a.da.y)&&!this.R.Xa){var c=.9*(1-a.Hj);a.da.x=c*a.da.x;a.da.y=c*a.da.y;this.u.Fh=!0;.01>a.da.x*a.da.x+a.da.y*a.da.y?(a.da.x=0,a.da.y=0):(this.Hm(a.da.x,a.da.y),this.update())}};b.prototype.ep=function(){if(this.C.dm&&
this.C.Ad){var a=this.Tl;a.pan=this.pan.c;a.i=this.i.c;a.f=this.f.c;this.Ue(a);this.Ue(a);this.Ue(a);var c=a.pan-this.pan.c,b=a.i-this.i.c;a=a.f-this.f.c;if(0!=c||0!=b||0!=a){var d=.2+.9*Math.min((Math.abs(c)+Math.abs(b)+Math.abs(a))/Math.abs(Math.min(this.f.c,90))*.3,1);this.pan.c+=c*d;this.i.c+=b*d;this.f.c+=a*d;this.Ba.Hj=.3;this.update()}else this.Ba.Hj=0}else this.Xf();if(2!=this.kb){for(;360<this.pan.c;)this.pan.c-=360;for(;-360>this.pan.c;)this.pan.c+=360}};b.prototype.gp=function(){if(!this.Bh()&&
this.df&&5<this.h.am){var a,c=0,b=this.Sb.length;if(this.$g)b=50,this.Zi<b&&this.Zi++,c=this.Zi;else for(a=0;a<b;a++)(this.Sb[a].complete&&this.Sb[a].src!=this.Ek||""==this.Sb[a].src)&&c++;c==b?(this.bi=1,this.isLoaded=!0,this.ja&&this.ja.ggLoaded&&this.ja.ggLoaded(),this.Y("imagesready",{}),this.l.Ug&&this.l.enabled&&!this.u.active&&!this.B.rd&&(this.l.active=!0,this.l.startTime=(new Date).getTime(),this.l.jd=0)):this.bi=c/(1*b)}};b.prototype.Kg=function(){var a=this;a.gi||(a.Hh?setTimeout(function(){a.gi=
!1;a.Kg()},1E3/60):window.requestAnimationFrame(function(){a.gi=!1;a.Kg()}));a.gi=!0;this.Dj=this.di=0;a.u.Fh=!1;var c=new Date;this.Ah++;120<=this.Ah&&(this.M("F/s: "+Math.round(1E3*this.Ah/(c.getTime()-this.zl))),this.zl=c.getTime(),this.Ah=0);this.Y("timer",{});this.Z&&this.na.An();this.Fb&&""!==this.oe&&!this.ia&&document.hasOwnProperty(this.oe)&&document[this.oe].setPan&&0==this.Gn--&&(this.ia=document[this.oe],this.Sc=this.Z=!1,this.ya&&(this.ya.style.visibility="hidden"),this.ia.setLocked(!0),
this.ia.setSlaveMode(!0),this.ia.readConfigString(this.Ui),this.Lc("Flash player '"+this.oe+"' connected."));this.Zj&&(this.Rc(),this.Zj=!1);this.hp();this.gp();this.cp(c);this.ep();this.fp(c);this.na.qq();(0<=this.A.mode||0<this.A.nb.length)&&this.ip();this.yi();if(this.jf.pan!=this.pan.c||this.jf.i!=this.i.c||this.jf.f!=this.f.c)this.jf.pan=this.pan.c,this.jf.i=this.i.c,this.jf.f=this.f.c,this.Y("positionchanged",{});this.Bl!=this.Ka&&(this.Bl=this.Ka,this.Y("projectionchanged",{}));this.Da&&(0<
this.$f?this.$f--:(this.Da=!1,this.$f=0),this.B.be||this.B.rd||(this.Zg(),this.Y("renderframe",{})),this.Y("repaint",{}));c=this.Vk();c!=this.Al&&(c?(this.ja&&this.ja.ggReLoadedLevels&&this.ja.ggReLoadedLevels(),this.Y("tilesrequested",{})):(a.ja&&a.ja.ggLoadedLevels&&a.ja.ggLoadedLevels(),this.Y("tilesready",{})),this.Al=c)};b.prototype.ig=function(a){switch(a){case 4:a=Math.min(110,this.f.max);break;case 12:a=Math.min(270,this.f.uj);a=Math.min(360*this.ee(),a);a=Math.min(360/this.ee(),a);break;
case 9:a=Math.min(270,this.f.vj);break;default:a=90}return a};b.prototype.Cm=function(){var a=this;setTimeout(function(){a.zf(!1)},10);setTimeout(function(){a.zf(!1)},100)};b.prototype.yi=function(){this.Ti.Kk(this.pan.c,this.i.c);for(var a=0;a<this.N.length+this.I.length;a++){if(a<this.N.length)var c=this.N[a];else if(c=this.I[a-this.N.length],c.ld)continue;c.yi()}};b.prototype.Rp=function(a,c){var d=this;var f="<<L>>"+String(d.Ia);f=f.toUpperCase();"U"!=f.charAt(2)&&(d.C.ef=!1);if(0!=d.Fg.length||
!d.C.ef||d.C.Zf||d.C.rh)if(d.ad)d.T.removeChild(d.ad),d.ad=null;else{d.ad=document.createElement("div");var e=d.ad;f="left: "+a+"px;"+("top:\t "+c+"px;")+"z-index: 32000;";f+="position:relative;";f+="display: table;";f+="color: black;";f+="background-color: white;";f+="border: 1px solid lightgray;";f+="box-shadow: 1px 1px 3px #333;";f+="font-family: Verdana, Arial, Helvetica, sans-serif;";f+="font-size: 9pt;";f+="opacity : 0.95;";e.setAttribute("style",f);e.setAttribute("class","gg_contextmenu");
f=document.createElement("style");a=document.createTextNode(".gg_context_row:hover { background-color: #3399FF }");f.type="text/css";f.styleSheet?f.styleSheet.cssText=a.nodeValue:f.appendChild(a);e.appendChild(f);for(a=0;a<d.Fg.length;a++){c=d.Fg[a];var m=document.createElement("div");f="text-align: left;";f+="margin: 0;";f+="padding: 5px 20px;";f+="vertical-align: left;";m.setAttribute("style",f);m.setAttribute("class","gg_context_row");f=document.createElement("a");f.href=c.url;f.target="_blank";
f.innerHTML=c.text;f.setAttribute("style","color: black; text-decoration: none;");m.appendChild(f);e.appendChild(m)}0<d.Fg.length&&(!d.C.ef||d.C.Zf||d.C.rh)&&e.appendChild(document.createElement("hr"));if(d.C.rh&&d.Z){c=[];c.push({text:"Rectilinear Projection",Lg:4});c.push({text:"Stereographic Projection",Lg:9});c.push({text:"Fisheye Projection",Lg:12});for(a=0;a<c.length;a++){m=c[a];var h=document.createElement("div");h.setAttribute("class","gg_context_row");f="text-align: left;";f+="margin: 0;";
f=d.Ka==m.Lg?f+"padding: 5px 20px 5px 7px;":f+"padding: 5px 20px;";f+="vertical-align: left;";f+="cursor: pointer;";h.setAttribute("style",f);h.onclick=function(a){return function(){d.Ni(a,1);d.update()}}(m.Lg);d.Ka==m.Lg?h.innerHTML="&#10687; "+m.text:h.innerHTML=m.text;e.appendChild(h)}d.C.ef&&!d.C.Zf||e.appendChild(document.createElement("hr"))}d.C.Zf&&(a=document.createElement("div"),a.setAttribute("class","gg_context_row"),f="text-align: left;margin: 0;padding: 5px 20px;",f+="vertical-align: left;",
f+="cursor: pointer;",a.setAttribute("style",f),a.onclick=function(){d.ui()},a.innerHTML=d.Jh()?"Exit Fullscreen":"Enter Fullscreen",e.appendChild(a));d.C.ef||(a=document.createElement("div"),f="text-align: left;margin: 0;padding: 5px 20px;",f+="vertical-align: left;",a.setAttribute("style",f),a.setAttribute("class","gg_context_row"),f=document.createElement("a"),f.href=b.Sf("aHR0cDovL3Bhbm8ydnIuY29tLw=="),f.target="_blank",f.innerHTML=b.Sf("Q3JlYXRlZCB3aXRoIFBhbm8yVlI="),7<this.Rh.length&&(f.innerHTML+=
"<br/>"+b.qk(this.Rh).replace(/./gm,function(a){return"&#"+a.charCodeAt(0)+";"})),f.setAttribute("style","color: black; text-decoration: none;"),a.appendChild(f),e.appendChild(a));d.T.insertBefore(d.ad,d.T.firstChild);e.onclick=function(){d.ad&&(d.T.removeChild(d.ad),d.ad=null)};e.oncontextmenu=e.onclick}};b.prototype.fn=function(){var a=this;var c=a.Fa;a.control=c;a.control=c;a.Cm();setTimeout(function(){a.Kg()},10);setTimeout(function(){a.Ql()},200);setTimeout(function(){a.Ke();a.Zg()},10);c.addEventListener&&
(c.addEventListener("touchstart",function(c){a.jq(c)},!1),c.addEventListener("touchmove",function(c){a.iq(c)},!1),c.addEventListener("touchend",function(c){a.hq(c)},!1),c.addEventListener("touchcancel",function(c){a.gq(c)},!1),c.addEventListener("pointerdown",function(c){a.Jl(c)},!1),c.addEventListener("MSPointerDown",function(c){a.Jl(c)},!1),c.addEventListener("MSGestureStart",function(c){a.Sk(c)},!1),c.addEventListener("MSGestureEnd",function(c){a.Rk(c)},!1),c.addEventListener("MSGestureChange",
function(c){a.Qo(c)},!1),c.addEventListener("gesturestart",function(c){a.Sk(c)},!1),c.addEventListener("gesturechange",function(c){a.In(c)},!1),c.addEventListener("gestureend",function(c){a.Rk(c)},!1),c.addEventListener("mousedown",function(c){a.No(c)},!1),c.addEventListener("mousemove",function(c){a.Mo(c)},!1),document.addEventListener("mouseup",function(c){a.Lo(c)},!1),c.addEventListener("mousewheel",function(c){a.Il(c)},!1),c.addEventListener("DOMMouseScroll",function(c){a.Il(c)},!1),document.addEventListener("keydown",
function(c){a.Fo(c)},!1),document.addEventListener("keyup",function(c){a.Go(c)},!1),window.addEventListener("orientationchange",function(){a.Cm()},!1),window.addEventListener("resize",function(){a.Ke()},!1),window.addEventListener("blur",function(){a.Wo()},!1),a.T.addEventListener("webkitfullscreenchange",function(){a.$h()},!1),document.addEventListener("mozfullscreenchange",function(){a.$h()},!1),window.addEventListener("webkitfullscreenchange",function(){a.$h()},!1),document.addEventListener("MSFullscreenChange",
function(){a.$h()},!1));c.oncontextmenu=function(c){void 0===c&&(c=window.event);if(c.target&&!a.yc(c.target))return!0;if(!c.ctrlKey){c=a.jg(c);var b=a.qe();a.Rp(c.x-b.x,c.y-b.y);return!1}return!0};window.addEventListener("deviceorientation",function(c){a.Xo(c.alpha,c.beta,c.gamma,c.absolute)})};b.prototype.jk=function(){for(var a=0;a<this.P.length;a++)if("point"==this.P[a].type&&(this.ba&&this.ba.addSkinHotspot?(this.P[a].Ye(),this.P[a].b=new this.ba.addSkinHotspot(this.P[a])):this.P[a].b=new p.Pm(this,
this.P[a]),this.P[a].b.__div.style.left="-1000px",this.P[a].b.__div.style.top="-1000px",this.P[a].b&&this.P[a].b.__div)){var c=this.Fa.firstChild;c?this.Fa.insertBefore(this.P[a].b.__div,c):this.Fa.appendChild(this.P[a].b.__div)}};b.prototype.Mm=function(){var a,c=document.createElement("fakeelement"),b={OTransition:"oTransitionEnd",MSTransition:"msTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd",transition:"transitionEnd"};for(a in b)if(void 0!==c.style[a])return b[a]};
b.prototype.Hb=function(a){var c=[];"#"==a.substr(0,1)?a=a.substr(1):a="^"+a+"$";a=new RegExp(a,"");for(var b=0;b<this.N.length;b++)a.test(this.N[b].id)&&c.push(this.N[b]);for(b=0;b<this.I.length;b++)a.test(this.I[b].id)&&c.push(this.I[b]);for(b=0;b<this.Sa.length;b++)a.test(this.Sa[b].id)&&c.push(this.Sa[b]);return c};b.prototype.ao=function(a){if("_videopanorama"==a)return this.s.b;a=this.Hb(a);return 0<a.length?a[0].b:null};b.prototype.Wl=function(a,c){var b=this;c.addEventListener("ended",function(a){b.Y("videoended",
{video:a.target})});c.addEventListener("pause",function(a){b.Y("videopaused",{video:a.target})});c.addEventListener("play",function(a){b.Y("videostarted",{video:a.target})});for(var d=0;d<this.I.length;d++)if(this.I[d].id==a)return this.I[d].b=c,this.I[d];d=new p.ik(this);d.registerElement(a,c);return d};b.prototype.Xb=function(a){if(this.Fb){var c=this.ia;if(c)return c.isPlaying(a)}else{if("_main"===a)return!0;a=this.Hb(a);if(0<a.length)return a[0].la?a[0].nf:!a[0].b.ended&&!a[0].b.paused}return!1};
b.prototype.Ae=function(a,c){if(this.Fb){var b=this.ia;b&&b.playSound(a,c)}else try{b=this.Hb(a);for(var d=0;d<b.length;d++){var e=b[d];e.Zc=c&&!isNaN(Number(c))?Number(c)-1:e.loop-1;-1==e.Zc&&(e.Zc=1E7);this.M(e.b);this.Xb(a)&&this.pi(a);e.la?e.Md():(e.b.play(),e.Le&&e.Di());this.Jp(e.id)}}catch(m){this.M(m)}};b.prototype.Rl=function(a,c){a=this.Hb(a);for(var b=0;b<a.length;b++){var d=a[b];this.Xb(d.id)?this.zj(d.id):this.Ae(d.id,c)}};b.prototype.jp=function(a,c){a=this.Hb(a);for(var b=0;b<a.length;b++){var d=
a[b];this.Xb(d.id)?this.pi(d.id):this.Ae(d.id,c)}};b.prototype.zj=function(a){if(this.Fb){var c=this.ia;c&&c.pauseSound(a)}else try{if(c=void 0,"_main"==a){this.ki(a);for(c=0;c<this.N.length;c++)this.N[c].la?this.N[c].Ki():this.N[c].b.pause();for(c=0;c<this.I.length;c++)this.I[c].b.pause()}else{var b=this.Hb(a);for(c=0;c<b.length;c++){var d=b[c];this.ki(d.id);d.la?d.Ki():d.b.pause()}}}catch(e){this.M(e)}};b.prototype.$m=function(a,c){a=this.Hb(a);for(var b=0;b<a.length;b++){var d=a[b];0==c||1==c?
d.mg&&d.mg(1==c):2==c&&d.re&&d.re();!d.gb||-1==this.Sa.indexOf(d)&&-1==this.I.indexOf(d)||(this.Gb=this.md=d)}};b.prototype.pi=function(a){var c;if(this.Fb)(c=this.ia)&&c.stopSound(a);else try{if("_main"===a){this.ki(a);for(c=0;c<this.N.length;c++)this.N[c].la?this.N[c].Te():(this.N[c].b.pause(),this.N[c].b.currentTime=0);for(c=0;c<this.I.length;c++)this.I[c].b.pause(),this.I[c].b.currentTime=0}else{var b=this.Hb(a);for(c=0;c<b.length;c++){var d=b[c];this.ki(d.id);d.la?d.Te():d.b&&d.b.pause&&(d.b.pause(),
d.b.currentTime=0)}}}catch(e){this.M(e)}};b.prototype.ki=function(a){-1==this.Pc.indexOf(a)&&this.Pc.push(a);var c=this.ce.indexOf(a);-1!=c&&this.ce.splice(c,1);"_main"==a&&(this.ce=[])};b.prototype.Jp=function(a){-1!=this.Pc.indexOf("_main")&&-1==this.ce.indexOf(a)&&this.ce.push(a);a=this.Pc.indexOf(a);-1!=a&&this.Pc.splice(a,1)};b.prototype.Sp=function(a){a=this.Hb(a);return 0<a.length?(a=a[0],a.la?a.jn():a.b?a.b.currentTime:0):0};b.prototype.Tp=function(a,c){a=this.Hb(a);0<a.length&&(a=a[0],a.la?
(0>c&&(c=0),c>a.ph.duration&&(c=a.ph.duration-.1),a.kn(c)):a.b&&(0>c&&(c=0),c>a.b.duration&&(c=a.b.duration-.1),a.b.currentTime=c))};b.prototype.Op=function(a,c){if(this.Fb){var b=this.ia;b&&b.setVolume(a,c)}else try{b=void 0;var d=Number(c);1<d&&(d=1);0>d&&(d=0);"_videopanorama"===a&&this.s.b&&(this.s.b.volume=d);if("_main"===a){this.V=d;for(b=0;b<this.N.length;b++)this.N[b].b.volume=this.N[b].level*this.V;for(b=0;b<this.I.length;b++)this.I[b].b.volume=this.I[b].level*this.V;this.s.b&&(this.s.b.volume=
this.V)}else{var e=this.Hb(a);this.M(e);for(b=0;b<e.length;b++){var f=e[b];f.b&&null!=f.b.volume&&(f.b.volume=d*this.V);f.level=d}}}catch(t){this.M(t)}};b.prototype.sn=function(a,c){if(this.Fb){var b=this.ia;b&&b.changeVolume(a,c)}else try{var d=b=void 0;"_videopanorama"===a&&this.s.b&&(this.s.b.volume=this.s.b.volume+Number(c));if("_main"===a){b=this.V;b+=Number(c);1<b&&(b=1);0>b&&(b=0);this.V=b;for(d=0;d<this.N.length;d++)this.N[d].b.volume=this.N[d].level*this.V;for(d=0;d<this.I.length;d++)this.I[d].b.volume=
this.I[d].level*this.V;this.s.b&&(this.s.b.volume=this.V)}else{var e=this.Hb(a);for(d=0;d<e.length;d++){var f=e[d];b=f.level;b+=Number(c);1<b&&(b=1);0>b&&(b=0);f.level=b;f.b&&null!=f.b.volume&&(f.b.volume=b*this.V)}}}catch(t){this.M(t)}};b.prototype.zp=function(a,c){a=this.Hb(a);for(var b=0;b<a.length;b++){var d=a[b];0==c?(d.Cf(!1),d.hb=!1):1==c?(d.Cf(!0),d.hb=!0):2==c&&d.b&&("visible"==d.b.style.visibility?(d.Cf(!1),d.hb=!1):(d.Cf(!0),d.hb=!0))}};b.prototype.qm=function(){try{for(var a=this,c=!1,
b=!1,d=0;d<this.N.length;d++){var e=this.N[d];if(-1!=e.loop&&!this.Xb(e.id)&&-1==this.Pc.indexOf(e.id)&&(-1==this.Pc.indexOf("_main")||-1!=this.ce.indexOf(e.id)))if(this.pa&&this.La.enabled&&4!=e.mode&&6!=e.mode)if(this.La.zk){if(e.la)e.Md();else{var f=e.b.play();if(void 0!==f)f.then(function(){})["catch"](function(){});e.b.currentTime=0}e.ka=0;b=!0}else c=!0;else if(4!=e.mode&&6!=e.mode&&("_background"!=e.id||!this.Xb(e.id)))if(e.la)e.Md();else{f=e.b.play();if(void 0!==f)f.then(function(){})["catch"](function(){});
e.b.currentTime&&(e.b.currentTime=0)}}c&&setTimeout(function(){a.La.Zp()},1E3*this.La.xb);b&&(this.La.Vp=this.pa.currentTime,this.La.Up=setInterval(function(){a.La.Fn()},10))}catch(t){this.M(t)}};b.prototype.Ik=function(){for(var a=0;a<this.La.Xg.length;a++)this.La.Hk(this.La.Xg[a])};b.prototype.$l=function(){for(var a;0<this.P.length;)a=this.P.pop(),a.b&&(this.Fa.removeChild(a.b.__div),delete a.b),a.b=null;this.Y("hotspotsremoved",{})};b.prototype.Lj=function(a){this.ih=a;0!=a?this.T.style.zIndex=
a.toString():this.T.style.zIndex="auto";this.Ga&&this.Ga.Xc&&(this.Ga.Xc.zIndex=(a+4).toString());this.Fa.style.zIndex=(a+4).toString();this.ya.style.zIndex=(a+3).toString();this.Aa.style.zIndex=(a+5).toString();for(var c=0;c<this.I.length+this.Sa.length;c++){var b=c<this.I.length?this.I[c]:this.Sa[c-this.I.length];b.b&&(b.b.style.zIndex=(a+(b.gb?8E4:0)).toString())}};b.prototype.zf=function(a){var c=this.isFullscreen!==a;this.isFullscreen!==a&&(this.isFullscreen=a,this.update(100));if(this.isFullscreen){if(this.zi)try{this.T.webkitRequestFullScreen?
this.T.webkitRequestFullScreen():this.T.mozRequestFullScreen?this.T.mozRequestFullScreen():this.T.msRequestFullscreen?this.T.msRequestFullscreen():this.T.requestFullScreen?this.T.requestFullScreen():this.T.requestFullscreen&&this.T.requestFullscreen()}catch(k){this.M(k)}this.T.style.position="absolute";a=this.qe();this.T.style.left=window.pageXOffset-a.x+this.margin.left+"px";this.T.style.top=window.pageYOffset-a.y+this.margin.top+"px";this.Lj(10);document.body.style.overflow="hidden";c&&(this.ja&&
this.ja.ggEnterFullscreen&&this.ja.ggEnterFullscreen(),this.Y("fullscreenenter",{}))}else{if(this.zi)try{document.webkitIsFullScreen?document.webkitCancelFullScreen():document.mozFullScreen?document.mozCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():document.fullScreen&&(document.cancelFullScreen?document.cancelFullScreen():document.exitFullscreen&&document.exitFullscreen())}catch(k){this.M(k)}this.T.style.position="relative";this.T.style.left="0px";this.T.style.top="0px";
this.Lj(0);document.body.style.overflow="";c&&(this.ja&&this.ja.ggExitFullscreen&&this.ja.ggExitFullscreen(),this.Y("fullscreenexit",{}))}this.Ke()};b.prototype.ui=function(){this.zf(!this.isFullscreen)};b.prototype.Dn=function(){this.zf(!0)};b.prototype.exitFullscreen=function(){this.zf(!1)};b.prototype.Rn=function(){return this.isFullscreen};b.prototype.Xp=function(a,c,b){this.l.Kd=this.l.oh;this.l.Kf=this.l.eh;this.l.enabled=!0;this.l.Pe=this.l.enabled;this.l.active=!0;this.l.jd=0;var d=new Date;
this.l.Bd=0;this.l.startTime=d.getTime();a&&0!=a&&(this.l.speed=a);c&&(this.l.timeout=c);b&&(this.l.ti=b);this.Y("autorotatechanged",{})};b.prototype.$p=function(){this.l.active=!1;this.l.enabled=!1;this.l.Pe=this.l.enabled;this.Ne=this.l.Uh=!1;this.u.active&&this.u.Hg&&(this.u.active=!1,this.u.Hg=!1,this.u.Eb=0);this.Y("autorotatechanged",{})};b.prototype.eq=function(){this.l.enabled=!this.l.active;this.l.Pe=this.l.enabled;this.l.active=this.l.enabled;this.l.jd=0;if(this.l.enabled){var a=new Date;
this.l.Bd=0;this.l.startTime=a.getTime();this.l.Kd=this.l.oh;this.l.Kf=this.l.eh}this.Y("autorotatechanged",{})};b.prototype.Wp=function(a){this.qb.nd&&this.fm();this.Lb="";a&&""!=a&&(this.l.Kd=a);this.l.Pe=this.l.enabled;this.l.Kf=!0;this.l.enabled=!0;this.l.active=!0;this.l.jd=0;a=new Date;this.l.Bd=0;this.l.startTime=a.getTime();this.Y("autorotatechanged",{})};b.prototype.fm=function(){this.qb.nd=!1;this.qb.bj=!0;this.l.active=this.B.fe;this.za.splice(this.za.indexOf(this.w),1);0<this.za.length&&
(this.w=this.za[0]);this.Lb="";this.Og(this.B.Th);this.Pg(this.B.Ai);this.Ng(this.B.Lh);this.B.fe=!1;this.hf=(new Date).getTime()};b.prototype.yk=function(a){if(this.Nd=document.getElementById(a)){this.Nd.innerHTML="";this.T=document.createElement("div");this.T.onselectstart=function(){return!1};W&&this.T.setAttribute("id","viewport");a="top:\t0px;left: 0px;position:relative;-ms-touch-action: none;touch-action: none;text-align: left;"+(this.Ia+"user-select: none;");this.T.setAttribute("style",a);
this.Nd.appendChild(this.T);this.D=document.createElement("div");a="top:\t0px;left: 0px;width:  100px;height: 100px;overflow: hidden;position:absolute;-ms-touch-action: none;touch-action: none;"+(this.Ia+"user-select: none;");W&&this.D.setAttribute("id","viewer");this.D.setAttribute("style",a);this.T.appendChild(this.D);if(this.Fb){var c=document.createElement("div");a="top:\t0px;left: 0px;width:  100%;height: 100%;overflow: hidden;position:absolute;-ms-touch-action: none;touch-action: none;"+(this.Ia+
"user-select: none;");c.setAttribute("id",this.dj);c.setAttribute("style",a);this.D.appendChild(c)}this.Ga&&(this.Ga.Xc=document.createElement("canvas"),W&&this.Ga.Xc.setAttribute("id","lensflarecanvas"),a="top:\t0px;left: 0px;width:  100px;height: 100px;overflow: hidden;position:absolute;"+(this.Ia+"user-select: none;"),a+=this.Ia+"pointer-events: none;",this.Ga.Xc.setAttribute("style",a),this.T.appendChild(this.Ga.Xc));this.Fa=document.createElement("div");W&&this.Fa.setAttribute("id","hotspots");
a="top:\t0px;left: 0px;width:  100px;height: 100px;overflow: hidden;position:absolute;";this.oj&&(a+="background-image: url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7);");this.Jc&&!this.Z&&(a+=this.Ia+"transform: translateZ(9999999px);");a+=this.Ia+"user-select: none;";this.Fa.setAttribute("style",a);this.T.appendChild(this.Fa);this.ya=document.createElement("canvas");W&&this.ya.setAttribute("id","hotspotcanvas");a="top:\t0px;left: 0px;width:  100px;height: 100px;overflow: hidden;position:absolute;"+
(this.Ia+"user-select: none;");a+=this.Ia+"pointer-events: none;";this.ya.setAttribute("style",a);this.T.appendChild(this.ya);this.Aa=document.createElement("div");W&&this.Aa.setAttribute("id","hotspottext");this.Aa.setAttribute("style","top:\t0px;left: 0px;position:absolute;padding: 3px;visibility: hidden;");this.Aa.innerHTML=" Hotspot text!";this.T.appendChild(this.Aa);this.divSkin=this.ja=this.Fa;this.Lj(0)}else alert("container not found!")};b.prototype.Ak=function(a){this.Da=!0;return function(){a.Pa&&
(a.h&&a.h.complete?(a.loaded=!0,a.Pa.drawImage(a.h,0,0,a.width,a.height),a.h=null,a.Ed=null):a.Ed&&a.Ed.complete&&!a.loaded&&(a.Pa.drawImage(a.Ed,0,0,a.width,a.height),a.Ed=null))}};b.prototype.xk=function(a){var c,b=128;this.h.sf&&(this.D.style.backgroundColor=this.h.sf.replace("0x","#"));a?(b=this.Uf,this.Gf=1):this.uc>b&&(b=this.uc);for(c=0;6>c;c++){var d=this.pb.fb[c];a?(d.width=this.Uf,d.height=this.Uf):(d.K=document.createElement("canvas"),d.K.width=this.uc,d.K.height=this.uc,d.width=this.uc,
d.height=this.uc,d.Pa=d.K.getContext("2d"));var e="position:absolute;";e+="left: 0px;";e+="top: 0px;";e+="width: "+b+"px;";e+="height: "+b+"px;";a&&(e+="outline: 1px solid transparent;");e+=this.Ia+"transform-origin: 0% 0%;";e+="-webkit-user-select: none;";e+=this.Ia+"transform: ";var f="";var h=1;this.uf&&(h=100);f=4>c?f+("rotateY("+-90*c+"deg)"):f+("rotateX("+(4==c?-90:90)+"deg)");this.uf&&(f+=" scale("+h+")");f+=" translate3d("+-b/2+"px,"+-b/2+"px,"+-b*h/(2*this.Gf)+"px)";e+=f+";";d.fl=f;a||(d.K.setAttribute("style",
e),this.D.insertBefore(d.K,this.D.firstChild))}if(!a){for(c=0;6>c;c++)d=this.pb.fb[c],""!=this.We[c]&&(d.Ed=new Image,d.Ed.crossOrigin=this.crossOrigin,d.Ed.onload=this.Ak(d),d.Ed.setAttribute("src",this.nc(this.We[c])),this.Sb.push(d.Ed));for(c=0;6>c;c++)d=this.pb.fb[c],d.loaded=!1,d.h=new Image,d.h.crossOrigin=this.crossOrigin,d.h.onload=this.Ak(d),d.h.setAttribute("src",this.nc(this.th[c])),this.Sb.push(d.h)}};b.prototype.fi=function(){var a;this.Ba.da.x=0;this.Ba.da.y=0;if(this.Sc){for(a=0;a<
this.pb.fb.length;a++)this.pb.fb[a].K&&this.pb.fb[a].K.setAttribute&&(this.pb.fb[a].K.setAttribute("src",this.Ek),this.D.removeChild(this.pb.fb[a].K));if(this.h.J){for(a=0;a<this.h.J.length;a++){var c=this.h.J[a];for(d in c.U)if(c.U.hasOwnProperty(d)){var b=c.U[d];b.visible=!1;b.K&&(b.Pa&&b.Pa.clearRect(0,0,b.Pa.canvas.width,b.Pa.canvas.height),this.vi.push(b.K));b.h&&delete b.h;b.ib&&(this.H.deleteTexture(b.ib),this.fd--);b.Pa=null;b.K=null;b.h=null}delete c.U}delete this.h.J;this.h.J=null}}this.na.fi();
var d=[];for(a=0;a<this.I.length;a++)c=this.I[a],c.ld?d.push(c):c.Be();for(a=0;a<this.Sa.length;a++)this.Sa[a].Be();this.A.wg=-1;this.ya.style.visibility="hidden";this.kb=0;this.Ee=[];this.La.Xg=[];for(a=0;a<this.N.length;a++)c=this.N[a],0==c.mode||c.Ml||this.Ee.push(c);this.I=d;this.Sa=[];this.s.b&&(this.T.removeChild(this.s.b),this.s.b=null,a=this.Hb("_videopanorama"),0<a.length&&(a[0].b=null));this.s.hd=!1;this.s.Kh=!1};b.prototype.bl=function(){var a=1,c=-1!=navigator.userAgent.indexOf("Mac");
window.devicePixelRatio&&c&&(a=window.devicePixelRatio);return{hh:screen.width*a,pg:screen.height*a}};b.prototype.Wk=function(){var a=this.bl();return a.hh>a.pg?a.hh:a.pg};b.prototype.Cj=function(a,c){var b=(new DOMParser).parseFromString(a,"text/xml");this.Ui=a;this.Vl(b,c);this.ia&&(this.M("Apply to Flash player"),this.ia.readConfigString(this.Ui),this.ia.setLocked(!0),this.ia.setSlaveMode(!0))};b.prototype.Ul=function(a,c,b){try{var d=void 0;d=new XMLHttpRequest;d.open("GET",a,!1);d.send(null);
if(d.responseXML){var e=a.lastIndexOf("/");0<=e&&(this.Ld=a.substr(0,e+1));2<=arguments.length&&null!=c&&(this.Ld=c);this.Cj(d.responseText,b)}else alert("Error loading panorama XML")}catch(m){alert("Error:"+m)}};b.prototype.lp=function(a,b,d,f){var c=new XMLHttpRequest;var k=this;c.onload=function(e){if(4<=c.readyState)if(c.responseXML){var g=a.lastIndexOf("/");0<=g&&(k.Ld=a.substr(0,g+1));3<=arguments.length&&null!=d&&(k.Ld=d);k.Cj(c.responseText,f);b&&b()}else alert("Error loading panorama XML");
else console.error("Wrong state loading XML:"+c.statusText)};c.onerror=function(){console.error("Error loading XML:"+c.statusText)};c.open("GET",a,!0);c.send(null)};b.prototype.Li=function(a){var c="";"{"==a.charAt(0)&&(c=a.substr(1,a.length-2));(a=this.Od[c])&&(c=a);a={oldNodeId:this.Wa,nodeId:c};this.Y("beforechangenodeid",a);""!=this.Wa&&-1==this.ck.indexOf(this.Wa)&&this.ck.push(this.Wa);this.Cl=this.Wa;this.Wa=c;this.M("change active node: "+c);this.ba&&this.ba.changeActiveNode&&this.ba.changeActiveNode("{"+
c+"}");this.Y("changenodeid",a)};b.prototype.Tk=function(){return this.Wa};b.prototype.Xk=function(){if(0<this.Ma.length){var a=this.Ma.indexOf(this.Wa);a++;a>=this.Ma.length&&(a=0);return this.Ma[a]}return""};b.prototype.po=function(){if(0<this.Ma.length){var a=this.Ma.indexOf(this.Wa);a--;0>a&&(a=this.Ma.length-1);return this.Ma[a]}return""};b.prototype.Wn=function(){return this.Cl};b.prototype.Uo=function(a){return-1!=this.ck.indexOf(a)};b.prototype.Vl=function(a,c){var b=a.firstChild;this.Ie=
[];this.Ma=[];this.Od=[];for(a=b.firstChild;a;){if("map"==a.nodeName){var d={},e=a.getAttributeNode("title");e&&(d.title=e.nodeValue.toString());e=a.getAttributeNode("type");d.type=e.nodeValue.toString();"web"==d.type?(e=a.getAttributeNode("mapprovider"),d.mapprovider=e.nodeValue.toString(),(e=a.getAttributeNode("mapstyle"))&&(d.mapstyle=e.nodeValue.toString()),(e=a.getAttributeNode("googlecustomstylecode"))&&(d.googlecustomstylecode=e.nodeValue.toString()),(e=a.getAttributeNode("mapurltemplate"))&&
(d.mapurltemplate=e.nodeValue.toString()),(e=a.getAttributeNode("mapmaxzoom"))&&(d.mapmaxzoom=Number(e.nodeValue)),(e=a.getAttributeNode("mapkey"))&&(d.mapkey=e.nodeValue.toString()),(e=a.getAttributeNode("styleurl"))&&(d.styleurl=e.nodeValue.toString())):(e=a.getAttributeNode("width"),d.width=Number(e.nodeValue),e=a.getAttributeNode("height"),d.height=Number(e.nodeValue),e=a.getAttributeNode("zoomlevels"),d.zoomlevels=Number(e.nodeValue),e=a.getAttributeNode("tileformat"),d.tileformat=e.nodeValue.toString(),
e=a.getAttributeNode("bgcolor"),d.bgcolor=e.nodeValue.toString(),e=a.getAttributeNode("transparent"),d.transparent=1==e.nodeValue,e=a.getAttributeNode("floorplannorth"),d.floorplannorth=Number(e.nodeValue));e=a.getAttributeNode("id");this.Sh[e.nodeValue.toString()]=d}a=a.nextSibling}if("tour"==b.nodeName){this.gf=!0;a="";(e=b.getAttributeNode("start"))&&(a=e.nodeValue.toString());this.hasOwnProperty("startNode")&&this.startNode&&(a=String(this.startNode),this.startNode="");this.hasOwnProperty("startView")&&
this.startView&&("object"===typeof this.startView&&null!==this.startView?c=this.startView:""!=this.startView&&(c=String(this.startView)),this.startView="");d=b.firstChild;var f="";for(b="";d;){if("panorama"==d.nodeName){if(e=d.getAttributeNode("id"))f=e.nodeValue.toString(),""==a&&(a=f),""==b&&(b=f),this.Ie[f]=d,this.Ma.push(f);for(e=d.firstChild;e;){if("userdata"==e.nodeName){var h=this.bg(e);this.fh[f]=h;f==a&&(this.fh._first=h);h.customnodeid&&(this.Od[h.customnodeid]=f);this.zc[f]=this.Fk(e);
this.lf[f]=this.Gk(e)}e=e.nextSibling}}if("masternode"==d.nodeName)for(e=d.firstChild;e;)"userdata"==e.nodeName&&(h=this.bg(e),this.fh._master=h),e=e.nextSibling;d=d.nextSibling}this.Ie.hasOwnProperty(a)||(e=this.Od[a])&&(a=e);this.Ie.hasOwnProperty(a)||(this.Lc("Start node "+a+" not found!"),a=b);this.Bj(this.Ie[a],c);this.Li("{"+a+"}");this.Wa=a}else this.gf=!1,this.Bj(b,c),this.Li(""),this.Ma.push("");this.Y("configloaded",{});this.Y("changenode",{})};b.prototype.Bj=function(a,b){var c=this;this.$l();
this.Ga&&this.Ga.np();this.xf(this.eb);this.fi();this.xg=0;for(var d=a.firstChild,e,f,h=0;d;){if("view"==d.nodeName){if(e=d.getAttributeNode("fovmode"))this.f.mode=Number(e.nodeValue);e=d.getAttributeNode("pannorth");this.pan.xj=1*(e?e.nodeValue:0);for(var g=d.firstChild;g;){"start"==g.nodeName&&(e=g.getAttributeNode("pan"),this.pan.c=Number(e?e.nodeValue:0),this.pan.Qa=this.pan.c,e=g.getAttributeNode("tilt"),this.i.c=Number(e?e.nodeValue:0),this.i.Qa=this.i.c,e=g.getAttributeNode("roll"),this.O.c=
Number(e?e.nodeValue:0),this.O.Qa=this.O.c,e=g.getAttributeNode("fov"),this.f.c=Number(e?e.nodeValue:70),this.f.Qa=this.f.c,e=g.getAttributeNode("projection"),this.ai=Number(e?e.nodeValue:4),this.Nc(this.ai));"min"==g.nodeName&&(e=g.getAttributeNode("pan"),this.pan.min=1*(e?e.nodeValue:0),e=g.getAttributeNode("tilt"),this.i.min=1*(e?e.nodeValue:-90),e=g.getAttributeNode("fov"),this.f.min=1*(e?e.nodeValue:5),1E-20>this.f.min&&(this.f.min=1E-20),e=g.getAttributeNode("fovpixel"),this.f.Gg=1*(e?e.nodeValue:
0));if("max"==g.nodeName){e=g.getAttributeNode("pan");this.pan.max=1*(e?e.nodeValue:0);e=g.getAttributeNode("tilt");this.i.max=1*(e?e.nodeValue:90);e=g.getAttributeNode("fov");this.f.max=1*(e?e.nodeValue:120);180<=this.f.max&&(this.f.max=179.9);if(e=g.getAttributeNode("fovstereographic"))this.f.vj=1*e.nodeValue;if(e=g.getAttributeNode("fovfisheye"))this.f.uj=1*e.nodeValue;if(e=g.getAttributeNode("scaletofit"))this.C.em=1==e.nodeValue}if("flyin"==g.nodeName){if(e=g.getAttributeNode("projection"))this.wc.Eb=
Number(e.nodeValue);if(e=g.getAttributeNode("pan"))this.wc.pan=parseFloat(e.nodeValue);if(e=g.getAttributeNode("tilt"))this.wc.i=parseFloat(e.nodeValue);if(e=g.getAttributeNode("fov"))this.wc.f=parseFloat(e.nodeValue)}g=g.nextSibling}}if("autorotate"==d.nodeName){if(e=d.getAttributeNode("speed"))this.l.speed=1*e.nodeValue;if(e=d.getAttributeNode("delay"))this.l.timeout=1*e.nodeValue;if(e=d.getAttributeNode("returntohorizon"))this.l.ti=1*e.nodeValue;if(e=d.getAttributeNode("nodedelay"))this.l.Yh=1*
e.nodeValue;if(e=d.getAttributeNode("noderandom"))this.l.wj=1==e.nodeValue;this.Qd&&(this.l.enabled=!0,this.l.Pe=!0,this.l.active=!1);this.l.Bd=0;if(e=d.getAttributeNode("startloaded"))this.l.Ug=1==e.nodeValue,this.l.Ug&&(this.l.active=!1);if(e=d.getAttributeNode("useanimation"))this.l.eh=1==e.nodeValue,this.l.Kf=this.l.eh;if(e=d.getAttributeNode("syncanimationwithvideo"))this.l.Sj=1==e.nodeValue}if("animation"==d.nodeName){if(e=d.getAttributeNode("syncanimationwithvideo"))this.l.Sj=1==e.nodeValue;
if(e=d.getAttributeNode("useinautorotation"))this.l.eh=1==e.nodeValue;if(e=d.getAttributeNode("animsequence"))this.l.oh=e.nodeValue,this.Qd&&(this.l.Kd=this.l.oh);this.za=[];for(g=d.firstChild;g;){if("clip"==g.nodeName){this.w=new p.ek;if(e=g.getAttributeNode("animtitle"))this.w.Oe=e.nodeValue.toString();if(e=g.getAttributeNode("cliptitle"))this.w.cb=e.nodeValue.toString();if(e=g.getAttributeNode("nodeid"))this.w.Fq=e.nodeValue.toString();if(e=g.getAttributeNode("length"))this.w.length=Number(e.nodeValue);
if(e=g.getAttributeNode("animtype"))this.w.cn=Number(e.nodeValue);if(e=g.getAttributeNode("nextcliptitle"))this.w.Kl=e.nodeValue.toString();if(e=g.getAttributeNode("nextclipnodeid"))this.w.Xh=e.nodeValue.toString();if(e=g.getAttributeNode("nextclipstartview"))this.w.To=e.nodeValue.toString();if(e=g.getAttributeNode("transitiontype"))this.w.kq=Number(e.nodeValue);var q=g.firstChild;for(this.w.W=[];q;){if("keyframe"==q.nodeName){var n=new p.Ec;if(e=q.getAttributeNode("time"))n.time=Number(e.nodeValue);
if(e=q.getAttributeNode("value"))n.value=Number(e.nodeValue);if(e=q.getAttributeNode("valuestring"))n.ak=e.nodeValue.toString();if(e=q.getAttributeNode("transitiontime"))n.xb=Number(e.nodeValue);e=q.getAttributeNode("type");var y=0;e&&(n.type=Number(e.nodeValue),y=Number(e.nodeValue));if(e=q.getAttributeNode("property"))n.ub=Number(e.nodeValue);if(e=q.getAttributeNode("additionaltrackid"))n.mh=e.nodeValue.toString();if(1==y||2==y){if(e=q.getAttributeNode("bezierintime"))n.ge=Number(e.nodeValue);if(e=
q.getAttributeNode("bezierinvalue"))n.Vc=Number(e.nodeValue);if(e=q.getAttributeNode("bezierouttime"))n.he=Number(e.nodeValue);if(e=q.getAttributeNode("bezieroutvalue"))n.ie=Number(e.nodeValue)}this.w.W.push(n)}q=q.nextSibling}this.za.push(this.w)}g=g.nextSibling}}"input"==d.nodeName&&(f||(f=d));if(f)for(q=0;6>q;q++)e=f.getAttributeNode("prev"+q+"url"),this.We[q]=e?String(e.nodeValue):"";"altinput"==d.nodeName&&(g=0,(e=d.getAttributeNode("screensize"))&&(g=1*e.nodeValue),0<g&&g<=this.Wk()&&g>h&&(h=
g,f=d));if("control"==d.nodeName&&this.Qd){if(e=d.getAttributeNode("simulatemass"))this.Ba.enabled=1==e.nodeValue;if(e=d.getAttributeNode("rubberband"))this.C.dm=1==e.nodeValue;if(e=d.getAttributeNode("locked"))this.C.Ab=1==e.nodeValue;e&&(this.C.ue=1==e.nodeValue);if(e=d.getAttributeNode("lockedmouse"))this.C.Ab=1==e.nodeValue;if(e=d.getAttributeNode("lockedkeyboard"))this.C.ue=1==e.nodeValue;if(e=d.getAttributeNode("lockedkeyboardzoom"))this.C.Jo=1==e.nodeValue;if(e=d.getAttributeNode("lockedwheel"))this.C.kd=
1==e.nodeValue;if(e=d.getAttributeNode("invertwheel"))this.C.pl=1==e.nodeValue;if(e=d.getAttributeNode("speedwheel"))this.C.mm=1*e.nodeValue;if(e=d.getAttributeNode("invertcontrol"))this.C.Ad=1==e.nodeValue;if(e=d.getAttributeNode("sensitivity"))this.C.sensitivity=1*e.nodeValue,1>this.C.sensitivity&&(this.C.sensitivity=1);if(e=d.getAttributeNode("dblclickfullscreen"))this.C.Wi=1==e.nodeValue;if(e=d.getAttributeNode("contextfullscreen"))this.C.Zf=1==e.nodeValue;if(e=d.getAttributeNode("contextprojections"))this.C.rh=
1==e.nodeValue;if(e=d.getAttributeNode("hideabout"))this.C.ef=1==e.nodeValue;for(g=d.firstChild;g;)"menulink"==g.nodeName&&(q={text:"",url:""},e=g.getAttributeNode("text"),q.text=e.nodeValue,e=g.getAttributeNode("url"),q.url=e.nodeValue,this.Fg.push(q)),g=g.nextSibling}if("transition"==d.nodeName&&this.Qd){if(e=d.getAttributeNode("enabled"))this.B.enabled=1==e.nodeValue;if(e=d.getAttributeNode("blendtime"))this.B.Tf=1*e.nodeValue;if(e=d.getAttributeNode("blendcolor"))this.B.Re=e.nodeValue.toString();
if(e=d.getAttributeNode("type"))this.B.type=e.nodeValue.toString();if(e=d.getAttributeNode("softedge"))this.B.Bc=1*e.nodeValue;if(e=d.getAttributeNode("zoomin"))this.B.Oa=1*e.nodeValue;if(e=d.getAttributeNode("zoomout"))this.B.ec=1*e.nodeValue;if(e=d.getAttributeNode("zoomfov"))this.B.Of=1*e.nodeValue;if(e=d.getAttributeNode("zoomspeed"))this.B.de=1*e.nodeValue;if(e=d.getAttributeNode("zoomoutpause"))this.B.Pf=1==e.nodeValue;"cut"==this.B.type&&(this.B.Tf=0)}if("soundstransition"==d.nodeName){if(e=
d.getAttributeNode("enabled"))this.La.enabled=1==e.nodeValue;if(e=d.getAttributeNode("transitiontime"))this.La.xb=1*e.nodeValue;if(e=d.getAttributeNode("crossfade"))this.La.zk=1==e.nodeValue}if("flyintransition"==d.nodeName){if(e=d.getAttributeNode("enabled"))this.qb.enabled=1==e.nodeValue&&this.Z;if(e=d.getAttributeNode("speed"))this.qb.speed=1*e.nodeValue}"userdata"==d.nodeName&&(this.userdata=this.Lf=this.bg(d),this.zc[a.id]||(this.zc[a.id]=this.Fk(d),this.lf[a.id]=this.Gk(d)));if("hotspots"==
d.nodeName)for(g=d.firstChild;g;){if("label"==g.nodeName&&this.Qd){q=this.A.Uj;if(e=g.getAttributeNode("enabled"))q.enabled=1==e.nodeValue;if(e=g.getAttributeNode("width"))q.width=1*e.nodeValue;if(e=g.getAttributeNode("height"))q.height=1*e.nodeValue;if(e=g.getAttributeNode("textcolor"))q.Vj=1*e.nodeValue;if(e=g.getAttributeNode("textalpha"))q.Tj=1*e.nodeValue;if(e=g.getAttributeNode("background"))q.background=1==e.nodeValue;if(e=g.getAttributeNode("backgroundalpha"))q.gc=1*e.nodeValue;if(e=g.getAttributeNode("backgroundcolor"))q.hc=
1*e.nodeValue;if(e=g.getAttributeNode("border"))q.Ji=1*e.nodeValue;if(e=g.getAttributeNode("bordercolor"))q.kc=1*e.nodeValue;if(e=g.getAttributeNode("borderalpha"))q.jc=1*e.nodeValue;if(e=g.getAttributeNode("borderradius"))q.Ii=1*e.nodeValue;if(e=g.getAttributeNode("wordwrap"))q.Bi=1==e.nodeValue}if("polystyle"==g.nodeName&&this.Qd){if(e=g.getAttributeNode("mode"))this.A.mode=1*e.nodeValue;if(e=g.getAttributeNode("bordercolor"))this.A.kc=1*e.nodeValue;if(e=g.getAttributeNode("backgroundcolor"))this.A.hc=
1*e.nodeValue;if(e=g.getAttributeNode("borderalpha"))this.A.jc=1*e.nodeValue;if(e=g.getAttributeNode("backgroundalpha"))this.A.gc=1*e.nodeValue;if(e=g.getAttributeNode("handcursor"))this.A.cf=1==e.nodeValue}e=void 0;"hotspot"==g.nodeName&&(e=new p.lh(this),e.type="point",e.Nb(g),this.P.push(e));"polyhotspot"==g.nodeName&&(e=new p.lh(this),e.type="poly",e.Nb(g),this.P.push(e));g=g.nextSibling}if("sounds"==d.nodeName||"media"==d.nodeName)for(g=d.firstChild;g;){if("sound"==g.nodeName&&!this.Ll)for(e=
new p.Xm(this),e.Nb(g),this.Fb||e.addElement(),q=0;q<this.Ee.length;q++)e.id==this.Ee[q].id&&(this.Ee.splice(q,1),q--);"video"==g.nodeName&&(e=new p.ik(this),e.Nb(g),this.Fb||e.addElement());"image"==g.nodeName&&(e=new p.Vm(this),e.Nb(g),this.Fb||e.addElement());"lensflare"==g.nodeName&&this.Ga&&(e=new p.Wm(this),e.Nb(g),this.Ga.Cg.push(e));g=g.nextSibling}d=d.nextSibling}for(q=0;q<this.Ee.length;q++){a=this.Ee[q];if(this.pa&&this.La.enabled&&this.Xb(a.id))this.La.Xg.push(a);else{try{a.la?a.Te():
a.b.pause()}catch(v){this.M(v)}a.Be()}this.N.splice(this.N.indexOf(a),1)}1!=this.B.Oa&&2!=this.B.Oa&&this.Ik();this.jb.hi=!0;b&&("object"===typeof b&&null!==b?(b.hasOwnProperty("pan")&&this.Af(Number(b.pan)),b.hasOwnProperty("tilt")&&this.Bf(Number(b.tilt)),b.hasOwnProperty("fov")&&this.yf(Number(b.fov)),b.hasOwnProperty("projection")&&this.Nc(Number(b.projection))):""!=b&&(b=b.toString().split("/"),4<b.length&&this.Nc(Number(b[4])),0<b.length&&(e=String(b[0]),"N"==e.charAt(0)?this.Jj(Number(e.substr(1))):
"S"==e.charAt(0)?this.Jj(-180+Number(e.substr(1))):this.Af(Number(e))),1<b.length&&this.Bf(Number(b[1])),2<b.length&&this.yf(Number(b[2]))));if(f){for(q=0;6>q;q++)(e=f.getAttributeNode("tile"+q+"url"))&&(this.th[q]=String(e.nodeValue)),e=f.getAttributeNode("tile"+q+"url1");for(q=0;6>q;q++)(e=f.getAttributeNode("prev"+q+"url"))&&(this.We[q]=String(e.nodeValue));if(e=f.getAttributeNode("tilesize"))this.uc=1*e.nodeValue;if(e=f.getAttributeNode("canvassize"))this.Uf=Number(e.nodeValue);if(e=f.getAttributeNode("tilescale"))this.Gf=
1*e.nodeValue;if(e=f.getAttributeNode("leveltileurl"))this.h.Hl=e.nodeValue;if(e=f.getAttributeNode("leveltilesize"))this.h.G=Number(e.nodeValue);if(e=f.getAttributeNode("levelbias"))this.h.Fl=Number(e.nodeValue);if(e=f.getAttributeNode("levelbiashidpi"))this.h.Gl=Number(e.nodeValue);e=f.getAttributeNode("overlap");this.$a.O=0;this.$a.pitch=0;e&&(this.h.Ja=Number(e.nodeValue));if(e=f.getAttributeNode("levelingroll"))this.$a.O=Number(e.nodeValue);if(e=f.getAttributeNode("levelingpitch"))this.$a.pitch=
Number(e.nodeValue);this.kb=0;(e=f.getAttributeNode("flat"))&&1==e.nodeValue&&(this.kb=2);e=f.getAttributeNode("width");this.h.width=1*(e?e.nodeValue:1);e=f.getAttributeNode("height");this.h.height=1*(e?e.nodeValue:this.h.width);this.s.src=[];this.h.J=[];for(g=f.firstChild;g;){if("preview"==g.nodeName){if(e=g.getAttributeNode("color"))this.h.sf=e.nodeValue;if(e=g.getAttributeNode("strip"))this.h.Sl=1==e.nodeValue}if("video"==g.nodeName){if(e=g.getAttributeNode("format"))"3x2"==e.nodeValue&&(this.s.format=
14),"equirectangular"==e.nodeValue&&(this.s.format=1);if(e=g.getAttributeNode("flipy"))this.s.ej=Number(e.nodeValue);if(e=g.getAttributeNode("startonload"))this.s.Pj=1==e.nodeValue;if(e=g.getAttributeNode("startmutedmobile"))this.s.om=1==e.nodeValue;if(e=g.getAttributeNode("bleed"))this.s.Qe=Number(e.nodeValue);if(e=g.getAttributeNode("endaction"))this.s.me=String(e.nodeValue);if(e=g.getAttributeNode("width"))this.s.width=Number(e.nodeValue);if(e=g.getAttributeNode("height"))this.s.height=Number(e.nodeValue);
for(q=g.firstChild;q;)"source"==q.nodeName&&(e=q.getAttributeNode("url"))&&this.s.src.push(e.nodeValue.toString()),q=q.nextSibling}if("level"==g.nodeName){f=new p.hk;e=g.getAttributeNode("width");f.width=1*(e?e.nodeValue:1);e=g.getAttributeNode("height");f.height=1*(e?e.nodeValue:f.width);if(e=g.getAttributeNode("preload"))f.cache=1==e.nodeValue;if(e=g.getAttributeNode("preview"))f.rf=1==e.nodeValue;f.L=Math.floor((f.width+this.h.G-1)/this.h.G);f.fa=Math.floor((f.height+this.h.G-1)/this.h.G);this.h.J.push(f)}g=
g.nextSibling}this.h.qj=this.h.J.length}this.df=!0;this.$g&&(this.Z=this.Sc=!1,this.lc||(this.M("dummy rendering"),this.lc=document.createElement("canvas"),this.lc.width=100,this.lc.height=100,this.lc.id="dummycanvas",this.D.appendChild(this.lc)),this.Rc());this.Z&&this.H&&(this.na.nl(this.Gf),this.na.ol());this.Sc&&(0<this.h.J.length?this.xk(!0):this.xk(!1),this.xg=0);var r=this;0<this.h.J.length&&this.h.Sl&&0==this.kb&&(b=new Image,f=new p.hk,f.rf=!0,f.cache=!0,f.L=f.fa=0,f.height=f.width=0,this.h.J.push(f),
b.crossOrigin=this.crossOrigin,b.onload=this.na.So(b),b.setAttribute("src",this.He(6,this.h.qj-1,0,0)));if(0<this.s.src.length&&this.Z)if(this.bh){this.s.b=document.createElement("video");this.s.b.crossOrigin=this.crossOrigin;this.s.b.setAttribute("style","display:none; max-width:none;");this.s.b.setAttribute("playsinline","playsinline");this.s.b.preload=!0;this.s.b.volume=this.V;this.T.appendChild(this.s.b);this.s.hd=!1;this.s.sm=!1;this.s.b.oncanplay=function(){if(!r.s.hd){r.s.Kh=!0;var a,b,c=[],
d=new p.xa,e=r.H,f=r.s.b.videoWidth/3;r.s.width=r.s.b.videoWidth;r.s.height=r.s.b.videoHeight;for(a=0;6>a;a++){var k=a%3*f+r.s.Qe;var g=k+f-2*r.s.Qe;var h=4;3>a&&(h+=f);var l=h+f-2*r.s.Qe;for(b=0;4>b;b++){d.x=-1;d.y=-1;d.z=1;for(var m=0;m<b;m++)d.cm();c.push((0<d.x?k:g)/(3*f),(0<d.y?l:h)/(2*f))}}e.bindBuffer(e.ARRAY_BUFFER,r.s.ri);e.bufferData(e.ARRAY_BUFFER,new Float32Array(c),e.STATIC_DRAW)}};"exit"==this.s.me?this.s.b.onended=function(){r.s.Kh=!1;r.s.hd=!1;r.T.removeChild(r.s.b);r.s.b=null;r.update()}:
"stop"==this.s.me?r.s.b.onended=function(){r.update()}:"{"==this.s.me.charAt(0)?this.s.b.onended=function(){r.ye(r.s.me,"$fwd")}:this.s.b.loop=!0;for(q=0;q<this.s.src.length;q++)f=document.createElement("source"),f.setAttribute("src",this.nc(this.s.src[q])),this.s.b.appendChild(f);f=this.Hb("_videopanorama");0<f.length?f[0].b=this.s.b:this.Wl("_videopanorama",this.s.b);if(this.s.Pj&&(f=this.s.b.play(),void 0!==f))f.then(function(){})["catch"](function(){c.s.om&&(c.s.b.muted=!0,c.s.b.play())})}else"{"==
this.s.me.charAt(0)&&r.ye(r.s.me,"$fwd");this.jk();this.B.rd||this.qm();this.update();this.Qd&&(this.Qd=!1,this.ja&&this.ja.ggViewerInit&&this.ja.ggViewerInit(),this.qb.enabled&&0==this.kb&&this.Z&&(this.Nc(9),this.pan.c=this.wc.pan,this.i.c=this.wc.i,this.f.c=this.wc.f,this.Ka=this.wc.Eb,this.w=this.hg(!1),this.pan.c=this.w.W[0].value,this.i.c=this.w.W[1].value,this.f.c=this.w.W[2].value,3==this.w.W[3].ub&&this.Nc(this.w.W[3].value),this.Lb=this.w.cb,this.B.Th=this.C.Ab,this.B.Ai=this.C.kd,this.B.Lh=
this.C.ue,this.l.active=!1,this.l.eg=!0));this.Rc()};b.prototype.yj=function(a,b){0<a.length&&(".xml"==a.substr(a.length-4)||".swf"==a.substr(a.length-4)||"{"==a.charAt(0)?this.ye(this.nc(a),b):window.open(this.nc(a),b))};b.prototype.Yp=function(){this.df=this.isLoaded=!1;this.checkLoaded=this.Sb=[];this.bi=0;this.ja&&this.ja.ggReLoaded&&this.ja.ggReLoaded();this.Y("beforechangenode",{})};b.prototype.ye=function(a,b){if(""!=a&&"{}"!=a){this.Yp();this.ba&&this.ba.hotspotProxyOut&&this.ba.hotspotProxyOut(this.ta.id,
this.ta.url);".swf"==a.substr(a.length-4)&&(a=a.substr(0,a.length-4)+".xml");var c="",d=null;"object"===typeof b&&null!==b?d=b:b&&(c=b.toString());c=c.replace("$cur",this.pan.c+"/"+this.i.c+"/"+this.f.c+"//"+this.sa());c=c.replace("$(cur)",this.pan.c+"/"+this.i.c+"/"+this.f.c+"//"+this.sa());c=c.replace("$fwd","N"+this.pe()+"/"+this.i.c+"/"+this.f.c+"//"+this.sa());c=c.replace("$(fwd)","N"+this.pe()+"/"+this.i.c+"/"+this.f.c+"//"+this.sa());c=c.replace("$bwd","S"+this.pe()+"/"+this.i.c+"/"+this.f.c+
"//"+this.sa());c=c.replace("$(bwd)","S"+this.pe()+"/"+this.i.c+"/"+this.f.c+"//"+this.sa());c=c.replace("$ap",String(this.pan.c));c=c.replace("$(ap)",String(this.pan.c));c=c.replace("$an",String(this.pe()));c=c.replace("$(an)",String(this.pe()));c=c.replace("$at",String(this.i.c));c=c.replace("$(at)",String(this.i.c));c=c.replace("$af",String(this.f.c));c=c.replace("$(af)",String(this.f.c));c=c.replace("$ar",String(this.sa()));c=c.replace("$(ar)",String(this.sa()));""!=c&&(b=c.split("/"),3<b.length&&
""!=b[3]&&(this.startNode=b[3]));d=null!==d?d:c;this.va();if("{"==a.charAt(0)){b=a.substr(1,a.length-2);if(this.Wa==b&&this.Vf)return;var e=this.Od[b];e&&(b=e);e=this.B;var f=this.H;if(this.Ie[b]){this.Vf=!0;if(this.B.enabled&&this.Z&&this.B.dc){e.be||e.rd||(e.Th=this.C.Ab,e.Ai=this.C.kd,e.Lh=this.C.ue,this.Og(!0),this.Pg(!0),this.Ng(!0));var h=void 0;"wipeleftright"==e.type?h=1:"wiperightleft"==e.type?h=2:"wipetopbottom"==e.type?h=3:"wipebottomtop"==e.type?h=4:"wiperandom"==e.type&&(h=Math.ceil(4*
Math.random()));e.Vi=h;f.bindFramebuffer(f.FRAMEBUFFER,e.dc);f.viewport(0,0,e.dc.width,e.dc.height);f.clear(f.COLOR_BUFFER_BIT|f.DEPTH_BUFFER_BIT);e.Wg=!0;this.Zg();e.Wg=!1;f.bindFramebuffer(f.FRAMEBUFFER,null);f.viewport(0,0,this.rb.width,this.rb.height);f=new Date;this.ta!=this.eb?(e.jh=this.ta.Mb/this.o.width,e.kh=1-this.ta.vb/this.o.height):(e.jh=.5,e.kh=.5);1!=e.Oa&&2!=e.Oa?(e.Qj=f.getTime(),e.be=!0):(e.Om=f.getTime(),e.rd=!0,e.yb=Math.sin(this.Ib()/2*Math.PI/180)/Math.sin(e.Of/2*Math.PI/180),
e.yb=Math.max(e.yb,1),e.Nm=1/e.de*e.yb*.3)}this.Bj(this.Ie[b],d);this.Li(a);e.enabled&&this.Z&&0!=e.ec&&(e.Fe=this.kg(),e.Ge=this.Ch(),e.Hd=this.Ib(),e.qd=this.sa(),1==e.ec||3==e.ec?this.li(e.Of):2==e.ec?this.li(this.Ib()+e.Of):4==e.ec&&(this.Nc(this.wc.Eb),this.Af(this.wc.pan),this.Bf(this.wc.i),this.li(this.wc.f)),e.Pf||1==e.Oa||2==e.Oa||(4==e.ec?(this.w=this.hg(!0,e.Fe,e.Ge,e.Hd),this.Lb=this.w.cb,this.l.active=!0,this.qb.nd=!0):this.moveTo(e.Fe,e.Ge,e.Hd,e.de,0,e.qd)));this.ia&&this.ia.openNext(a,
c);this.B.rd||this.B.be||(this.ha&&this.Ri(),this.Vf=!1)}else{this.Lc("invalid node id: "+b);return}}else this.Ul(a,null,d);this.Y("changenode",{});this.update(5)}};b.prototype.co=function(){return this.df?this.gf?this.Ma.slice(0):[""]:[]};b.prototype.bg=function(a){var b;var d={title:"",description:"",author:"",datetime:"",copyright:"",source:"",information:"",comment:"",latitude:0,longitude:0,customnodeid:"",tags:[]};if(a&&((b=a.getAttributeNode("title"))&&(d.title=b.nodeValue.toString()),(b=a.getAttributeNode("description"))&&
(d.description=b.nodeValue.toString()),(b=a.getAttributeNode("author"))&&(d.author=b.nodeValue.toString()),(b=a.getAttributeNode("datetime"))&&(d.datetime=b.nodeValue.toString()),(b=a.getAttributeNode("copyright"))&&(d.copyright=b.nodeValue.toString()),(b=a.getAttributeNode("source"))&&(d.source=b.nodeValue.toString()),(b=a.getAttributeNode("info"))&&(d.information=b.nodeValue.toString()),(b=a.getAttributeNode("comment"))&&(d.comment=b.nodeValue.toString()),(b=a.getAttributeNode("latitude"))&&(d.latitude=
Number(b.nodeValue)),(b=a.getAttributeNode("longitude"))&&(d.longitude=Number(b.nodeValue)),(b=a.getAttributeNode("customnodeid"))&&(d.customnodeid=b.nodeValue.toString()),b=a.getAttributeNode("tags"))){a=b.nodeValue.toString().split("|");for(b=0;b<a.length;b++)""==a[b]&&(a.splice(b,1),b--);d.tags=a}return d};b.prototype.Fk=function(a){for(var b={},d=a.firstChild;d;){if("mapcoords"==d.nodeName){var f={x:0,y:0};a=d.getAttributeNode("x");f.x=Number(a.nodeValue);a=d.getAttributeNode("y");f.y=Number(a.nodeValue);
a=d.getAttributeNode("mapid");b[a.nodeValue.toString()]=f}d=d.nextSibling}return b};b.prototype.Gk=function(a){for(var b={},d=a.firstChild;d;){if("mapcoords"==d.nodeName){var f={x:0,y:0};a=d.getAttributeNode("x_floorplan_percent");f.x=Number(a.nodeValue);a=d.getAttributeNode("y_floorplan_percent");f.y=Number(a.nodeValue);a=d.getAttributeNode("mapid");b[a.nodeValue.toString()]=f}d=d.nextSibling}return b};b.prototype.gj=function(a){return a?this.fh[a]?this.fh[a]:this.bg():this.Lf};b.prototype.eo=function(a){a=
this.gj(a);var b=[];""!=a.latitude&&0!=a.latitude&&0!=a.longitude&&(b.push(a.latitude),b.push(a.longitude));return b};b.prototype.io=function(a){return this.gj(a).title};b.prototype.af=function(a,b){var c;for(c=0;c<this.w.W.length;c++)if(this.w.W[c].time==a&&this.w.W[c].ub==b)return this.w.W[c];return!1};b.prototype.bo=function(a){var b,d=1E5,f=a,e=!1;for(b=0;b<this.w.W.length;b++)this.w.W[b].ub==a.ub&&this.w.W[b].time>a.time&&this.w.W[b].time<d&&(f=this.w.W[b],d=f.time,e=!0);return e?f:!1};b.prototype.Jn=
function(a){for(var b=[],d=0;d<this.w.W.length;d++)if(this.w.W[d].time<=a&&4==this.w.W[d].ub){for(var f=!1,e=0;e<b.length;e++)if(b[e].mh==this.w.W[d].mh){b[e].time<this.w.W[d].time?b.splice(e,1):f=!0;break}f||b.push(this.w.W[d])}return b};b.prototype.hg=function(a,b,d,f){for(var c=0;c<this.za.length;c++)if(this.za[c].cb&&0==this.za[c].cb.indexOf("__FlyIn"))return this.za[c];c=new p.dk;c.cb="__FlyIn";c.Ef=this.pan.c;c.Sg=this.i.c;c.Gd=this.f.c;c.Rg=this.Ka;c.qd=this.ai;a?(c.Xe=!1,c.ke=!1,c.speed=this.B.de,
c.Qc=b,c.$d=d,c.Ff=f):(c.Xe=!0,c.ke=!0,c.speed=this.qb.speed,c.Qc=this.pan.Qa,c.$d=this.i.Qa,c.Ff=this.f.Qa);return this.Qk(c)};b.prototype.Qk=function(a){var b=new p.ek;b.cb=a.cb;b.Oe="";b.W=[];for(var d=a.Rg!=a.qd&&-1!=a.qd;-180>a.Qc;)a.Qc=a.Qc+360;for(;180<a.Qc;)a.Qc=a.Qc-360;var f=a.Qc-a.Ef;if(360==this.pan.max-this.pan.min){for(;-180>f;)f+=360;for(;180<f;)f-=360}var e=a.$d-a.Sg,h=a.Ff-a.Gd,t=Math.round(Math.sqrt(f*f+e*e+h*h)/a.speed*.33);d&&(t=Math.max(10,t));b.length=t;if(a.le){var g=Math.ceil(.7*
t);g=Math.min(15,g);g=Math.max(5,g);b.length=t+g;var q=.33*g}var n=a.Ff,y=t,r=0,v=t-1;if(d){var u=a.Gd,x=void 0;4==a.qd?x=120:x=this.ig(a.qd);var A=a.Ff;h=A-a.Gd;var w=new p.vc(0,a.Gd),B=new p.vc(t,A),z=new p.vc,C=new p.vc;C.Za(t/3,a.Gd+h/3);z.Za(2*t/3,A-h/3);if(u>x)for(;r<=t&&u>x;)u=new p.vc,u.Hi(w,B,C,z,r),u=u.y,r++;else r=1;r>=.8*t&&(y=r=Math.round(.8*t));0==r&&(r=1);x=void 0;4==a.Rg?x=120:x=this.ig(a.Rg);u=a.Ff;if(u>x)for(;v>r&&u>x;)u=new p.vc,u.Hi(w,B,C,z,v),u=u.y,v--}w=new p.Ec;w.time=0;w.ub=
0;w.value=a.Ef;w.type=1;w.he=t/3;w.ie=a.Xe?a.Ef:a.Ef+f/3;b.W.push(w);w=new p.Ec;w.time=0;w.ub=1;w.value=a.Sg;w.type=1;w.he=t/3;w.ie=a.Xe?a.Sg:a.Sg+e/3;b.W.push(w);w=new p.Ec;w.time=0;w.ub=2;w.value=a.Gd;w.type=1;w.he=t/3;w.ie=a.Xe?a.Gd:a.Gd+h/3;b.W.push(w);w=new p.Ec;w.time=0;w.ub=3;w.value=a.Rg;w.type=0;w.xb=0;b.W.push(w);d&&(w=new p.Ec,w.time=r,w.ub=3,w.value=a.qd,w.type=0,w.xb=v-r,b.W.push(w));w=new p.Ec;w.time=t;w.ub=0;w.value=a.Ef+f;w.type=1;w.ge=2*t/3;a.ke&&!a.le?w.Vc=w.value:w.Vc=w.value-f/
3;a.le&&(w.he=t+q,w.ie=w.value+q/t*f);b.W.push(w);w=new p.Ec;w.time=t;w.ub=1;w.value=a.$d;w.type=1;w.ge=2*t/3;a.ke&&!a.le?w.Vc=a.$d:w.Vc=a.$d-e/3;a.le&&(w.he=t+q,w.ie=w.value+q/t*e);b.W.push(w);w=new p.Ec;w.time=y;w.ub=2;w.value=n;w.type=1;w.ge=2*y/3;a.ke?w.Vc=n:w.Vc=n-h/3;b.W.push(w);a.le&&(w=new p.Ec,w.time=t+g,w.ub=0,w.value=a.Qc,w.type=1,w.ge=t+g-q,w.Vc=a.Qc,b.W.push(w),w=new p.Ec,w.time=t+g,w.ub=1,w.value=a.$d,w.type=1,w.ge=t+g-q,w.Vc=a.$d,b.W.push(w));this.za.push(b);return b};b.prototype.wq=
function(){this.s.b&&this.s.b.play()};b.prototype.xq=function(){this.s.b&&(this.s.b.pause(),this.s.b.currentTime=0)};b.prototype.vq=function(){this.s.b&&this.s.b.pause()};b.prototype.Np=function(a){this.s.b&&(0>a&&(a=0),a>this.s.b.duration&&(a=this.s.b.duration-.1),this.s.b.currentTime=a,this.update())};b.prototype.vo=function(){return this.s.b?this.s.b.currentTime:0};b.prototype.uo=function(){if(this.s.b)return this.s.b};b.prototype.Mp=function(a){if(this.s.b){var b=!this.s.b.paused&&!this.s.b.ended,
d=this.s.b.currentTime;this.s.b.pause();isNaN(parseInt(a,10))?this.s.b.src=String(a):this.s.b.src=this.s.src[parseInt(a,10)];b&&(this.s.b.onloadedmetadata=function(){this.currentTime=d;this.play();this.onloadedmetadata=null});this.s.b.currentTime=d}};b.prototype.Cn=function(){this.Ll=!0};return b}();p.a=d})(ggP2VR||(ggP2VR={}));window.ggHasHtml5Css3D=U;window.ggHasWebGL=V;window.pano2vrPlayer=ggP2VR.a;ggP2VR.a.prototype.getVersion=ggP2VR.a.prototype.el;ggP2VR.a.prototype.readConfigString=ggP2VR.a.prototype.Cj;
ggP2VR.a.prototype.readConfigUrl=ggP2VR.a.prototype.Ul;ggP2VR.a.prototype.readConfigUrlAsync=ggP2VR.a.prototype.lp;ggP2VR.a.prototype.readConfigXml=ggP2VR.a.prototype.Vl;ggP2VR.a.prototype.openUrl=ggP2VR.a.prototype.yj;ggP2VR.a.prototype.openNext=ggP2VR.a.prototype.ye;ggP2VR.a.prototype.setMargins=ggP2VR.a.prototype.yp;ggP2VR.a.prototype.addListener=ggP2VR.a.prototype.addListener;ggP2VR.a.prototype.on=ggP2VR.a.prototype.addListener;ggP2VR.a.prototype.removeEventListener=ggP2VR.a.prototype.removeEventListener;
ggP2VR.a.prototype.off=ggP2VR.a.prototype.removeEventListener;ggP2VR.a.prototype.detectBrowser=ggP2VR.a.prototype.Dk;ggP2VR.a.prototype.initWebGL=ggP2VR.a.prototype.Ic;ggP2VR.a.prototype.getPercentLoaded=ggP2VR.a.prototype.lo;ggP2VR.a.prototype.setBasePath=ggP2VR.a.prototype.sp;ggP2VR.a.prototype.getBasePath=ggP2VR.a.prototype.Kn;ggP2VR.a.prototype.setViewerSize=ggP2VR.a.prototype.jm;ggP2VR.a.prototype.getViewerSize=ggP2VR.a.prototype.yo;ggP2VR.a.prototype.setSkinObject=ggP2VR.a.prototype.Ip;
ggP2VR.a.prototype.changeViewMode=ggP2VR.a.prototype.qn;ggP2VR.a.prototype.getViewMode=ggP2VR.a.prototype.wo;ggP2VR.a.prototype.changePolygonMode=ggP2VR.a.prototype.uk;ggP2VR.a.prototype.setPolygonMode=ggP2VR.a.prototype.uk;ggP2VR.a.prototype.getPolygonMode=ggP2VR.a.prototype.no;ggP2VR.a.prototype.showOnePolyHotspot=ggP2VR.a.prototype.km;ggP2VR.a.prototype.hideOnePolyHotspot=ggP2VR.a.prototype.jl;ggP2VR.a.prototype.changePolyHotspotColor=ggP2VR.a.prototype.nn;
ggP2VR.a.prototype.toggleOnePolyHotspot=ggP2VR.a.prototype.fq;ggP2VR.a.prototype.changeViewState=ggP2VR.a.prototype.rn;ggP2VR.a.prototype.getViewState=ggP2VR.a.prototype.xo;ggP2VR.a.prototype.setRenderFlags=ggP2VR.a.prototype.Bp;ggP2VR.a.prototype.getRenderFlags=ggP2VR.a.prototype.qo;ggP2VR.a.prototype.setMaxTileCount=ggP2VR.a.prototype.hm;ggP2VR.a.prototype.getVFov=ggP2VR.a.prototype.Ib;ggP2VR.a.prototype.setVFov=ggP2VR.a.prototype.li;ggP2VR.a.prototype.getHFov=ggP2VR.a.prototype.On;
ggP2VR.a.prototype.updatePanorama=ggP2VR.a.prototype.Zg;ggP2VR.a.prototype.isTouching=ggP2VR.a.prototype.wl;ggP2VR.a.prototype.getIsMobile=ggP2VR.a.prototype.Tn;ggP2VR.a.prototype.setIsMobile=ggP2VR.a.prototype.wp;ggP2VR.a.prototype.getIsTour=ggP2VR.a.prototype.Un;ggP2VR.a.prototype.getIsAutorotating=ggP2VR.a.prototype.Qn;ggP2VR.a.prototype.getIsLoading=ggP2VR.a.prototype.Sn;ggP2VR.a.prototype.getIsLoaded=ggP2VR.a.prototype.Bh;ggP2VR.a.prototype.getIsTileLoading=ggP2VR.a.prototype.Vk;
ggP2VR.a.prototype.getLastActivity=ggP2VR.a.prototype.Vn;ggP2VR.a.prototype.getPan=ggP2VR.a.prototype.kg;ggP2VR.a.prototype.getPanNorth=ggP2VR.a.prototype.pe;ggP2VR.a.prototype.getPanDest=ggP2VR.a.prototype.ko;ggP2VR.a.prototype.getPanN=ggP2VR.a.prototype.Yk;ggP2VR.a.prototype.setPan=ggP2VR.a.prototype.Af;ggP2VR.a.prototype.setPanNorth=ggP2VR.a.prototype.Jj;ggP2VR.a.prototype.changePan=ggP2VR.a.prototype.tk;ggP2VR.a.prototype.changePanLog=ggP2VR.a.prototype.mn;ggP2VR.a.prototype.getTilt=ggP2VR.a.prototype.Ch;
ggP2VR.a.prototype.getTiltDest=ggP2VR.a.prototype.ro;ggP2VR.a.prototype.setTilt=ggP2VR.a.prototype.Bf;ggP2VR.a.prototype.changeTilt=ggP2VR.a.prototype.vk;ggP2VR.a.prototype.changeTiltLog=ggP2VR.a.prototype.pn;ggP2VR.a.prototype.getFov=ggP2VR.a.prototype.fj;ggP2VR.a.prototype.getFovDest=ggP2VR.a.prototype.Nn;ggP2VR.a.prototype.setFov=ggP2VR.a.prototype.yf;ggP2VR.a.prototype.changeFov=ggP2VR.a.prototype.sk;ggP2VR.a.prototype.changeFovLog=ggP2VR.a.prototype.Mi;ggP2VR.a.prototype.getRoll=ggP2VR.a.prototype.al;
ggP2VR.a.prototype.setRoll=ggP2VR.a.prototype.Kj;ggP2VR.a.prototype.setPanTilt=ggP2VR.a.prototype.Ap;ggP2VR.a.prototype.setPanTiltFov=ggP2VR.a.prototype.ji;ggP2VR.a.prototype.setDefaultView=ggP2VR.a.prototype.up;ggP2VR.a.prototype.setLocked=ggP2VR.a.prototype.xp;ggP2VR.a.prototype.setLockedMouse=ggP2VR.a.prototype.Og;ggP2VR.a.prototype.setLockedKeyboard=ggP2VR.a.prototype.Ng;ggP2VR.a.prototype.getLockedKeyboard=ggP2VR.a.prototype.Xn;ggP2VR.a.prototype.setLockedWheel=ggP2VR.a.prototype.Pg;
ggP2VR.a.prototype.moveTo=ggP2VR.a.prototype.moveTo;ggP2VR.a.prototype.moveToEx=ggP2VR.a.prototype.Vh;ggP2VR.a.prototype.moveToDefaultView=ggP2VR.a.prototype.Oo;ggP2VR.a.prototype.moveToDefaultViewEx=ggP2VR.a.prototype.Po;ggP2VR.a.prototype.addHotspotElements=ggP2VR.a.prototype.jk;ggP2VR.a.prototype.playSound=ggP2VR.a.prototype.Ae;ggP2VR.a.prototype.playPauseSound=ggP2VR.a.prototype.Rl;ggP2VR.a.prototype.playStopSound=ggP2VR.a.prototype.jp;ggP2VR.a.prototype.pauseSound=ggP2VR.a.prototype.zj;
ggP2VR.a.prototype.activateSound=ggP2VR.a.prototype.$m;ggP2VR.a.prototype.soundGetTime=ggP2VR.a.prototype.Sp;ggP2VR.a.prototype.soundSetTime=ggP2VR.a.prototype.Tp;ggP2VR.a.prototype.setMediaVisibility=ggP2VR.a.prototype.zp;ggP2VR.a.prototype.isPlaying=ggP2VR.a.prototype.Xb;ggP2VR.a.prototype.stopSound=ggP2VR.a.prototype.pi;ggP2VR.a.prototype.setVolume=ggP2VR.a.prototype.Op;ggP2VR.a.prototype.changeVolume=ggP2VR.a.prototype.sn;ggP2VR.a.prototype.removeHotspots=ggP2VR.a.prototype.$l;
ggP2VR.a.prototype.getHotspotsVisible=ggP2VR.a.prototype.Uk;ggP2VR.a.prototype.getCurrentPerspective=ggP2VR.a.prototype.dd;ggP2VR.a.prototype.addHotspot=ggP2VR.a.prototype.an;ggP2VR.a.prototype.updateHotspot=ggP2VR.a.prototype.nq;ggP2VR.a.prototype.removeHotspot=ggP2VR.a.prototype.mp;ggP2VR.a.prototype.setActiveHotspot=ggP2VR.a.prototype.xf;ggP2VR.a.prototype.getPointHotspotIds=ggP2VR.a.prototype.mo;ggP2VR.a.prototype.getHotspot=ggP2VR.a.prototype.Pn;ggP2VR.a.prototype.setFullscreen=ggP2VR.a.prototype.zf;
ggP2VR.a.prototype.toggleFullscreen=ggP2VR.a.prototype.ui;ggP2VR.a.prototype.enterFullscreen=ggP2VR.a.prototype.Dn;ggP2VR.a.prototype.exitFullscreen=ggP2VR.a.prototype.exitFullscreen;ggP2VR.a.prototype.getIsFullscreen=ggP2VR.a.prototype.Rn;ggP2VR.a.prototype.startAutorotate=ggP2VR.a.prototype.Xp;ggP2VR.a.prototype.stopAutorotate=ggP2VR.a.prototype.$p;ggP2VR.a.prototype.toggleAutorotate=ggP2VR.a.prototype.eq;ggP2VR.a.prototype.startAnimation=ggP2VR.a.prototype.Wp;ggP2VR.a.prototype.createLayers=ggP2VR.a.prototype.yk;
ggP2VR.a.prototype.removePanorama=ggP2VR.a.prototype.fi;ggP2VR.a.prototype.getScreenResolution=ggP2VR.a.prototype.bl;ggP2VR.a.prototype.getMaxScreenResolution=ggP2VR.a.prototype.Wk;ggP2VR.a.prototype.getNodeIds=ggP2VR.a.prototype.co;ggP2VR.a.prototype.getNodeUserdata=ggP2VR.a.prototype.gj;ggP2VR.a.prototype.getNodeLatLng=ggP2VR.a.prototype.eo;ggP2VR.a.prototype.getNodeTitle=ggP2VR.a.prototype.io;ggP2VR.a.prototype.getCurrentNode=ggP2VR.a.prototype.Tk;ggP2VR.a.prototype.getNextNode=ggP2VR.a.prototype.Xk;
ggP2VR.a.prototype.getPrevNode=ggP2VR.a.prototype.po;ggP2VR.a.prototype.getLastVisitedNode=ggP2VR.a.prototype.Wn;ggP2VR.a.prototype.getCurrentPointHotspots=ggP2VR.a.prototype.Mn;ggP2VR.a.prototype.getPositionAngles=ggP2VR.a.prototype.oo;ggP2VR.a.prototype.getPositionRawAngles=ggP2VR.a.prototype.Zk;ggP2VR.a.prototype.nodeVisited=ggP2VR.a.prototype.Uo;ggP2VR.a.prototype.setElementIdPrefix=ggP2VR.a.prototype.vp;ggP2VR.a.prototype.videoPanoPlay=ggP2VR.a.prototype.wq;ggP2VR.a.prototype.videoPanoStop=ggP2VR.a.prototype.xq;
ggP2VR.a.prototype.videoPanoPause=ggP2VR.a.prototype.vq;ggP2VR.a.prototype.getVideoPanoTime=ggP2VR.a.prototype.vo;ggP2VR.a.prototype.setVideoPanoTime=ggP2VR.a.prototype.Np;ggP2VR.a.prototype.getVideoPanoObject=ggP2VR.a.prototype.uo;ggP2VR.a.prototype.setVideoPanoSource=ggP2VR.a.prototype.Mp;ggP2VR.a.prototype.getMediaObject=ggP2VR.a.prototype.ao;ggP2VR.a.prototype.registerVideoElement=ggP2VR.a.prototype.Wl;ggP2VR.a.prototype.disableSoundLoading=ggP2VR.a.prototype.Cn;
ggP2VR.a.prototype.setCrossOrigin=ggP2VR.a.prototype.tp;ggP2VR.a.prototype.setProjection=ggP2VR.a.prototype.Nc;ggP2VR.a.prototype.getProjection=ggP2VR.a.prototype.sa;ggP2VR.a.prototype.changeProjection=ggP2VR.a.prototype.Ni;ggP2VR.a.prototype.changeProjectionEx=ggP2VR.a.prototype.Ni;ggP2VR.a.prototype.changeLensflares=ggP2VR.a.prototype.ln;ggP2VR.a.prototype.setTransition=ggP2VR.a.prototype.Kp;ggP2VR.a.prototype.getMapType=ggP2VR.a.prototype.$n;ggP2VR.a.prototype.getMapDetails=ggP2VR.a.prototype.Zn;
ggP2VR.a.prototype.getNodeMapCoords=ggP2VR.a.prototype.fo;ggP2VR.a.prototype.getNodeMapCoordsInPercent=ggP2VR.a.prototype.ho;ggP2VR.a.prototype.getMapContainingNode=ggP2VR.a.prototype.Yn;ggP2VR.a.prototype.addVariable=ggP2VR.a.prototype.bn;ggP2VR.a.prototype.setVariableOptions=ggP2VR.a.prototype.im;ggP2VR.a.prototype.setVariableValue=ggP2VR.a.prototype.Zd;ggP2VR.a.prototype.getVariableValue=ggP2VR.a.prototype.to;ggP2VR.a.prototype.setUseGyro=ggP2VR.a.prototype.Lp;ggP2VR.a.prototype.getUseGyro=ggP2VR.a.prototype.so;
ggP2VR.a.prototype.getOS=ggP2VR.a.prototype.jo;ggP2VR.a.prototype.getBrowser=ggP2VR.a.prototype.Ln;ggP2VR.a.prototype.triggerEvent=ggP2VR.a.prototype.Y;ggP2VR.a.prototype.requestRedraw=ggP2VR.a.prototype.op;ggP2VR.a.prototype.getWebGlContext=ggP2VR.a.prototype.zo;
