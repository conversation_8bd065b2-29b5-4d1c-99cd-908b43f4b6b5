/**
 * 全景图热点编辑系统 - 主要JavaScript文件
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */

layui.use(['layer', 'form', 'table', 'upload', 'element'], function() {
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var upload = layui.upload;
    var element = layui.element;
    var $ = layui.$;

    // 全局变量
    var currentTaskId = null;
    var hotspotTable = null;
    var deviceList = [];

    // 初始化
    $(document).ready(function() {
        console.log('页面加载完成，开始初始化...');
        initPage();
        bindEvents();
        loadTaskList();
    });

    /**
     * 初始化页面
     */
    function initPage() {
        // 初始化热点表格
        initHotspotTable();

        // 初始化文件上传
        initFileUpload();

        // 初始化拖拽功能
        initResizeHandle();
    }

    /**
     * 绑定事件
     */
    function bindEvents() {
        console.log('开始绑定事件...');

        // 检查按钮是否存在
        console.log('创建任务按钮:', $('#createTaskBtn').length);

        // 创建任务按钮
        $('#createTaskBtn').on('click', function() {
            console.log('创建任务按钮被点击');
            showCreateTaskDialog();
        });

        // 导出按钮
        $('#exportBtn').on('click', function() {
            if (!currentTaskId) {
                layer.msg('请先选择任务', {icon: 2});
                return;
            }
            exportPanorama();
        });

        // 刷新表格按钮
        $('#refreshTableBtn').on('click', function() {
            if (currentTaskId) {
                hotspotTable.reload();
            }
        });

        // 刷新预览按钮
        $('#refreshPreviewBtn').on('click', function() {
            if (currentTaskId) {
                loadPreview();
            }
        });

        // 任务选择监听
        form.on('select(taskSelect)', function(data) {
            var taskId = data.value;
            console.log('任务选择改变:', taskId);

            if (taskId) {
                // 如果当前已有任务，提示用户确认切换
                if (currentTaskId && currentTaskId != taskId) {
                    layer.confirm('切换任务将清空当前工作状态，请确保已保存所有修改。是否继续切换？', {
                        icon: 3,
                        title: '确认切换任务',
                        btn: ['确认切换', '取消']
                    }, function(index) {
                        // 用户确认切换
                        layer.close(index);
                        selectTask(taskId);
                    }, function(index) {
                        // 用户取消切换，恢复原选择
                        layer.close(index);
                        $('#taskSelect').val(currentTaskId);
                        form.render('select');
                    });
                } else {
                    // 首次选择任务，直接切换
                    selectTask(taskId);
                }
            } else {
                // 选择空值，清空任务信息
                if (currentTaskId) {
                    layer.confirm('确定要取消选择当前任务吗？这将清空所有工作状态。', {
                        icon: 3,
                        title: '确认取消选择',
                        btn: ['确认', '取消']
                    }, function(index) {
                        layer.close(index);
                        clearTaskInfo();
                    }, function(index) {
                        layer.close(index);
                        $('#taskSelect').val(currentTaskId);
                        form.render('select');
                    });
                } else {
                    clearTaskInfo();
                }
            }
        });

        // 创建任务表单提交 - 注意这里不需要lay-filter，因为我们手动触发
        // form.on('submit(createTask)', function(data) {
        //     createTask(data.field);
        //     return false;
        // });

        // 编辑热点表单提交 - 注意这里不需要lay-filter，因为我们手动触发
        // form.on('submit(editHotspot)', function(data) {
        //     updateHotspot(data.field);
        //     return false;
        // });
    }

    /**
     * 加载任务列表
     */
    function loadTaskList(callback) {
        console.log('开始加载任务列表...');
        $.get('/panorama/task/list', function(res) {
            console.log('任务列表响应:', res);
            if (res.success) {
                var options = '<option value="">请选择任务</option>';
                $.each(res.data, function(index, task) {
                    var statusText = getStatusText(task.STATUS);
                    options += '<option value="' + task.TASK_ID + '">' +
                              task.TASK_NAME + ' (' + statusText + ')</option>';
                });
                console.log('生成的选项:', options);
                $('#taskSelect').html(options);
                form.render('select');

                // 如果有回调函数，执行它
                if (typeof callback === 'function') {
                    callback();
                }
            } else {
                console.error('加载任务列表失败:', res.msg);
                layer.msg('加载任务列表失败: ' + res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            console.error('网络请求失败:', xhr, status, error);
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 获取状态文本
     */
    function getStatusText(status) {
        switch(status) {
            case 0: return '创建中';
            case 1: return '已完成';
            case 2: return '已导出';
            default: return '未知';
        }
    }

    /**
     * 格式化时间戳
     */
    function formatTimestamp(timestamp) {
        if (!timestamp) return '-';

        // 如果是13位时间戳，直接使用；如果是10位，需要乘以1000
        var time = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
        var date = new Date(time);

        if (isNaN(date.getTime())) return '-';

        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var hours = String(date.getHours()).padStart(2, '0');
        var minutes = String(date.getMinutes()).padStart(2, '0');
        var seconds = String(date.getSeconds()).padStart(2, '0');

        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    }

    /**
     * 选择任务
     */
    function selectTask(taskId) {
        currentTaskId = taskId;
        
        // 加载任务详情
        $.get('/panorama/task/' + taskId, function(res) {
            if (res.success) {
                updateTaskInfo(res.data);
                enableUploadButtons();
                loadDeviceList();
                loadHotspotData();
                loadPreview();
            } else {
                layer.msg('加载任务详情失败: ' + res.msg, {icon: 2});
            }
        });
    }

    /**
     * 更新任务信息显示
     */
    function updateTaskInfo(taskData) {
        $('#taskName').text(taskData.TASK_NAME || '-');

        // 合并型号信息显示
        var modelInfo = '';
        if (taskData.MODEL_ID && taskData.MODEL_NAME) {
            modelInfo = taskData.MODEL_ID + ' - ' + taskData.MODEL_NAME;
        } else if (taskData.MODEL_ID) {
            modelInfo = taskData.MODEL_ID;
        } else if (taskData.MODEL_NAME) {
            modelInfo = taskData.MODEL_NAME;
        } else {
            modelInfo = '-';
        }
        $('#modelInfo').text(modelInfo);

        $('#taskDescription').text(taskData.DESCRIPTION || '-');
        $('#createTime').text(formatTimestamp(taskData.CREATE_TIME));
        $('#taskStatus').text(getStatusText(taskData.STATUS));

        // 更新上传状态
        $('#zipStatus').text(taskData.ZIP_FILE_PATH ? '已上传' : '未上传');
        $('#excelStatus').text('未知'); // 需要额外查询

        // 启用导出按钮
        if (taskData.STATUS >= 1) {
            $('#exportBtn').prop('disabled', false);
        }
    }

    /**
     * 清空任务信息
     */
    function clearTaskInfo() {
        currentTaskId = null;
        $('#taskName').text('未选择任务');
        $('#modelInfo').text('-');
        $('#taskDescription').text('-');
        $('#createTime').text('-');
        $('#taskStatus').text('-');
        $('#zipStatus').text('未上传');
        $('#excelStatus').text('未上传');

        disableUploadButtons();
        $('#exportBtn').prop('disabled', true);

        // 清空表格
        if (hotspotTable) {
            hotspotTable.reload({
                data: []
            });
        }

        // 清空预览
        hidePreview();
    }

    /**
     * 启用上传按钮和查看按钮
     */
    function enableUploadButtons() {
        // {{CHENGQI:
        // Action: Modified
        // Timestamp: 2025-01-27 16:18:00 CST
        // Reason: 修改按钮启用逻辑，包含新的查看设备按钮
        // Principle_Applied: 一致性 - 统一按钮状态管理
        // Optimization: 同时更新设备状态显示
        // }}
        $('#uploadZipBtn').prop('disabled', false);
        $('#viewDeviceBtn').prop('disabled', false);

        // 更新设备状态显示
        if (typeof updateDeviceStatus === 'function') {
            updateDeviceStatus();
        }
    }

    /**
     * 禁用上传按钮和查看按钮
     */
    function disableUploadButtons() {
        $('#uploadZipBtn').prop('disabled', true);
        $('#viewDeviceBtn').prop('disabled', true);

        // 重置设备状态显示
        $('#deviceStatus').text('-');
    }

    /**
     * 初始化热点表格
     */
    function initHotspotTable() {
        hotspotTable = table.render({
            elem: '#hotspotTable',
            url: '/panorama/hotspot/list',
            where: {
                taskId: 0 // 初始为0，不加载数据
            },
            page: true,
            limit: 25, // 增加每页显示行数以显示更多内容
            limits: [25, 50, 100],
            height: 'full-45', // 自适应高度，减去头部高度
            cols: [[
                // {{CHENGQI:
                // Action: Modified
                // Timestamp: 2025-01-27 15:40:00 CST
                // Reason: 进一步优化列宽和显示，改ID为序号，缩小各列宽度
                // Principle_Applied: 用户体验优化 - 最大化内容显示空间，紧凑化设计
                // Optimization: 序号列、状态列、操作列宽度最小化，为内容列留出更多空间
                // }}
                {type: 'numbers', title: '序号', width: 60}, // 改为序号列，缩小宽度
                {field: 'EDITED_TITLE', title: '编辑标题', width: 200, edit: 'text'},
                {field: 'EDITED_DESCRIPTION', title: '编辑描述', width: 280, edit: 'text'},
                {field: 'IS_EDITED', title: '状态', width: 70, templet: '#editStatusTpl'}, // 缩小状态列宽度
                {title: '操作', width: 100, toolbar: '#hotspotTableBar', fixed: 'right'} // 缩小操作列宽度
            ]],
            done: function() {
                // 表格渲染完成回调
            }
        });

        // 监听表格工具条
        table.on('tool(hotspotTable)', function(obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                editHotspot(data);
            } else if (obj.event === 'locate') {
                locateHotspot(data.HOTSPOT_ID);
            }
        });

        // 监听单元格编辑
        table.on('edit(hotspotTable)', function(obj) {
            var value = obj.value;
            var data = obj.data;
            var field = obj.field;

            // 更新热点信息
            var updateData = {
                hotspotId: data.HOTSPOT_ID
            };
            // 映射字段名
            if (field === 'EDITED_TITLE') {
                updateData.editedTitle = value;
            } else if (field === 'EDITED_DESCRIPTION') {
                updateData.editedDescription = value;
            } else {
                updateData[field.toLowerCase()] = value;
            }

            $.post('/panorama/hotspot/update', updateData, function(res) {
                if (res.success) {
                    layer.msg('更新成功', {icon: 1});
                    hotspotTable.reload();
                } else {
                    layer.msg('更新失败: ' + res.msg, {icon: 2});
                }
            }).fail(function() {
                layer.msg('网络请求失败', {icon: 2});
            });
        });
    }

    /**
     * 加载热点数据
     */
    function loadHotspotData() {
        if (!currentTaskId) return;
        
        hotspotTable.reload({
            where: {
                taskId: currentTaskId
            }
        });
    }

    /**
     * 初始化拖拽调整功能
     */
    function initResizeHandle() {
        var resizeHandle = document.getElementById('resizeHandle');
        var leftPanel = document.getElementById('leftPanel');
        var rightPanel = document.getElementById('rightPanel');
        var container = document.querySelector('.container-content');
        var iframe = document.getElementById('panoramaFrame');

        var isResizing = false;
        var startX = 0;
        var startLeftWidth = 0;

        resizeHandle.addEventListener('mousedown', function(e) {
            isResizing = true;
            startX = e.clientX;
            startLeftWidth = leftPanel.offsetWidth;

            // 添加全局样式，防止选择文本
            document.body.style.userSelect = 'none';
            document.body.style.cursor = 'col-resize';

            // 禁用iframe的鼠标事件，防止干扰拖拽
            if (iframe) {
                iframe.style.pointerEvents = 'none';
            }

            // 添加遮罩层防止iframe捕获事件
            createDragOverlay();

            e.preventDefault();
            e.stopPropagation();
        });

        document.addEventListener('mousemove', function(e) {
            if (!isResizing) return;

            var deltaX = e.clientX - startX;
            var newLeftWidth = startLeftWidth + deltaX;
            var containerWidth = container.offsetWidth;
            var minLeftWidth = 300;
            var maxLeftWidth = containerWidth * 0.6;
            var minRightWidth = 300;

            // 限制最小和最大宽度
            if (newLeftWidth < minLeftWidth) {
                newLeftWidth = minLeftWidth;
            } else if (newLeftWidth > maxLeftWidth) {
                newLeftWidth = maxLeftWidth;
            } else if (containerWidth - newLeftWidth - 6 < minRightWidth) {
                newLeftWidth = containerWidth - minRightWidth - 6;
            }

            // 设置新宽度
            leftPanel.style.width = newLeftWidth + 'px';

            e.preventDefault();
            e.stopPropagation();
        });

        document.addEventListener('mouseup', function(e) {
            if (isResizing) {
                isResizing = false;

                // 恢复样式
                document.body.style.userSelect = '';
                document.body.style.cursor = '';

                // 恢复iframe的鼠标事件
                if (iframe) {
                    iframe.style.pointerEvents = '';
                }

                // 移除遮罩层
                removeDragOverlay();

                // 重新渲染表格以适应新宽度
                if (hotspotTable) {
                    setTimeout(function() {
                        hotspotTable.resize();
                    }, 100);
                }

                e.preventDefault();
                e.stopPropagation();
            }
        });

        // 窗口大小改变时重新计算
        window.addEventListener('resize', function() {
            var containerWidth = container.offsetWidth;
            var currentLeftWidth = leftPanel.offsetWidth;
            var minRightWidth = 300;

            if (containerWidth - currentLeftWidth - 6 < minRightWidth) {
                leftPanel.style.width = (containerWidth - minRightWidth - 6) + 'px';

                // 重新渲染表格
                if (hotspotTable) {
                    setTimeout(function() {
                        hotspotTable.resize();
                    }, 100);
                }
            }
        });
    }

    /**
     * 创建拖拽遮罩层
     */
    function createDragOverlay() {
        var overlay = document.createElement('div');
        overlay.id = 'dragOverlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            cursor: col-resize;
            background: transparent;
        `;
        document.body.appendChild(overlay);
    }

    /**
     * 移除拖拽遮罩层
     */
    function removeDragOverlay() {
        var overlay = document.getElementById('dragOverlay');
        if (overlay) {
            document.body.removeChild(overlay);
        }
    }

    /**
     * 初始化文件上传
     */
    function initFileUpload() {
        // ZIP文件上传
        var zipUploadInst = upload.render({
            elem: '#uploadZipBtn',
            url: '/panorama/upload/zip',
            accept: 'file',
            exts: 'zip',
            data: {
                taskId: function() {
                    return currentTaskId;
                }
            },
            before: function(obj) {
                if (!currentTaskId) {
                    layer.msg('请先选择任务', {icon: 2});
                    return false;
                }

                // 检查是否已存在热点数据
                checkExistingHotspotsBeforeUpload(obj, zipUploadInst);
                return false; // 阻止默认上传，由检查结果决定是否继续
            },
            done: function(res) {
                layer.closeAll('loading');
                if (res.success) {
                    layer.msg('ZIP文件上传成功', {icon: 1});
                    $('#zipStatus').text('已上传');
                    loadHotspotData();
                    loadPreview();
                } else {
                    layer.msg('上传失败: ' + res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('上传失败', {icon: 2});
            }
        });

        // 查看设备数据按钮事件
        $('#viewDeviceBtn').click(function() {
            if (!currentTaskId) {
                layer.msg('请先选择任务', {icon: 2});
                return;
            }
            showDeviceListDialog();
        });
    }

    /**
     * 显示设备列表弹窗
     */
    function showDeviceListDialog() {
        // {{CHENGQI:
        // Action: Added
        // Timestamp: 2025-01-27 16:16:00 CST
        // Reason: 新增设备列表弹窗功能，支持查看和上传设备数据
        // Principle_Applied: 用户体验优化 - 集成查看和管理功能
        // Optimization: 使用Layer弹窗 + Layui表格实现分页显示
        // }}

        var dialogHtml = '<div id="deviceListContainer" style="padding: 0;">' +
            '<div style="padding: 15px; border-bottom: 1px solid #e6e8eb; background: #fafbfc;">' +
            '<div style="display: flex; justify-content: space-between; align-items: center;">' +
            '<h3 style="margin: 0; font-size: 16px; color: #333;">' +
            '<i class="layui-icon layui-icon-table" style="color: #667eea;"></i> 单机数据管理</h3>' +
            '<div>' +
            '<!-- {{CHENGQI:' +
            '// Action: Modified' +
            '// Timestamp: 2025-01-27 16:38:00 CST' +
            '// Reason: 在上传按钮右边添加下载按钮，支持Excel导出功能' +
            '// Principle_Applied: 用户体验优化 - 提供完整的数据编辑循环' +
            '// Optimization: 按钮组合布局，功能互补' +
            '// }} -->' +
            '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="uploadExcelInDialog" style="margin-right: 8px;">' +
            '<i class="layui-icon layui-icon-upload"></i> 上传Excel</button>' +
            '<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" id="downloadExcelInDialog">' +
            '<i class="layui-icon layui-icon-download-circle"></i> 下载Excel</button>' +
            '</div>' +
            '</div></div>' +
            '<div style="height: 400px; overflow: hidden;">' +
            '<table class="layui-hide" id="deviceTable" lay-filter="deviceTable"></table>' +
            '</div></div>';

        layer.open({
            type: 1,
            title: false,
            content: dialogHtml,
            area: ['900px', '500px'],
            success: function(layero, index) {
                // 初始化设备表格
                initDeviceTable();

                // 初始化弹窗内的上传功能
                initDialogUpload();

                // 初始化弹窗内的下载功能
                initDialogDownload();
            }
        });
    }

    /**
     * 初始化设备表格
     */
    function initDeviceTable() {
        table.render({
            elem: '#deviceTable',
            url: '/panorama/device/list',
            where: {
                taskId: currentTaskId
            },
            page: true,
            limit: 15,
            limits: [15, 30, 50],
            height: 400,
            cols: [[
                {type: 'numbers', title: '序号', width: 60},
                {field: 'DEVICE_NAME', title: '单机名称', width: 150},
                {field: 'DEVICE_CODE', title: '单机代号', width: 120},
                {field: 'BATCH_NO', title: '批次号', width: 100},
                {field: 'MODEL_ID', title: '型号ID', width: 100},
                {field: 'MODEL_NAME', title: '型号名称', width: 120},
                {field: 'CREATE_TIME', title: '创建时间', width: 140},
                {field: 'UPDATE_TIME', title: '更新时间', width: 140}
            ]],
            done: function() {
                // 表格渲染完成回调
            }
        });
    }

    /**
     * 初始化弹窗内的上传功能
     */
    function initDialogUpload() {
        upload.render({
            elem: '#uploadExcelInDialog',
            url: '/panorama/upload/excel',
            accept: 'file',
            exts: 'xlsx|xls',
            data: {
                taskId: function() {
                    return currentTaskId;
                }
            },
            before: function(obj) {
                layer.load();
            },
            done: function(res) {
                layer.closeAll('loading');
                if (res.success) {
                    layer.msg('Excel文件上传成功', {icon: 1});
                    // 刷新设备表格
                    table.reload('deviceTable');
                    // 更新外部状态
                    updateDeviceStatus();
                } else {
                    layer.msg('上传失败: ' + res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('上传失败', {icon: 2});
            }
        });
    }

    /**
     * 初始化弹窗内的下载功能
     */
    function initDialogDownload() {
        // {{CHENGQI:
        // Action: Added
        // Timestamp: 2025-01-27 16:39:00 CST
        // Reason: 新增弹窗内的Excel下载功能
        // Principle_Applied: 用户体验优化 - 提供数据导出功能
        // Optimization: 使用window.open方式下载文件
        // }}
        $('#downloadExcelInDialog').click(function() {
            if (!currentTaskId) {
                layer.msg('请先选择任务', {icon: 2});
                return;
            }

            // 显示下载提示
            layer.msg('正在生成Excel文件...', {icon: 16, time: 2000});

            // 创建下载链接并触发下载
            var downloadUrl = '/panorama/device/export?taskId=' + currentTaskId;
            window.open(downloadUrl);
        });
    }

    /**
     * 更新设备状态显示
     */
    function updateDeviceStatus() {
        $.get('/panorama/device/list', {taskId: currentTaskId, page: 1, limit: 1}, function(res) {
            if (res.code === 0 && res.count > 0) {
                $('#deviceStatus').text('共' + res.count + '条');
            } else {
                $('#deviceStatus').text('-');
            }
        });
    }

    /**
     * 显示创建任务对话框
     */
    function showCreateTaskDialog() {
        console.log('显示创建任务对话框');
        console.log('layer对象:', layer);
        console.log('对话框元素:', $('#createTaskDialog'));
        console.log('对话框元素长度:', $('#createTaskDialog').length);

        // 检查layer是否可用
        if (typeof layer === 'undefined') {
            alert('Layer组件未加载，请检查Layui是否正确引入');
            return;
        }

        // 先尝试简单的消息提示
        layer.msg('测试Layer是否工作', {icon: 1});

        // 尝试直接使用HTML内容而不是jQuery对象
        var dialogHtml = $('#createTaskDialog').html();
        console.log('对话框HTML内容:', dialogHtml);

        layer.open({
            type: 1,
            title: '创建新任务',
            content: dialogHtml,
            area: ['500px', '400px'],
            btn: ['创建', '取消'],
            yes: function(index, layero) {
                console.log('点击创建按钮');
                // 手动获取表单数据并提交
                var formData = {
                    taskName: layero.find('input[name="taskName"]').val(),
                    modelId: layero.find('input[name="modelId"]').val(),
                    modelName: layero.find('input[name="modelName"]').val(),
                    description: layero.find('textarea[name="description"]').val()
                };

                console.log('表单数据:', formData);

                // 验证必填字段
                if (!formData.taskName) {
                    layer.msg('请输入任务名称', {icon: 2});
                    return false;
                }

                createTask(formData);
                return false; // 阻止默认关闭
            },
            success: function(layero, index) {
                console.log('对话框打开成功');
                // 重新渲染表单
                form.render();
            }
        });
    }

    /**
     * 创建任务
     */
    function createTask(formData) {
        $.post('/panorama/task/create', formData, function(res) {
            if (res.success) {
                layer.closeAll();
                layer.msg('任务创建成功', {icon: 1});

                // 先加载任务列表，然后自动选择新创建的任务
                loadTaskList(function() {
                    // 任务列表加载完成后，自动选择新创建的任务
                    setTimeout(function() {
                        $('#taskSelect').val(res.data.taskId);
                        form.render('select');
                        selectTask(res.data.taskId);
                    }, 200);
                });
            } else {
                layer.msg('创建失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 编辑热点
     */
    function editHotspot(hotspotData) {
        layer.open({
            type: 1,
            title: '编辑热点信息',
            content: $('#editHotspotDialog'),
            area: ['500px', '400px'],
            btn: ['保存', '取消'],
            yes: function(index, layero) {
                // 手动获取表单数据并提交
                var formData = {
                    hotspotId: hotspotData.HOTSPOT_ID,
                    editedTitle: layero.find('input[name="editedTitle"]').val(),
                    editedDescription: layero.find('textarea[name="editedDescription"]').val(),
                    deviceId: layero.find('select[name="deviceId"]').val()
                };

                updateHotspot(formData);
                return false; // 阻止默认关闭
            },
            success: function(layero, index) {
                // 填充表单数据
                layero.find('input[name="editedTitle"]').val(hotspotData.EDITED_TITLE || hotspotData.ORIGINAL_TITLE);
                layero.find('textarea[name="editedDescription"]').val(hotspotData.EDITED_DESCRIPTION || hotspotData.ORIGINAL_DESCRIPTION);

                // 更新单机选择列表
                updateDeviceSelect();

                // 设置选中的设备
                setTimeout(function() {
                    layero.find('select[name="deviceId"]').val(hotspotData.DEVICE_ID);
                    form.render('select');
                }, 100);
            }
        });
    }

    /**
     * 更新热点信息
     */
    function updateHotspot(formData) {
        $.post('/panorama/hotspot/update', formData, function(res) {
            if (res.success) {
                layer.closeAll();
                layer.msg('热点信息更新成功', {icon: 1});
                hotspotTable.reload();
                // 更新预览
                updatePreview();
            } else {
                layer.msg('更新失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 热点定位
     */
    function locateHotspot(hotspotId) {
        $.post('/panorama/hotspot/locate', {hotspotId: hotspotId}, function(res) {
            if (res.success) {
                var pan = res.data.PAN;
                var tilt = res.data.TILT;

                // 向iframe发送定位消息
                var iframe = document.getElementById('panoramaFrame');
                if (iframe && iframe.contentWindow) {
                    iframe.contentWindow.postMessage({
                        type: 'locate',
                        pan: pan,
                        tilt: tilt
                    }, '*');
                }

                layer.msg('正在定位到热点...', {icon: 1});
            } else {
                layer.msg('定位失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 加载单机信息列表
     */
    function loadDeviceList() {
        if (!currentTaskId) return;

        $.get('/panorama/device/list', {taskId: currentTaskId}, function(res) {
            if (res.success) {
                deviceList = res.data;
                updateDeviceSelect();
            }
        });
    }

    /**
     * 更新单机选择列表
     */
    function updateDeviceSelect() {
        var options = '<option value="">请选择单机</option>';
        $.each(deviceList, function(index, device) {
            options += '<option value="' + device.DEVICE_ID + '">' +
                      device.DEVICE_NAME + ' (' + device.DEVICE_CODE + ')</option>';
        });
        $('select[name="deviceId"]').html(options);
        form.render('select');
    }

    /**
     * 加载预览
     */
    function loadPreview() {
        if (!currentTaskId) return;

        $.get('/panorama/preview/path', {taskId: currentTaskId}, function(res) {
            if (res.success) {
                showPreview(res.data.previewUrl);
            } else {
                hidePreview();
            }
        });
    }

    /**
     * 显示预览
     */
    function showPreview(previewUrl) {
        $('#previewContainer .preview-placeholder').hide();
        $('#panoramaFrame').attr('src', previewUrl).show();
    }

    /**
     * 隐藏预览
     */
    function hidePreview() {
        $('#panoramaFrame').hide();
        $('#previewContainer .preview-placeholder').show();
    }

    /**
     * 更新预览
     */
    function updatePreview() {
        // 刷新iframe
        var iframe = document.getElementById('panoramaFrame');
        if (iframe && iframe.src) {
            iframe.src = iframe.src;
        }
    }

    /**
     * 导出全景图包
     */
    function exportPanorama() {
        layer.confirm('确定要导出当前任务的全景图包吗？', {
            icon: 3,
            title: '确认导出'
        }, function(index) {
            layer.close(index);

            $.post('/panorama/export', {taskId: currentTaskId}, function(res) {
                if (res.success) {
                    layer.msg('导出成功', {icon: 1});
                    // TODO: 处理下载链接
                } else {
                    layer.msg('导出失败: ' + res.msg, {icon: 2});
                }
            }).fail(function() {
                layer.msg('网络请求失败', {icon: 2});
            });
        });
    }

    /**
     * 检查是否已存在热点数据（上传前）
     */
    function checkExistingHotspotsBeforeUpload(uploadObj, uploadInstance) {
        $.get('/panorama/check/hotspots', {taskId: currentTaskId}, function(res) {
            if (res.success) {
                if (res.hasData) {
                    // 存在热点数据，显示确认对话框
                    layer.confirm('当前任务已存在热点数据，重新上传将清除所有已编辑的热点信息，是否继续？', {
                        icon: 3,
                        title: '重新上传全景图',
                        btn: ['继续上传', '取消']
                    }, function(index) {
                        // 用户确认继续上传
                        layer.close(index);
                        clearTaskDataAndUpload(uploadObj, uploadInstance);
                    }, function(index) {
                        // 用户取消上传
                        layer.close(index);
                    });
                } else {
                    // 不存在热点数据，直接上传
                    proceedWithUpload(uploadObj, uploadInstance);
                }
            } else {
                layer.msg('检查热点数据失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 清理任务数据并上传
     */
    function clearTaskDataAndUpload(uploadObj, uploadInstance) {
        layer.load();
        $.post('/panorama/clear/data', {taskId: currentTaskId}, function(res) {
            if (res.success) {
                // 清理成功，继续上传
                proceedWithUpload(uploadObj, uploadInstance);
            } else {
                layer.closeAll('loading');
                layer.msg('清理数据失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.closeAll('loading');
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 执行文件上传
     */
    function proceedWithUpload(uploadObj, uploadInstance) {
        console.log('uploadObj:', uploadObj);
        console.log('uploadInstance:', uploadInstance);

        // 显示加载状态
        if (!$('.layui-layer-loading').length) {
            layer.load();
        }

        // 使用Layui upload实例的upload方法继续上传
        if (uploadInstance && typeof uploadInstance.upload === 'function') {
            // 临时修改upload配置，移除before回调以避免循环
            var originalBefore = uploadInstance.config.before;
            uploadInstance.config.before = function() {
                layer.load(); // 显示加载状态
                return true; // 允许上传
            };

            // 重新触发上传
            uploadInstance.upload();

            // 恢复原始before回调
            setTimeout(function() {
                uploadInstance.config.before = originalBefore;
            }, 100);
        } else {
            layer.closeAll('loading');
            layer.msg('上传实例无效，请重新选择文件', {icon: 2});
        }
    }

    // 监听iframe消息
    window.addEventListener('message', function(event) {
        // 处理来自全景图iframe的消息
        if (event.data && event.data.type) {
            switch(event.data.type) {
                case 'hotspotClick':
                    // 热点被点击
                    console.log('热点被点击:', event.data);
                    break;
                case 'viewChange':
                    // 视角改变
                    console.log('视角改变:', event.data);
                    break;
            }
        }
    });
});
