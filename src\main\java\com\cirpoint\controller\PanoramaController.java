package com.cirpoint.controller;

import cn.hutool.json.JSONObject;
import com.cirpoint.service.PanoramaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 全景图热点编辑控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/panorama")
public class PanoramaController {

    private final PanoramaService panoramaService;

    @Autowired
    public PanoramaController(PanoramaService panoramaService) {
        this.panoramaService = panoramaService;
    }

    /**
     * 创建新任务
     *
     * @param taskName    任务名称
     * @param modelId     型号ID
     * @param modelName   型号名称
     * @param description 任务描述
     * @return 创建结果
     */
    @PostMapping("/task/create")
    public ResponseEntity<JSONObject> createTask(
            @RequestParam("taskName") String taskName,
            @RequestParam(value = "modelId", required = false) String modelId,
            @RequestParam(value = "modelName", required = false) String modelName,
            @RequestParam(value = "description", required = false) String description) {
        
        try {
            JSONObject result = panoramaService.createTask(taskName, modelId, modelName, description);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建任务失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "创建任务失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务列表
     *
     * @return 任务列表
     */
    @GetMapping("/task/list")
    public ResponseEntity<JSONObject> getTaskList() {
        try {
            JSONObject result = panoramaService.getTaskList();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取任务列表失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @GetMapping("/task/{taskId}")
    public ResponseEntity<JSONObject> getTaskDetail(@PathVariable("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getTaskDetail(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取任务详情失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 检查任务是否已存在热点数据
     *
     * @param taskId 任务ID
     * @return 检查结果
     */
    @GetMapping("/check/hotspots")
    public ResponseEntity<JSONObject> checkExistingHotspots(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.checkExistingHotspots(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查热点数据失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "检查热点数据失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 清理任务的热点数据和文件信息
     *
     * @param taskId 任务ID
     * @return 清理结果
     */
    @PostMapping("/clear/data")
    public ResponseEntity<JSONObject> clearTaskData(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.clearTaskData(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清理任务数据失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "清理任务数据失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 上传全景图ZIP包
     *
     * @param taskId 任务ID
     * @param file   ZIP文件
     * @return 上传结果
     */
    @PostMapping("/upload/zip")
    public ResponseEntity<JSONObject> uploadPanoramaZip(
            @RequestParam("taskId") Long taskId,
            @RequestParam("file") MultipartFile file) {
        
        try {
            JSONObject result = panoramaService.uploadPanoramaZip(taskId, file);
            return ResponseEntity.ok(result);
        } catch (IOException e) {
            log.error("上传ZIP文件失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "上传ZIP文件失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 上传单机信息Excel文件
     *
     * @param taskId 任务ID
     * @param file   Excel文件
     * @return 上传结果
     */
    @PostMapping("/upload/excel")
    public ResponseEntity<JSONObject> uploadDeviceExcel(
            @RequestParam("taskId") Long taskId,
            @RequestParam("file") MultipartFile file) {
        
        try {
            JSONObject result = panoramaService.uploadDeviceExcel(taskId, file);
            return ResponseEntity.ok(result);
        } catch (IOException e) {
            log.error("上传Excel文件失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "上传Excel文件失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务下的设备列表
     *
     * @param taskId 任务ID
     * @param page   页码
     * @param limit  每页数量
     * @return 设备列表
     */
    @GetMapping("/device/list")
    public ResponseEntity<JSONObject> getDeviceList(
            @RequestParam("taskId") Long taskId,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "limit", defaultValue = "15") int limit) {

        try {
            // {{CHENGQI:
            // Action: Added
            // Timestamp: 2025-01-27 16:13:00 CST
            // Reason: 新增设备列表查询接口，支持前端分页表格显示
            // Principle_Applied: RESTful API设计，清晰的接口职责
            // Optimization: 支持分页参数，返回标准Layui表格格式
            // }}
            JSONObject result = panoramaService.getDeviceList(taskId, page, limit);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("code", 1);
            errorResult.set("msg", "获取设备列表失败: " + e.getMessage());
            errorResult.set("count", 0);
            errorResult.set("data", new JSONArray());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取热点列表
     *
     * @param taskId 任务ID
     * @param page   页码
     * @param limit  每页数量
     * @return 热点列表
     */
    @GetMapping("/hotspot/list")
    public ResponseEntity<JSONObject> getHotspotList(
            @RequestParam("taskId") Long taskId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        
        try {
            JSONObject result = panoramaService.getHotspotList(taskId, page, limit);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取热点列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取热点列表失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 更新热点信息
     *
     * @param hotspotId   热点ID
     * @param editedTitle       编辑后热点标题
     * @param editedDescription 编辑后热点描述
     * @param deviceId    关联单机ID
     * @return 更新结果
     */
    @PostMapping("/hotspot/update")
    public ResponseEntity<JSONObject> updateHotspot(
            @RequestParam("hotspotId") Long hotspotId,
            @RequestParam(value = "editedTitle", required = false) String editedTitle,
            @RequestParam(value = "editedDescription", required = false) String editedDescription,
            @RequestParam(value = "deviceId", required = false) Long deviceId) {
        
        try {
            JSONObject result = panoramaService.updateHotspot(hotspotId, editedTitle, editedDescription, deviceId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新热点信息失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "更新热点信息失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取单机信息列表
     *
     * @param taskId 任务ID
     * @return 单机信息列表
     */
    @GetMapping("/device/list")
    public ResponseEntity<JSONObject> getDeviceList(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getDeviceList(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取单机信息列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取单机信息列表失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 热点定位
     *
     * @param hotspotId 热点ID
     * @return 定位信息
     */
    @PostMapping("/hotspot/locate")
    public ResponseEntity<JSONObject> locateHotspot(@RequestParam("hotspotId") Long hotspotId) {
        try {
            JSONObject result = panoramaService.locateHotspot(hotspotId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("热点定位失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "热点定位失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 导出修改后的全景图包
     *
     * @param taskId 任务ID
     * @return 导出结果
     */
    @PostMapping("/export")
    public ResponseEntity<JSONObject> exportPanorama(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.exportPanorama(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("导出全景图包失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "导出全景图包失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取全景图预览路径
     *
     * @param taskId 任务ID
     * @return 预览路径
     */
    @GetMapping("/preview/path")
    public ResponseEntity<JSONObject> getPreviewPath(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getPreviewPath(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取预览路径失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取预览路径失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 测试数据库连接和表结构
     *
     * @return 测试结果
     */
    @GetMapping("/test/database")
    public ResponseEntity<JSONObject> testDatabase() {
        try {
            JSONObject result = panoramaService.testDatabase();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("测试数据库失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "测试数据库失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }
}
