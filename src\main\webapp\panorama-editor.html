<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全景图热点编辑系统</title>
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="panorama/css/panorama-editor.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="panorama-header">
        <div class="header-content">
            <div class="header-left">
                <h1 class="system-title">
                    <i class="layui-icon layui-icon-camera"></i>
                    全景图热点编辑系统
                </h1>
            </div>
            <div class="header-center">
                <div class="task-selector layui-form">
                    <label>当前任务：</label>
                    <select id="taskSelect" lay-filter="taskSelect" lay-search>
                        <option value="">请选择任务</option>
                    </select>
                </div>
            </div>
            <div class="header-right">
                <button type="button" class="layui-btn layui-btn-normal" id="createTaskBtn">
                    <i class="layui-icon layui-icon-add-1"></i> 创建任务
                </button>
                <button type="button" class="layui-btn layui-btn-warm" id="exportBtn" disabled>
                    <i class="layui-icon layui-icon-export"></i> 导出
                </button>
            </div>
        </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="panorama-container">
        <div class="container-content">
            <!-- 左侧功能区域 -->
            <div class="left-panel" id="leftPanel">
                <!-- 任务信息和文件上传合并区域 -->
                <div class="task-upload-combined">
                    <div class="card-header">
                        <h3><i class="layui-icon layui-icon-form"></i> 任务信息 & 文件上传</h3>
                    </div>
                    <div class="card-body">
                        <!-- 任务信息区域 -->
                        <div class="task-info-section">
                            <div class="task-info" id="taskInfo">
                                <div class="info-item">
                                    <span class="info-label">任务名称：</span>
                                    <span class="info-value" id="taskName">未选择任务</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">型号信息：</span>
                                    <span class="info-value" id="modelInfo">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">创建时间：</span>
                                    <span class="info-value" id="createTime">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">任务状态：</span>
                                    <span class="info-value" id="taskStatus">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- 文件上传区域 -->
                        <div class="upload-section">
                            <div class="upload-item">
                                <span class="upload-label">全景图ZIP：</span>
                                <div class="upload-controls">
                                    <button type="button" class="layui-btn layui-btn-normal" id="uploadZipBtn" disabled>
                                        <i class="layui-icon layui-icon-upload"></i> 选择
                                    </button>
                                    <span class="upload-status" id="zipStatus">未上传</span>
                                </div>
                            </div>
                            <div class="upload-item">
                                <span class="upload-label">单机Excel：</span>
                                <div class="upload-controls">
                                    <button type="button" class="layui-btn layui-btn-normal" id="uploadExcelBtn" disabled>
                                        <i class="layui-icon layui-icon-upload"></i> 选择
                                    </button>
                                    <span class="upload-status" id="excelStatus">未上传</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 热点编辑表格 -->
                <div class="table-card">
                    <div class="card-header">
                        <h3><i class="layui-icon layui-icon-edit"></i> 热点编辑</h3>
                        <div class="card-tools">
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="refreshTableBtn">
                                <i class="layui-icon layui-icon-refresh"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="layui-hide" id="hotspotTable" lay-filter="hotspotTable"></table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 可拖拽分隔条 -->
            <div class="resize-handle" id="resizeHandle"></div>

            <!-- 右侧预览区域 -->
            <div class="right-panel" id="rightPanel">
                <div class="preview-card">
                    <div class="card-header">
                        <h3><i class="layui-icon layui-icon-camera-fill"></i> 全景图预览</h3>
                        <div class="card-tools">
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="refreshPreviewBtn">
                                <i class="layui-icon layui-icon-refresh"></i> 刷新预览
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="preview-container" id="previewContainer">
                            <div class="preview-placeholder">
                                <i class="layui-icon layui-icon-picture"></i>
                                <p>请先选择任务并上传全景图文件</p>
                            </div>
                            <iframe id="panoramaFrame" class="panorama-iframe" style="display: none;"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建任务对话框 -->
    <div id="createTaskDialog" class="layui-hide">
        <form class="layui-form" lay-filter="createTaskForm" style="padding: 20px;">
            <div class="layui-form-item">
                <label class="layui-form-label">任务名称</label>
                <div class="layui-input-block">
                    <input type="text" name="taskName" required lay-verify="required" 
                           placeholder="请输入任务名称" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">型号ID</label>
                <div class="layui-input-block">
                    <input type="text" name="modelId" placeholder="请输入型号ID"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">型号名称</label>
                <div class="layui-input-block">
                    <input type="text" name="modelName" placeholder="请输入型号名称"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">任务描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入任务描述" 
                              class="layui-textarea"></textarea>
                </div>
            </div>
        </form>
    </div>

    <!-- 热点编辑对话框 -->
    <div id="editHotspotDialog" class="layui-hide">
        <form class="layui-form" lay-filter="editHotspotForm" style="padding: 20px;">
            <input type="hidden" name="hotspotId">
            <div class="layui-form-item">
                <label class="layui-form-label">编辑后标题</label>
                <div class="layui-input-block">
                    <input type="text" name="editedTitle" placeholder="请输入编辑后的热点标题"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">编辑后描述</label>
                <div class="layui-input-block">
                    <textarea name="editedDescription" placeholder="请输入编辑后的热点描述"
                              class="layui-textarea"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">关联单机</label>
                <div class="layui-input-block">
                    <select name="deviceId" lay-filter="deviceSelect" lay-search>
                        <option value="">请选择单机</option>
                    </select>
                </div>
            </div>
        </form>
    </div>

    <!-- 引入JS文件 -->
    <script src="static/lib/jquery/jquery.min.js"></script>
    <script src="static/lib/layui/layui.js"></script>
    <script src="panorama/js/panorama-editor.js"></script>

    <!-- 表格操作列模板 -->
    <script type="text/html" id="hotspotTableBar">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="locate">定位</a>
    </script>

    <!-- 编辑状态模板 -->
    <script type="text/html" id="editStatusTpl">
        {{# if(d.IS_EDITED == 1){ }}
            <span class="layui-badge layui-bg-green">已编辑</span>
        {{# } else { }}
            <span class="layui-badge">未编辑</span>
        {{# } }}
    </script>
</body>
</html>
