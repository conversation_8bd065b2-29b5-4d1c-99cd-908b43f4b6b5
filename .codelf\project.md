## {Project Name} (init from readme/docs)

> {Project Description}

> {Project Purpose}

> {Project Status}

> {Project Team}

> {Framework/language/other(you think it is important to know)}



## Dependencies (init from programming language specification like package.json, requirements.txt, etc.)

* package1 (version): simple description
* package2 (version): simple description


## Development Environment

> include all the tools and environments needed to run the project
> makefile introduction (if exists)


## Structrue (init from project tree)

> It is essential to consistently refine the analysis down to the file level — this level of granularity is of utmost importance.

> If the number of files is too large, you should at least list all the directories, and provide comments for the parts you consider particularly important.

> In the code block below, add comments to the directories/files to explain their functionality and usage scenarios.

> if you think the directory/file is not important, you can not skip it, just add a simple comment to it.

> but if you think the directory/file is important, you should read the files and add more detail comments on it (e.g. add comments on the functions, classes, and variables. explain the functionality and usage scenarios. write the importance of the directory/file).
```
root
- .blackboxrules
- .cursor
    - record
        - 20250424-结构件打印二维码功能.md
        - 20250427-AIT看板中现场问题单和临时单增加全部导出.md
        - 20250507-AIT质量确认中统计签署和锁定表的信息.md
        - 20250514-发射场确认中增加自动生成目录的功能.md
        - 20250521-BPM系统接口集成_技术状态更改单.md
        - 20250523-BPM系统接口集成_不合格品审理单.md
        - 20250527-处理BPM系统表单.md
        - 20250528-BPM接口数据插入数据库.md
    - rules
        - data-package-management-architecture.mdc
        - derived-cursor-rules.mdc
        - development-best-practices.mdc
        - thingworx.mdc
- .cursorignore
- .cursorindexingignore
- .gitignore
- .windsurfrules
- build
    - css
        - app.css
        - app_black.css
        - doc.css
        - font-awesome.min.css
        - message.css
        - nprogress.css
        - themes
            - blue.css
            - light.css
    - images
        - pay.png
    - js
        - app.js
        - carousel.js
        - code.js
        - element.js
        - flow.js
        - form.js
        - jquery.js
        - laydate.js
        - layedit.js
        - layer.js
        - layim.js
        - laypage.js
        - laytpl.js
        - loader.js
        - message.js
        - mobile.js
        - navbar.js
        - nprogress.js
        - onelevel.js
        - pjax.js
        - tab.js
        - table.js
        - tree.js
        - upload.js
        - utils.js
- ca_login.html
- components
    - aitScreen
        - aitScreen.css
        - aitScreen.html
        - aitScreen.js
        - bhgpsl.html
        - img
            - bg_1.png
            - bg_1_2x.png
            - bg_2.png
            - header_bg.png
            - icon_single_board.png
            - num_bg.png
            - title_1.png
            - title_1_2x.png
            - title_2.png
        - js
            - change-order-chart.js
            - common.js
            - handle-chart.js
            - init.js
            - list-chart.js
            - model-process.js
            - nonconformity-chart.js
            - problem-chart.js
            - submit-chart.js
            - temp-chart.js
        - jsztggd.html
        - viewTable.html
    - bigScreen
        - bigScreen.css
        - bigScreen.html
        - bigScreen.js
        - chartOption.js
        - img
            - bg.png
            - bg_1.png
            - bg_2.png
            - bg_3.png
            - bg_4.png
            - bg_5.png
            - bg_center.jpg
            - bg_titile.png
            - bg_title_1.png
            - icon_entity.png
            - icon_ex factory.png
            - icon_file.png
            - icon_fold.png
            - icon_image.png
            - icon_integrate.png
            - icon_more.png
            - icon_open.png
            - icon_product.png
            - icon_quality.png
            - icon_record.png
            - icon_s.png
            - icon_single_board.png
            - icon_split.png
            - icon_stage.png
            - icon_state.png
            - icon_statistics.png
            - icon_structure.png
            - num_bg.png
            - sort_1.png
            - sort_2.png
        - SecondTable.js
    - captureManagement
        - addConfigData.html
        - captureManagement.html
        - captureManagement.js
        - dataPackageLink.html
        - editConfigData.html
        - manualUpload.html
        - refDataPackage.js
        - stateCheck.html
        - tree.js
        - uploadManyFile.js
    - costing
        - analyse
            - analyse.css
            - analyse.html
            - analyse.js
            - form.js
            - table.js
        - css
            - common.css
        - flow
            - flow.css
            - flow.html
            - flow.js
        - form
            - form.css
            - form.html
            - form.js
            - handson.js
        - js
            - common.js
            - upload.js
        - model
            - calc.js
            - img
                - add.png
                - calc.png
                - edit.png
                - folder.png
                - model.png
                - remove.png
            - model.css
            - model.html
            - model.js
            - param.js
            - result.js
            - tree.js
            - verify.js
        - statistic
            - statistic.html
            - statistic.js
        - task
            - task.html
            - task.js
        - zero
            - finished
                - finished.js
            - form.js
            - myTodo
                - myTodo.js
            - process
                - process.js
                - view.js
            - user.js
            - zero.css
            - zero.html
            - zero.js
    - dataSearch
        - advancedSearch.html
        - advancedSearch.js
        - dataSearch.html
        - dataSearch.js
        - dataSearchTable.js
        - keyItemAnalysisSearch
            - keyItemAnalysisProductSearch.html
            - keyItemAnalysisSearch.html
        - processDataSearch
            - processDataSearch.js
            - tableCol.js
        - processDataSearch.html
        - processDataSearch.js
        - ProcessQuality.js
        - productDataSearch.html
        - productDataSearch.js
        - productList.js
        - productQuality.js
        - QualityPhoto.js
        - qualitySearch
            - BomTree.js
            - DataTable.js
            - qualitySearch.html
            - qualitySearch.js
        - tableCol.js
    - dataTree
        - bom_tree.js
        - images
            - A.png
            - add.png
            - add1.png
            - B.png
            - batch-mark.png
            - certificate.png
            - copy.png
            - copy1.png
            - dir.png
            - download.png
            - edit.png
            - edit1.png
            - end.png
            - Excel.png
            - excel1.png
            - excelupload.png
            - excelupload1.png
            - export-data.png
            - file-export.png
            - folder-open.png
            - folder.png
            - folder1.png
            - id.png
            - import-data.png
            - init.png
            - lock.png
            - mark.png
            - morepdf.png
            - notstart.png
            - pdf.png
            - pdf1.png
            - phase.png
            - phase_z.png
            - push.png
            - recover.png
            - refresh.png
            - remove.png
            - remove1.png
            - report.png
            - root.png
            - set-status.png
            - sign.png
            - sign1.png
            - start.png
            - statistics.png
            - stop-sync.png
            - sync.png
            - table.png
            - table1.png
            - tag.png
            - transfer.png
            - unlock.png
            - update.png
            - upload-image.png
            - upload.png
            - view.png
            - word.png
            - workType.png
            - zip.png
            - 卫星.png
            - 拍照.png
        - index.html
        - index.js
        - index1.js
        - tree.js
        - tree1.js
    - downloadManagement
        - index.html
        - index.js
        - push.js
    - excelDataView
        - addendum.html
        - addendum.js
        - attachment.html
        - attachment.js
        - cableTest.html
        - cableTest.js
        - cphoto.html
        - cphoto.js
        - electronicComponents.html
        - electronicComponents.js
        - gphoto.html
        - gphoto.js
        - heatingReinspection.html
        - heatingReinspection.js
        - heatingTest.html
        - heatingTest.js
        - listData.html
        - listData.js
        - materiel.html
        - materiel.js
        - record.html
        - record.js
    - indexAnalysis
        - indexAnalysis.css
        - indexAnalysis.html
        - indexAnalysis.js
    - js
        - base64.js
        - config
            - twxconfig.js
        - handle360.js
        - HandsonTableUtil.js
        - index.js
        - intercept.js
        - js-NSV.js
        - listDataOpt.js
        - login.js
        - login2.js
        - login_jsp.js
        - logUtil.js
        - onlineConfirm.js
        - onlineTree.js
        - pdfExportDialog.js
        - sessionUtils.js
        - sign.js
        - UDS.js
        - UDSVideoDemo.html
        - util.js
        - utils.js
    - launchConfirm
        - launchConfirm.html
        - launchConfirm.js
        - tree.js
    - launchConfirmConfig
        - launchConfirmConfig.html
        - launchConfirmConfig.js
        - tree.js
    - logs
        - confirmLogs.html
        - confirmLogs.js
        - integrationLogs.html
        - integrationLogs.js
        - logs.html
        - logTableUtil.js
    - photoQuery
        - photoQuery.css
        - photoQuery.html
        - photoQuery.js
    - planBuild
        - planBuild.html
        - planBuild.js
        - refDataPackage.js
    - preview
        - docxjs-master
            - .editorconfig
            - .github
                - ISSUE_TEMPLATE
                    - document-rendering-bug-report.md
                - workflows
                    - webpack.yml
            - .gitignore
            - demo
                - thumbnail.example.css
                - thumbnail.example.js
            - dist
                - docx-preview.d.ts
                - docx-preview.js
                - docx-preview.js.map
                - docx-preview.min.js
                - docx-preview.min.js.map
                - docx-preview.min.mjs
                - docx-preview.min.mjs.map
                - docx-preview.mjs
                - docx-preview.mjs.map
            - index.html
            - karma.conf.js
            - LICENSE
            - README.md
            - src
                - common
                    - open-xml-package.ts
                    - part.ts
                    - relationship.ts
                - document
                    - bookmarks.ts
                    - border.ts
                    - common.ts
                    - document-part.ts
                    - document.ts
                    - dom.ts
                    - fields.ts
                    - line-spacing.ts
                    - paragraph.ts
                    - run.ts
                    - section.ts
                    - style.ts
                - document-parser.ts
                - document-props
                    - core-props-part.ts
                    - core-props.ts
                    - custom-props-part.ts
                    - custom-props.ts
                    - extended-props-part.ts
                    - extended-props.ts
                - docx-preview.ts
                - font-table
                    - font-table.ts
                    - fonts.ts
                - header-footer
                    - elements.ts
                    - parts.ts
                - html-renderer.ts
                - javascript.ts
                - length.ts
                - notes
                    - elements.ts
                    - parts.ts
                - numbering
                    - numbering-part.ts
                    - numbering.ts
                - parser
                    - xml-parser.ts
                - settings
                    - settings-part.ts
                    - settings.ts
                - styles
                    - styles-part.ts
                - theme
                    - theme-part.ts
                    - theme.ts
                - typings.d.ts
                - utils.ts
                - vml
                    - vml.ts
                - word-document.ts
            - tests
                - extended-props-test
                    - document.docx
                    - extended-props.spec.js
                - render-test
                    - equation
                        - document.docx
                        - result.html
                    - footnote
                        - document.docx
                        - result.html
                    - header-footer
                        - document.docx
                        - result.html
                    - line-spacing
                        - document.docx
                        - result.html
                    - numbering
                        - document.docx
                        - result.html
                    - page-layout
                        - document.docx
                        - result.html
                    - revision
                        - document.docx
                        - result.html
                    - table
                        - document.docx
                        - result.html
                    - table-spans
                        - document.docx
                        - result.html
                    - test.spec.js
                    - text
                        - document.docx
                        - result.html
                    - text-break
                        - document.docx
                        - result.html
                    - underlines
                        - document.docx
                        - result.html
            - webpack.config.js
        - preview.html
    - productQuality
        - productQuality.html
        - productQuality.js
    - publishMission
        - addCraftData.html
        - addDesignData.html
        - addProcessManagementData.html
        - addQualityControlData.html
        - craftDataList.html
        - designDataList.html
        - progressManagementDataList.html
        - qualityControlDataList.html
        - updateCraftData.html
        - updateDesignData.html
        - updateProcessManagementData.html
        - updateQualityControlData.html
    - publishMission_backup
        - addCraftData.html
        - addDesignData.html
        - addProcessManagementData.html
        - addQualityControlData.html
        - craftDataList.html
        - designDataList.html
        - progressManagementDataList.html
        - qualityControlDataList.html
        - updateCraftData.html
        - updateDesignData.html
        - updateProcessManagementData.html
        - updateQualityControlData.html
    - qualityConfirm
        - qualityConfirm.html
        - qualityConfirm.js
        - tree.js
    - qualityReport
        - downloadList.js
        - electricTest.js
        - featureTestCalculation.js
        - lockStatistics.html
        - ProcessQuality.js
        - QualityPhoto.js
        - qualityReport.html
        - qualityReport.js
        - signStatistics.html
        - structureComponent.js
        - tableButtons.js
        - tableContentLoading.js
        - tableStatistics.js
        - tree.js
        - treeContextMenu.js
        - viewTable
            - layui
                - css
                    - layui.css
                - font
                    - iconfont.eot
                    - iconfont.svg
                    - iconfont.ttf
                    - iconfont.woff
                    - iconfont.woff2
                - layui.js
            - view.css
            - view.js
            - viewTable.html
    - qualityReportTpl
        - qualityReportTpl.html
        - qualityReportTpl.js
        - tree.js
    - qualityTestDataQuery
        - main.js
        - qualityTestDataQuery.html
        - table-config.js
    - staticticsReport
        - connectoronoff.html
        - connectoronoff.js
        - connectoronofftimes.html
        - connectoronofftimes.js
        - heater.html
        - heater.js
        - heatresist.html
        - heatresist.js
        - layersonoff.html
        - layersonoff.js
        - loadTable.js
        - standAlong.html
        - standAlong.js
    - systemManagement
        - basicConfig
            - basicConfig.html
            - basicConfig.js
        - menuMgr
            - func.js
            - menu.js
            - menuMgr.html
            - tpl
                - funcInfo.html
                - menuInfo.html
        - roleMgr
            - assignMenu.js
            - role.js
            - roleMgr.html
            - tpl
                - roleInfo.html
        - templateMgr
            - templateMgr.html
            - templateMgr.js
        - userMgr
            - modelMgr.js
            - role.js
            - user.js
            - userMgr.html
            - userMgr.js
        - webServiceConfig
            - addConfigData.html
            - editConfigData.html
            - webServiceConfig.html
            - webServiceConfig.js
    - tableConfig
        - js
            - common.js
            - input.js
            - param.js
            - table.js
            - tree.js
        - tableConfig.html
        - tableConfig.js
    - tableSearch
        - dataSearchTable.js
        - tableSearch.html
        - tableSearch.js
    - taskInfo
        - file.js
        - planTask
            - handlePlanTask
                - file.js
                - handlePlanTask.css
                - handlePlanTask.html
                - handlePlanTask.js
                - new_file.html
                - table.js
                - tree.js
                - util.js
            - planTask.html
            - planTask.js
        - table.js
        - taskInfo.html
        - taskInfo.js
        - tree.js
        - util.js
    - testEvaluation
        - img
            - folder.png
            - model.png
            - product.png
            - root.png
            - subsystem.png
        - js
            - file-table.js
            - file.js
            - table.js
            - tree.js
        - testEvaluation.css
        - testEvaluation.html
        - testEvaluation.js
    - testEvaluationScreen
        - deploymentTypeTable.js
        - fileChart.js
        - modelProgressChart.js
        - productChart.js
        - statisticsTable.js
        - statusTable.js
        - testEvaluationScreen.css
        - testEvaluationScreen.html
        - testEvaluationScreen.js
    - testEvent
        - img
            - model.png
            - product.png
            - root.png
            - subsystem.png
        - js
            - table.js
            - tree.js
        - testEvent.css
        - testEvent.html
        - testEvent.js
    - viewTable
        - data
            - data.json
            - photo
                - 2025-03
                    - 58eb6abc-3124-4f69-ac75-05901b3ffee1
                    - 5b9022ae-8ecd-4575-b693-45c32e198474
                - test.json
        - layui
            - css
                - layui.css
            - font
                - iconfont.eot
                - iconfont.svg
                - iconfont.ttf
                - iconfont.woff
                - iconfont.woff2
            - layui.js
        - view.css
        - view.js
        - viewTable.html
    - workbench
        - chart.js
        - constant.js
        - my-table.js
        - workbench.css
        - workbench.html
        - workbench.js
- css
    - admin.css
    - animate.css
    - bootstrap
        - bootstrap.min.css
    - font-awesome.min.css
    - HotStyle.css
    - icon.css
    - login
        - admin.css
        - login.css
    - login.css
    - second-table.css
    - style.css
- deploy
    - Deploy.class
    - Deploy.java
    - 一键更新部署.bat
- docs
    - 试验鉴定模块.md
    - 试验鉴定看板.md
- FileHandle
    - FileHandle.iml
    - src
        - com
            - cirpoint
                - bom
                    - ExportBomTpl.java
                    - ImportBom.java
                - config.properties
                - DeleteFile.java
                - DownloadExcel.java
                - DownloadExcelService.java
                - DownloadFile.java
                - DownloadLocaleFile.java
                - electricTest
                    - ExportTestEvent.java
                    - SyncCreateTable.java
                    - SyncTestTable.java
                    - TestCreateFile.java
                    - TestCreateTemporaryFile.java
                - ExcelUtil.java
                - ExcelUtil3.java
                - ExportConfirmLogExcel.java
                - ExportExcel2.java
                - ExportLogExcel.java
                - ExportSecondExcel.java
                - ExportUserExcel.java
                - ExportUserTpl.java
                - file
                    - DeleteTempFolder.java
                    - DeleteTempPhoto.java
                    - DownloadPhoto.java
                    - ExportModelProcess.java
                    - GetFileSize.java
                    - UpdateModelFileSize.java
                    - UploadBigFile.java
                - GetPhotoIsExist.java
                - hdfs
                    - DownloadHdfsServlet.java
                    - ListHdfsServlet.java
                    - TestHDFS.java
                    - UploadHdfsServlet.java
                - ImportExcel.java
                - ImportExcel2.java
                - ImportUser.java
                - launch
                    - ConvertAutoTableServlet.java
                    - ExportMappingServlet.java
                    - ImportMappingServlet.java
                    - LaunchConfirmService.java
                - list
                    - ExportDataPkg.java
                    - ExportListExcel.java
                    - Get360ImageViewUrl.java
                    - GetPDMFilePath.java
                    - PushFilesSystem.java
                    - ws
                        - client
                            - UploadFileAxisClientEx.java
                            - UploadRestClient.java
                            - UploadWSClient.java
                            - WsClientDemo.java
                        - interceptor
                            - ClientLoginInterceptor.java
                - onlineConfirm
                    - BatchImportTable.java
                    - DealBase64.java
                    - DealSignHtml.java
                    - ExportExcel.java
                    - ExportImg.java
                    - ExportMoreExcel.java
                    - ExportMorePdf.java
                    - ExportPdf.java
                    - ExportPdfZip.java
                    - ExportWorkHours.java
                    - ExportZip.java
                    - GenerateMorePdf.java
                    - GenerateZip.java
                    - GetIp.java
                    - ImportBigZip.java
                    - ImportExcel.java
                    - ImportPdf.java
                    - ImportTable.java
                - OpenFile.java
                - ParsingExcel.java
                - POIReadExcelToHtml.java
                - ReadExcel.java
                - report
                    - ExportExcel.java
                    - ExportMoreExcel.java
                    - ExportMorePdf.java
                    - ExportPdf.java
                    - ExportPdfZip.java
                    - ExportZip.java
                    - GenerateMorePdf.java
                    - ImportBigZip.java
                    - ImportExcel.java
                    - ImportPdf.java
                    - ImportTestExcel.java
                    - ImportTplExcel.java
                    - TreeNode.java
                    - TreeUtil.java
                - superior
                    - DownloadDocFile.java
                    - UploadDocFile.java
                - table
                    - Certificate.java
                    - DeletePhoto.java
                    - DownloadAllPhotos.java
                    - DownloadCustomTpl.java
                    - DownloadMesFile.java
                    - DownloadTdPhotos.java
                    - ExcelToHtml.java
                    - ExportCustomExcel.java
                    - ExportPhotoPlanExcel.java
                    - ExportQualityExcel.java
                    - ExportQualityPhotoExcel.java
                    - ExportSecondExcel.java
                    - GenerateAllPhotos.java
                    - GenerateQualityPhotoExcel.java
                    - GetSecondTableHeader.java
                    - ImportMesThreeExcel.java
                    - ImportPlanTable.java
                    - ImportThreeExcel.java
                    - Upload360Photo.java
                    - UploadPhoto.java
                    - WebUploadPhoto.java
                - testEvaluation
                    - ExportDataPackage.java
                    - UploadTestFile.java
                - Upload.java
                - UploadExcelTpl.java
                - util
                    - Base64File.java
                    - DealThings.java
                    - DeployLaunch.java
                    - ExcelUtil.java
                    - GeneratePDFUtil.java
                    - GeneratePDFUtil1.java
                    - HandSonTableUtil.java
                    - ImportXmlProblem.java
                    - ImportXmlSubmit.java
                    - PageMarker.java
                    - Test.java
                    - TestCSV.java
                    - TestReadHasImageExcel.java
                    - TreeNode.java
                    - TreeUtil.java
                    - Util.java
                - ZipUtil.java
        - license.xml
        - log4j.properties
- gulpfile.js
- img
    - analysis.png
    - analysis_b.png
    - chart.png
    - clear.png
    - clear1.png
    - confirm.png
    - design.png
    - download.png
    - excel.png
    - favicon.ico
    - female.png
    - file.png
    - gongyi.png
    - guocheng.png
    - locked.png
    - login-background.jpg
    - login-background1.jpg
    - login-background2.jpg
    - login-background3.jpg
    - login-background4.jpg
    - logo-img.png
    - logo-img1.png
    - luser.png
    - male.png
    - manualSyn.png
    - myadd.png
    - myConfig.png
    - mydelete.png
    - myedit.png
    - myview.png
    - my_print_qrcode.png
    - ok.png
    - pause.png
    - photo.png
    - preview.png
    - product.png
    - pull_right.png
    - quanlity.png
    - refresh.png
    - refresh1.png
    - relation.png
    - report.png
    - save.png
    - search.png
    - search1.png
    - select.png
    - start.png
    - upload.png
    - user.png
    - 全景图片.png
- index.html
- index1.html
- login.html
- login.jsp
- login2.html
- package-lock.json
- package.json
- plugins
    - bootstrap
        - bootstrap.min.css
    - bpmn-js
        - bpmn-js.css
        - bpmn-viewer.development.js
        - diagram-js.css
    - common
        - base64.min.js
        - jquery-3.7.0.min.js
        - jquery-3.7.1.min.js
        - lodash.min.js
    - contextMenu
        - .keep
        - bootstrap-combined.min.css
        - contextMenu.js
        - prettify.js
    - docx
        - bootstrap.bundle.min.js
        - bootstrap.min.css
        - docx-preview.js
        - jszip.min.js
    - easyui
        - easyloader.js
        - jq-signature.js
        - jquery.easyui.debug.js
        - jquery.easyui.min.js
        - jquery.jsPlumb-1.3.16-all-min.js
        - jquery.jsPlumb-1.7.5.js
        - jquery.min.js
        - jsPlumb.min.js
        - locale
            - easyui-lang-af.js
            - easyui-lang-am.js
            - easyui-lang-ar.js
            - easyui-lang-bg.js
            - easyui-lang-ca.js
            - easyui-lang-cs.js
            - easyui-lang-cz.js
            - easyui-lang-da.js
            - easyui-lang-de.js
            - easyui-lang-el.js
            - easyui-lang-en.js
            - easyui-lang-es.js
            - easyui-lang-fa.js
            - easyui-lang-fr.js
            - easyui-lang-it.js
            - easyui-lang-jp.js
            - easyui-lang-ko.js
            - easyui-lang-nl.js
            - easyui-lang-pl.js
            - easyui-lang-pt_BR.js
            - easyui-lang-ru.js
            - easyui-lang-sv_SE.js
            - easyui-lang-tr.js
            - easyui-lang-ua.js
            - easyui-lang-zh_CN.js
            - easyui-lang-zh_TW.js
        - plugins
            - copy.js
            - jquery.accordion.js
            - jquery.calendar
            - jquery.calendar.js
            - jquery.checkbox.js
            - jquery.combo.js
            - jquery.combobox.js
            - jquery.combogrid.js
            - jquery.combotree.js
            - jquery.combotreegrid.js
            - jquery.datagrid.js
            - jquery.datalist.js
            - jquery.datebox.js
            - jquery.datetimebox.js
            - jquery.datetimespinner.js
            - jquery.dialog.js
            - jquery.draggable.js
            - jquery.droppable.js
            - jquery.filebox.js
            - jquery.form.js
            - jquery.layout.js
            - jquery.linkbutton.js
            - jquery.maskedbox.js
            - jquery.menu.js
            - jquery.menubutton.js
            - jquery.messager.js
            - jquery.mobile.js
            - jquery.numberbox.js
            - jquery.numberspinner.js
            - jquery.pagination.js
            - jquery.panel.js
            - jquery.parser.js
            - jquery.passwordbox.js
            - jquery.progressbar.js
            - jquery.propertygrid.js
            - jquery.radiobutton.js
            - jquery.resizable.js
            - jquery.searchbox.js
            - jquery.sidemenu.js
            - jquery.slider.js
            - jquery.spinner.js
            - jquery.splitbutton.js
            - jquery.switchbutton.js
            - jquery.tabs.js
            - jquery.tagbox.js
            - jquery.textbox.js
            - jquery.timespinner.js
            - jquery.tooltip.js
            - jquery.tree.js
            - jquery.treegrid.js
            - jquery.validatebox.js
            - jquery.window.js
        - themes
            - angular.css
            - black
                - accordion.css
                - calendar.css
                - checkbox.css
                - combo.css
                - combobox.css
                - datagrid.css
                - datalist.css
                - datebox.css
                - dialog.css
                - easyui.css
                - filebox.css
                - images
                    - accordion_arrows.png
                    - blank.gif
                    - calendar_arrows.png
                    - combo_arrow.png
                    - datagrid_icons.png
                    - datebox_arrow.png
                    - layout_arrows.png
                    - linkbutton_bg.png
                    - loading.gif
                    - menu_arrows.png
                    - messager_icons.png
                    - pagination_icons.png
                    - panel_tools.png
                    - passwordbox_close.png
                    - passwordbox_open.png
                    - searchbox_button.png
                    - slider_handle.png
                    - spinner_arrows.png
                    - tabs_icons.png
                    - tagbox_icons.png
                    - tree_icons.png
                    - validatebox_warning.png
                - layout.css
                - linkbutton.css
                - menu.css
                - menubutton.css
                - messager.css
                - numberbox.css
                - pagination.css
                - panel.css
                - passwordbox.css
                - progressbar.css
                - propertygrid.css
                - radiobutton.css
                - searchbox.css
                - sidemenu.css
                - slider.css
                - spinner.css
                - splitbutton.css
                - switchbutton.css
                - tabs.css
                - tagbox.css
                - textbox.css
                - timepicker.css
                - tooltip.css
                - tree.css
                - validatebox.css
                - window.css
            - bootstrap
                - accordion.css
                - calendar.css
                - checkbox.css
                - combo.css
                - combobox.css
                - datagrid.css
                - datalist.css
                - datebox.css
                - dialog.css
                - easyui.css
                - filebox.css
                - images
                    - accordion_arrows.png
                    - blank.gif
                    - calendar_arrows.png
                    - combo_arrow.png
                    - datagrid_icons.png
                    - datebox_arrow.png
                    - layout_arrows.png
                    - linkbutton_bg.png
                    - loading.gif
                    - menu_arrows.png
                    - messager_icons.png
                    - pagination_icons.png
                    - panel_tools.png
                    - passwordbox_close.png
                    - passwordbox_open.png
                    - searchbox_button.png
                    - slider_handle.png
                    - spinner_arrows.png
                    - tabs_icons.png
                    - tagbox_icons.png
                    - tree_icons.png
                    - validatebox_warning.png
                - layout.css
                - linkbutton.css
                - menu.css
                - menubutton.css
                - messager.css
                - numberbox.css
                - pagination.css
                - panel.css
                - passwordbox.css
                - progressbar.css
                - propertygrid.css
                - radiobutton.css
                - searchbox.css
                - sidemenu.css
                - slider.css
                - spinner.css
                - splitbutton.css
                - switchbutton.css
                - tabs.css
                - tagbox.css
                - textbox.css
                - timepicker.css
                - tooltip.css
                - tree.css
                - validatebox.css
                - window.css
            - color.css
            - default
                - accordion.css
                - calendar.css
                - checkbox.css
                - combo.css
                - combobox.css
                - datagrid.css
                - datalist.css
                - datebox.css
                - dialog.css
                - easyui.css
                - filebox.css
                - images
                    - accordion_arrows.png
                    - blank.gif
                    - calendar_arrows.png
                    - combo_arrow.png
                    - datagrid_icons.png
                    - datebox_arrow.png
                    - layout_arrows.png
                    - linkbutton_bg.png
                    - loading.gif
                    - menu_arrows.png
                    - messager_icons.png
                    - pagination_icons.png
                    - panel_tools.png
                    - passwordbox_close.png
                    - passwordbox_open.png
                    - searchbox_button.png
                    - slider_handle.png
                    - spinner_arrows.png
                    - tabs_icons.png
                    - tagbox_icons.png
                    - tree_icons.png
                    - validatebox_warning.png
                - layout.css
                - linkbutton.css
                - menu.css
                - menubutton.css
                - messager.css
                - numberbox.css
                - pagination.css
                - panel.css
                - passwordbox.css
                - progressbar.css
                - propertygrid.css
                - radiobutton.css
                - searchbox.css
                - sidemenu.css
                - slider.css
                - spinner.css
                - splitbutton.css
                - switchbutton.css
                - tabs.css
                - tagbox.css
                - textbox.css
                - timepicker.css
                - tooltip.css
                - tree.css
                - validatebox.css
                - window.css
            - gray
                - accordion.css
                - calendar.css
                - checkbox.css
                - combo.css
                - combobox.css
                - datagrid.css
                - datalist.css
                - datebox.css
                - dialog.css
                - easyui.css
                - easyui1.css
                - filebox.css
                - images
                    - accordion_arrows.png
                    - blank.gif
                    - calendar_arrows.png
                    - combo_arrow.png
                    - datagrid_icons.png
                    - datebox_arrow.png
                    - layout_arrows.png
                    - linkbutton_bg.png
                    - loading.gif
                    - menu_arrows.png
                    - messager_icons.png
                    - pagination_icons.png
                    - panel_tools.png
                    - passwordbox_close.png
                    - passwordbox_open.png
                    - searchbox_button.png
                    - slider_handle.png
                    - spinner_arrows.png
                    - tabs_icons.png
                    - tagbox_icons.png
                    - tree_icons.png
                    - validatebox_warning.png
                - layout.css
                - linkbutton.css
                - menu.css
                - menubutton.css
                - messager.css
                - numberbox.css
                - pagination.css
                - panel.css
                - passwordbox.css
                - progressbar.css
                - propertygrid.css
                - radiobutton.css
                - searchbox.css
                - sidemenu.css
                - slider.css
                - spinner.css
                - splitbutton.css
                - switchbutton.css
                - tabs.css
                - tagbox.css
                - textbox.css
                - timepicker.css
                - tooltip.css
                - tree.css
                - validatebox.css
                - window.css
            - icon.css
            - icons
                - back.png
                - blank.gif
                - cancel.png
                - clear.png
                - cut.png
                - edit_add.png
                - edit_remove.png
                - filesave.png
                - filter.png
                - help.png
                - large_chart.png
                - large_clipart.png
                - large_picture.png
                - large_shapes.png
                - large_smartart.png
                - lock.png
                - man.png
                - mini_add.png
                - mini_edit.png
                - mini_refresh.png
                - more.png
                - no.png
                - ok.png
                - pencil.png
                - print.png
                - redo.png
                - reload.png
                - search.png
                - sum.png
                - tip.png
                - undo.png
            - material
                - accordion.css
                - calendar.css
                - checkbox.css
                - combo.css
                - combobox.css
                - datagrid.css
                - datalist.css
                - datebox.css
                - dialog.css
                - easyui.css
                - filebox.css
                - images
                    - accordion_arrows.png
                    - blank.gif
                    - calendar_arrows.png
                    - combo_arrow.png
                    - datagrid_icons.png
                    - datebox_arrow.png
                    - layout_arrows.png
                    - linkbutton_bg.png
                    - loading.gif
                    - menu_arrows.png
                    - messager_icons.png
                    - pagination_icons.png
                    - panel_tools.png
                    - passwordbox_close.png
                    - passwordbox_open.png
                    - searchbox_button.png
                    - slider_handle.png
                    - spinner_arrows.png
                    - tabs_icons.png
                    - tagbox_icons.png
                    - tree_icons.png
                    - validatebox_warning.png
                - layout.css
                - linkbutton.css
                - menu.css
                - menubutton.css
                - messager.css
                - numberbox.css
                - pagination.css
                - panel.css
                - passwordbox.css
                - progressbar.css
                - propertygrid.css
                - radiobutton.css
                - searchbox.css
                - sidemenu.css
                - slider.css
                - spinner.css
                - splitbutton.css
                - switchbutton.css
                - tabs.css
                - tagbox.css
                - textbox.css
                - timepicker.css
                - tooltip.css
                - tree.css
                - validatebox.css
                - window.css
            - material-blue
                - accordion.css
                - calendar.css
                - checkbox.css
                - combo.css
                - combobox.css
                - datagrid.css
                - datalist.css
                - datebox.css
                - dialog.css
                - easyui.css
                - filebox.css
                - images
                    - accordion_arrows.png
                    - blank.gif
                    - calendar_arrows.png
                    - combo_arrow.png
                    - datagrid_icons.png
                    - datebox_arrow.png
                    - layout_arrows.png
                    - linkbutton_bg.png
                    - loading.gif
                    - menu_arrows.png
                    - menu_arrows1.png
                    - menu_arrows2.png
                    - messager_icons.png
                    - pagination_icons.png
                    - panel_tools.png
                    - passwordbox_close.png
                    - passwordbox_open.png
                    - searchbox_button.png
                    - slider_handle.png
                    - spinner_arrows.png
                    - tabs_icons.png
                    - tagbox_icons.png
                    - tree_icons.png
                    - validatebox_warning.png
                - layout.css
                - linkbutton.css
                - menu.css
                - menubutton.css
                - messager.css
                - numberbox.css
                - pagination.css
                - panel.css
                - passwordbox.css
                - progressbar.css
                - propertygrid.css
                - radiobutton.css
                - searchbox.css
                - sidemenu.css
                - slider.css
                - spinner.css
                - splitbutton.css
                - switchbutton.css
                - tabs.css
                - tagbox.css
                - textbox.css
                - timepicker.css
                - tooltip.css
                - tree.css
                - validatebox.css
                - window.css
            - material-teal
                - accordion.css
                - calendar.css
                - checkbox.css
                - combo.css
                - combobox.css
                - datagrid.css
                - datalist.css
                - datebox.css
                - dialog.css
                - easyui.css
                - filebox.css
                - images
                    - accordion_arrows.png
                    - blank.gif
                    - calendar_arrows.png
                    - combo_arrow.png
                    - datagrid_icons.png
                    - datebox_arrow.png
                    - layout_arrows.png
                    - linkbutton_bg.png
                    - loading.gif
                    - menu_arrows.png
                    - messager_icons.png
                    - pagination_icons.png
                    - panel_tools.png
                    - passwordbox_close.png
                    - passwordbox_open.png
                    - searchbox_button.png
                    - slider_handle.png
                    - spinner_arrows.png
                    - tabs_icons.png
                    - tagbox_icons.png
                    - tree_icons.png
                    - validatebox_warning.png
                - layout.css
                - linkbutton.css
                - menu.css
                - menubutton.css
                - messager.css
                - numberbox.css
                - pagination.css
                - panel.css
                - passwordbox.css
                - progressbar.css
                - propertygrid.css
                - radiobutton.css
                - searchbox.css
                - sidemenu.css
                - slider.css
                - spinner.css
                - splitbutton.css
                - switchbutton.css
                - tabs.css
                - tagbox.css
                - textbox.css
                - timepicker.css
                - tooltip.css
                - tree.css
                - validatebox.css
                - window.css
            - metro
                - accordion.css
                - calendar.css
                - checkbox.css
                - combo.css
                - combobox.css
                - datagrid.css
                - datalist.css
                - datebox.css
                - dialog.css
                - easyui.css
                - filebox.css
                - images
                    - accordion_arrows.png
                    - blank.gif
                    - calendar_arrows.png
                    - combo_arrow.png
                    - datagrid_icons.png
                    - datebox_arrow.png
                    - layout_arrows.png
                    - linkbutton_bg.png
                    - loading.gif
                    - menu_arrows.png
                    - messager_icons.png
                    - pagination_icons.png
                    - panel_tools.png
                    - passwordbox_close.png
                    - passwordbox_open.png
                    - searchbox_button.png
                    - slider_handle.png
                    - spinner_arrows.png
                    - tabs_icons.png
                    - tagbox_icons.png
                    - tree_icons.png
                    - validatebox_warning.png
                - layout.css
                - linkbutton.css
                - menu.css
                - menubutton.css
                - messager.css
                - numberbox.css
                - pagination.css
                - panel.css
                - passwordbox.css
                - progressbar.css
                - propertygrid.css
                - radiobutton.css
                - searchbox.css
                - sidemenu.css
                - slider.css
                - spinner.css
                - splitbutton.css
                - switchbutton.css
                - tabs.css
                - tagbox.css
                - textbox.css
                - timepicker.css
                - tooltip.css
                - tree.css
                - validatebox.css
                - window.css
            - mobile.css
            - react.css
            - vue.css
        - treegrid-dnd.html
        - treegrid-dnd.js
    - echarts
        - circleChart.min.js
        - echarts.common.min.js
        - echarts.min.js
        - echarts.min1.js
        - echarts.min2.js
        - jquery-1.11.0.min.js
    - font-awesome-4.7.0
        - css
            - font-awesome.css
            - font-awesome.min.css
        - fonts
            - fontawesome-webfont.eot
            - fontawesome-webfont.svg
            - fontawesome-webfont.ttf
            - fontawesome-webfont.woff
            - fontawesome-webfont.woff2
            - FontAwesome.otf
        - HELP-US-OUT.txt
        - less
            - animated.less
            - bordered-pulled.less
            - core.less
            - fixed-width.less
            - font-awesome.less
            - icons.less
            - larger.less
            - list.less
            - mixins.less
            - path.less
            - rotated-flipped.less
            - screen-reader.less
            - stacked.less
            - variables.less
        - scss
            - font-awesome.scss
            - _animated.scss
            - _bordered-pulled.scss
            - _core.scss
            - _fixed-width.scss
            - _icons.scss
            - _larger.scss
            - _list.scss
            - _mixins.scss
            - _path.scss
            - _rotated-flipped.scss
            - _screen-reader.scss
            - _stacked.scss
            - _variables.scss
    - fonts
        - fontawesome-webfont.eot
        - fontawesome-webfont.svg
        - fontawesome-webfont.ttf
        - fontawesome-webfont.woff
        - fontawesome-webfont.woff2
        - FontAwesome.otf
        - glyphicons-halflings-regular.eot
        - glyphicons-halflings-regular.svg
        - glyphicons-halflings-regular.ttf
        - glyphicons-halflings-regular.woff
        - glyphicons-halflings-regular.woff2
    - handson
        - dompurify
            - LICENSE
            - purify.js
            - purify.js.map
        - handsontable.css
        - handsontable.css.map
        - handsontable.full.css
        - handsontable.full.js
        - handsontable.full.min.css
        - handsontable.full.min.js
        - handsontable.js
        - handsontable.js.map
        - handsontable.min.css
        - handsontable.min.js
        - languages
            - all.js
            - all.min.js
            - de-CH.js
            - de-CH.min.js
            - de-DE.js
            - de-DE.min.js
            - en-US.js
            - en-US.min.js
            - es-MX.js
            - es-MX.min.js
            - fr-FR.js
            - fr-FR.min.js
            - it-IT.js
            - it-IT.min.js
            - ja-JP.js
            - ja-JP.min.js
            - ko-KR.js
            - ko-KR.min.js
            - lv-LV.js
            - lv-LV.min.js
            - nb-NO.js
            - nb-NO.min.js
            - nl-NL.js
            - nl-NL.min.js
            - pl-PL.js
            - pl-PL.min.js
            - pt-BR.js
            - pt-BR.min.js
            - ru-RU.js
            - ru-RU.min.js
            - zh-CN.js
            - zh-CN.min.js
            - zh-TW.js
            - zh-TW.min.js
        - moment
            - LICENSE
            - locale
                - af.js
                - ar-dz.js
                - ar-kw.js
                - ar-ly.js
                - ar-ma.js
                - ar-sa.js
                - ar-tn.js
                - ar.js
                - az.js
                - be.js
                - bg.js
                - bm.js
                - bn.js
                - bo.js
                - br.js
                - bs.js
                - ca.js
                - cs.js
                - cv.js
                - cy.js
                - da.js
                - de-at.js
                - de-ch.js
                - de.js
                - dv.js
                - el.js
                - en-au.js
                - en-ca.js
                - en-gb.js
                - en-ie.js
                - en-il.js
                - en-nz.js
                - en-SG.js
                - eo.js
                - es-do.js
                - es-us.js
                - es.js
                - et.js
                - eu.js
                - fa.js
                - fi.js
                - fo.js
                - fr-ca.js
                - fr-ch.js
                - fr.js
                - fy.js
                - ga.js
                - gd.js
                - gl.js
                - gom-latn.js
                - gu.js
                - he.js
                - hi.js
                - hr.js
                - hu.js
                - hy-am.js
                - id.js
                - is.js
                - it-ch.js
                - it.js
                - ja.js
                - jv.js
                - ka.js
                - kk.js
                - km.js
                - kn.js
                - ko.js
                - ku.js
                - ky.js
                - lb.js
                - lo.js
                - lt.js
                - lv.js
                - me.js
                - mi.js
                - mk.js
                - ml.js
                - mn.js
                - mr.js
                - ms-my.js
                - ms.js
                - mt.js
                - my.js
                - nb.js
                - ne.js
                - nl-be.js
                - nl.js
                - nn.js
                - pa-in.js
                - pl.js
                - pt-br.js
                - pt.js
                - ro.js
                - ru.js
                - sd.js
                - se.js
                - si.js
                - sk.js
                - sl.js
                - sq.js
                - sr-cyrl.js
                - sr.js
                - ss.js
                - sv.js
                - sw.js
                - ta.js
                - te.js
                - tet.js
                - tg.js
                - th.js
                - tl-ph.js
                - tlh.js
                - tr.js
                - tzl.js
                - tzm-latn.js
                - tzm.js
                - ug-cn.js
                - uk.js
                - ur.js
                - uz-latn.js
                - uz.js
                - vi.js
                - x-pseudo.js
                - yo.js
                - zh-cn.js
                - zh-hk.js
                - zh-tw.js
            - moment.js
        - numbro
            - languages
                - bg.min.js
                - cs-CZ.min.js
                - da-DK.min.js
                - de-AT.min.js
                - de-CH.min.js
                - de-DE.min.js
                - de-LI.min.js
                - el.min.js
                - en-AU.min.js
                - en-GB.min.js
                - en-IE.min.js
                - en-NZ.min.js
                - en-ZA.min.js
                - es-AR.min.js
                - es-CL.min.js
                - es-CO.min.js
                - es-CR.min.js
                - es-ES.min.js
                - es-MX.min.js
                - es-NI.min.js
                - es-PE.min.js
                - es-PR.min.js
                - es-SV.min.js
                - et-EE.min.js
                - fa-IR.min.js
                - fi-FI.min.js
                - fil-PH.min.js
                - fr-CA.min.js
                - fr-CH.min.js
                - fr-FR.min.js
                - he-IL.min.js
                - hu-HU.min.js
                - id.min.js
                - it-CH.min.js
                - it-IT.min.js
                - ja-JP.min.js
                - ko-KR.min.js
                - lv-LV.min.js
                - nb-NO.min.js
                - nb.min.js
                - nl-BE.min.js
                - nl-NL.min.js
                - nn.min.js
                - pl-PL.min.js
                - pt-BR.min.js
                - pt-PT.min.js
                - ro-RO.min.js
                - ro.min.js
                - ru-RU.min.js
                - ru-UA.min.js
                - sk-SK.min.js
                - sl.min.js
                - sr-Cyrl-RS.min.js
                - sv-SE.min.js
                - th-TH.min.js
                - tr-TR.min.js
                - uk-UA.min.js
                - zh-CN.min.js
                - zh-MO.min.js
                - zh-SG.min.js
                - zh-TW.min.js
            - languages.min.js
            - LICENSE
            - LICENSE-Numeraljs
            - numbro.js
        - pikaday
            - LICENSE
            - pikaday.css
            - pikaday.js
        - README.md
    - handson-12.1.3
        - dompurify
            - LICENSE
            - purify.js
            - purify.js.map
        - handsontable.css
        - handsontable.css.map
        - handsontable.full.css
        - handsontable.full.js
        - handsontable.full.min.css
        - handsontable.full.min.js
        - handsontable.js
        - handsontable.js.map
        - handsontable.min.css
        - handsontable.min.js
        - languages
            - all.js
            - all.min.js
            - ar-AR.js
            - ar-AR.min.js
            - cs-CZ.js
            - cs-CZ.min.js
            - de-CH.js
            - de-CH.min.js
            - de-DE.js
            - de-DE.min.js
            - en-US.js
            - en-US.min.js
            - es-MX.js
            - es-MX.min.js
            - fr-FR.js
            - fr-FR.min.js
            - it-IT.js
            - it-IT.min.js
            - ja-JP.js
            - ja-JP.min.js
            - ko-KR.js
            - ko-KR.min.js
            - lv-LV.js
            - lv-LV.min.js
            - nb-NO.js
            - nb-NO.min.js
            - nl-NL.js
            - nl-NL.min.js
            - pl-PL.js
            - pl-PL.min.js
            - pt-BR.js
            - pt-BR.min.js
            - ru-RU.js
            - ru-RU.min.js
            - sr-SP.js
            - sr-SP.min.js
            - zh-CN.js
            - zh-CN.min.js
            - zh-TW.js
            - zh-TW.min.js
        - moment
            - LICENSE
            - locale
                - af.js
                - ar-dz.js
                - ar-kw.js
                - ar-ly.js
                - ar-ma.js
                - ar-sa.js
                - ar-tn.js
                - ar.js
                - az.js
                - be.js
                - bg.js
                - bm.js
                - bn-bd.js
                - bn.js
                - bo.js
                - br.js
                - bs.js
                - ca.js
                - cs.js
                - cv.js
                - cy.js
                - da.js
                - de-at.js
                - de-ch.js
                - de.js
                - dv.js
                - el.js
                - en-au.js
                - en-ca.js
                - en-gb.js
                - en-ie.js
                - en-il.js
                - en-in.js
                - en-nz.js
                - en-SG.js
                - eo.js
                - es-do.js
                - es-mx.js
                - es-us.js
                - es.js
                - et.js
                - eu.js
                - fa.js
                - fi.js
                - fil.js
                - fo.js
                - fr-ca.js
                - fr-ch.js
                - fr.js
                - fy.js
                - ga.js
                - gd.js
                - gl.js
                - gom-deva.js
                - gom-latn.js
                - gu.js
                - he.js
                - hi.js
                - hr.js
                - hu.js
                - hy-am.js
                - id.js
                - is.js
                - it-ch.js
                - it.js
                - ja.js
                - jv.js
                - ka.js
                - kk.js
                - km.js
                - kn.js
                - ko.js
                - ku.js
                - ky.js
                - lb.js
                - lo.js
                - lt.js
                - lv.js
                - me.js
                - mi.js
                - mk.js
                - ml.js
                - mn.js
                - mr.js
                - ms-my.js
                - ms.js
                - mt.js
                - my.js
                - nb.js
                - ne.js
                - nl-be.js
                - nl.js
                - nn.js
                - oc-lnc.js
                - pa-in.js
                - pl.js
                - pt-br.js
                - pt.js
                - ro.js
                - ru.js
                - sd.js
                - se.js
                - si.js
                - sk.js
                - sl.js
                - sq.js
                - sr-cyrl.js
                - sr.js
                - ss.js
                - sv.js
                - sw.js
                - ta.js
                - te.js
                - tet.js
                - tg.js
                - th.js
                - tk.js
                - tl-ph.js
                - tlh.js
                - tr.js
                - tzl.js
                - tzm-latn.js
                - tzm.js
                - ug-cn.js
                - uk.js
                - ur.js
                - uz-latn.js
                - uz.js
                - vi.js
                - x-pseudo.js
                - yo.js
                - zh-cn.js
                - zh-hk.js
                - zh-mo.js
                - zh-tw.js
            - moment.js
        - numbro
            - languages
                - bg.min.js
                - cs-CZ.min.js
                - da-DK.min.js
                - de-AT.min.js
                - de-CH.min.js
                - de-DE.min.js
                - de-LI.min.js
                - el.min.js
                - en-AU.min.js
                - en-GB.min.js
                - en-IE.min.js
                - en-NZ.min.js
                - en-ZA.min.js
                - es-AR.min.js
                - es-CL.min.js
                - es-CO.min.js
                - es-CR.min.js
                - es-ES.min.js
                - es-MX.min.js
                - es-NI.min.js
                - es-PE.min.js
                - es-PR.min.js
                - es-SV.min.js
                - et-EE.min.js
                - fa-IR.min.js
                - fi-FI.min.js
                - fil-PH.min.js
                - fr-CA.min.js
                - fr-CH.min.js
                - fr-FR.min.js
                - he-IL.min.js
                - hu-HU.min.js
                - id.min.js
                - it-CH.min.js
                - it-IT.min.js
                - ja-JP.min.js
                - ko-KR.min.js
                - lv-LV.min.js
                - nb-NO.min.js
                - nb.min.js
                - nl-BE.min.js
                - nl-NL.min.js
                - nn.min.js
                - pl-PL.min.js
                - pt-BR.min.js
                - pt-PT.min.js
                - ro-RO.min.js
                - ro.min.js
                - ru-RU.min.js
                - ru-UA.min.js
                - sk-SK.min.js
                - sl.min.js
                - sr-Cyrl-RS.min.js
                - sv-SE.min.js
                - th-TH.min.js
                - tr-TR.min.js
                - uk-UA.min.js
                - zh-CN.min.js
                - zh-MO.min.js
                - zh-SG.min.js
                - zh-TW.min.js
            - languages.min.js
            - LICENSE
            - LICENSE-Numeraljs
            - numbro.js
        - pikaday
            - LICENSE
            - pikaday.css
            - pikaday.js
        - README.md
    - handson-13.x
        - dompurify
            - LICENSE
            - purify.js
            - purify.js.map
        - handsontable.css
        - handsontable.css.map
        - handsontable.full.css
        - handsontable.full.js
        - handsontable.full.min.css
        - handsontable.full.min.js
        - handsontable.js
        - handsontable.js.map
        - handsontable.min.css
        - handsontable.min.js
        - languages
            - all.js
            - all.min.js
            - ar-AR.js
            - ar-AR.min.js
            - cs-CZ.js
            - cs-CZ.min.js
            - de-CH.js
            - de-CH.min.js
            - de-DE.js
            - de-DE.min.js
            - en-US.js
            - en-US.min.js
            - es-MX.js
            - es-MX.min.js
            - fr-FR.js
            - fr-FR.min.js
            - it-IT.js
            - it-IT.min.js
            - ja-JP.js
            - ja-JP.min.js
            - ko-KR.js
            - ko-KR.min.js
            - lv-LV.js
            - lv-LV.min.js
            - nb-NO.js
            - nb-NO.min.js
            - nl-NL.js
            - nl-NL.min.js
            - pl-PL.js
            - pl-PL.min.js
            - pt-BR.js
            - pt-BR.min.js
            - ru-RU.js
            - ru-RU.min.js
            - sr-SP.js
            - sr-SP.min.js
            - zh-CN.js
            - zh-CN.min.js
            - zh-TW.js
            - zh-TW.min.js
        - moment
            - LICENSE
            - locale
                - af.js
                - ar-dz.js
                - ar-kw.js
                - ar-ly.js
                - ar-ma.js
                - ar-sa.js
                - ar-tn.js
                - ar.js
                - az.js
                - be.js
                - bg.js
                - bm.js
                - bn-bd.js
                - bn.js
                - bo.js
                - br.js
                - bs.js
                - ca.js
                - cs.js
                - cv.js
                - cy.js
                - da.js
                - de-at.js
                - de-ch.js
                - de.js
                - dv.js
                - el.js
                - en-au.js
                - en-ca.js
                - en-gb.js
                - en-ie.js
                - en-il.js
                - en-in.js
                - en-nz.js
                - en-SG.js
                - eo.js
                - es-do.js
                - es-mx.js
                - es-us.js
                - es.js
                - et.js
                - eu.js
                - fa.js
                - fi.js
                - fil.js
                - fo.js
                - fr-ca.js
                - fr-ch.js
                - fr.js
                - fy.js
                - ga.js
                - gd.js
                - gl.js
                - gom-deva.js
                - gom-latn.js
                - gu.js
                - he.js
                - hi.js
                - hr.js
                - hu.js
                - hy-am.js
                - id.js
                - is.js
                - it-ch.js
                - it.js
                - ja.js
                - jv.js
                - ka.js
                - kk.js
                - km.js
                - kn.js
                - ko.js
                - ku.js
                - ky.js
                - lb.js
                - lo.js
                - lt.js
                - lv.js
                - me.js
                - mi.js
                - mk.js
                - ml.js
                - mn.js
                - mr.js
                - ms-my.js
                - ms.js
                - mt.js
                - my.js
                - nb.js
                - ne.js
                - nl-be.js
                - nl.js
                - nn.js
                - oc-lnc.js
                - pa-in.js
                - pl.js
                - pt-br.js
                - pt.js
                - ro.js
                - ru.js
                - sd.js
                - se.js
                - si.js
                - sk.js
                - sl.js
                - sq.js
                - sr-cyrl.js
                - sr.js
                - ss.js
                - sv.js
                - sw.js
                - ta.js
                - te.js
                - tet.js
                - tg.js
                - th.js
                - tk.js
                - tl-ph.js
                - tlh.js
                - tr.js
                - tzl.js
                - tzm-latn.js
                - tzm.js
                - ug-cn.js
                - uk.js
                - ur.js
                - uz-latn.js
                - uz.js
                - vi.js
                - x-pseudo.js
                - yo.js
                - zh-cn.js
                - zh-hk.js
                - zh-mo.js
                - zh-tw.js
            - moment.js
        - numbro
            - languages
                - bg.min.js
                - cs-CZ.min.js
                - da-DK.min.js
                - de-AT.min.js
                - de-CH.min.js
                - de-DE.min.js
                - de-LI.min.js
                - el.min.js
                - en-AU.min.js
                - en-GB.min.js
                - en-IE.min.js
                - en-NZ.min.js
                - en-ZA.min.js
                - es-AR.min.js
                - es-CL.min.js
                - es-CO.min.js
                - es-CR.min.js
                - es-ES.min.js
                - es-MX.min.js
                - es-NI.min.js
                - es-PE.min.js
                - es-PR.min.js
                - es-SV.min.js
                - et-EE.min.js
                - fa-IR.min.js
                - fi-FI.min.js
                - fil-PH.min.js
                - fr-CA.min.js
                - fr-CH.min.js
                - fr-FR.min.js
                - he-IL.min.js
                - hu-HU.min.js
                - id.min.js
                - it-CH.min.js
                - it-IT.min.js
                - ja-JP.min.js
                - ko-KR.min.js
                - lv-LV.min.js
                - nb-NO.min.js
                - nb.min.js
                - nl-BE.min.js
                - nl-NL.min.js
                - nn.min.js
                - pl-PL.min.js
                - pt-BR.min.js
                - pt-PT.min.js
                - ro-RO.min.js
                - ro.min.js
                - ru-RU.min.js
                - ru-UA.min.js
                - sk-SK.min.js
                - sl.min.js
                - sr-Cyrl-RS.min.js
                - sv-SE.min.js
                - th-TH.min.js
                - tr-TR.min.js
                - uk-UA.min.js
                - zh-CN.min.js
                - zh-MO.min.js
                - zh-SG.min.js
                - zh-TW.min.js
            - languages.min.js
            - LICENSE
            - LICENSE-Numeraljs
            - numbro.js
        - pikaday
            - LICENSE
            - pikaday.css
            - pikaday.js
        - README.md
    - html5
        - html5.min.js
        - respond.min.js
    - icon
        - down.png
        - retrun.png
        - site_b.png
        - site_blue.png
        - site_g.png
        - site_r.png
        - site_y.png
        - site_z.png
        - star_blue.png
        - star_blue2.png
        - up.png
    - img
        - locked.png
        - login-background.jpg
        - user.png
    - index
        - Chart.min.js
        - jquery-1.9.1.min.js
        - jquery-3.3.1.min.js
        - jquery.easypiechart.min.js
        - jquery.fileDownload.js
        - jquery.form.js
        - jquery.min.js
    - InsdepUI
        - expand
            - columns-ext
                - columns-ext.html
                - columns-ext.js
                - group.html
                - __MACOSX
                    - ._columns-ext.js
            - datagrid-cellediting
                - datagrid-cellediting.html
                - datagrid-cellediting.js
            - datagrid-dnd
                - datagrid-dnd.html
                - datagrid-dnd.js
            - datagrid-filter
                - datagrid-filter.html
                - datagrid-filter.js
                - filter.png
            - jquery-easyui-checkbox
                - checkbox.css
                - checkbox.js
                - images
                    - checkbox.png
                    - disable.png
            - jquery-easyui-datagridview
                - datagrid-bufferview.html
                - datagrid-bufferview.js
                - datagrid-defaultview.js
                - datagrid-detailview.html
                - datagrid-detailview.js
                - datagrid-detailview2.html
                - datagrid-groupview.html
                - datagrid-groupview.js
                - datagrid-scrollview.html
                - datagrid-scrollview.js
                - datagrid-scrollview2.html
                - datagrid_data.json
                - images
                    - EST-1.png
                    - EST-10.png
                    - EST-2.png
                    - EST-3.png
                    - EST-4.png
                    - EST-5.png
                    - EST-6.png
                    - EST-7.png
                    - EST-8.png
                    - EST-9.png
            - jquery-easyui-dwrloader
                - dwrloader.html
                - dwrloader.js
            - jquery-easyui-edatagrid
                - datagrid_data.json
                - editable-datagrid.html
                - jquery.edatagrid.js
            - jquery-easyui-etree
                - editable-tree.html
                - jquery.etree.js
                - jquery.etree.lang.js
                - tree_data.json
            - jquery-easyui-pivotgrid
                - jquery.pivotgrid.js
                - layout.png
                - load.png
                - pivotgrid.html
                - pivotgrid_data1.json
                - pivotgrid_data2.json
                - __MACOSX
                    - ._layout.png
                    - ._load.png
            - jquery-easyui-portal
                - datagrid_data.json
                - jquery.portal.js
                - portal.html
            - jquery-easyui-radiobox
                - images
                    - disable.png
                    - radiobox.png
                - radiobox.css
                - radiobox.js
            - jquery-easyui-ribbon
                - images
                    - 16
                        - align-center.png
                        - align-justify.png
                        - align-left.png
                        - align-right.png
                        - bold.png
                        - bullets.png
                        - case-font.png
                        - copy.png
                        - decrease-font.png
                        - find.png
                        - format.png
                        - go.png
                        - grow-font.png
                        - increase-font.png
                        - italic.png
                        - numbers.png
                        - paste.png
                        - replace.png
                        - select.png
                        - selectall.png
                        - shrink-font.png
                        - strikethrough.png
                        - subscript.png
                        - superscript.png
                        - underline.png
                    - 32
                        - chart.png
                        - clipart.png
                        - paste.png
                        - picture.png
                        - shapes.png
                        - smartart.png
                        - table.png
                - jquery.ribbon.js
                - ribbon-icon.css
                - ribbon.css
                - ribbon.html
            - jquery-easyui-rtl
                - content.html
                - datagrid_data1.json
                - easyui-rtl.css
                - easyui-rtl.js
                - rtl.html
                - tree_data1.json
            - jquery-easyui-texteditor
                - images
                    - backcolor.png
                    - bold.png
                    - forecolor.png
                    - indent.png
                    - insertorderedlist.png
                    - insertunorderedlist.png
                    - italic.png
                    - justifycenter.png
                    - justifyfull.png
                    - justifyleft.png
                    - justifyright.png
                    - outdent.png
                    - strikethrough.png
                    - underline.png
                - jquery.texteditor.js
                - texteditor.css
                - texteditor.html
                - __MACOSX
                    - images
                        - ._.DS_Store
                        - ._backcolor.png
                        - ._forecolor.png
                        - ._indent.png
                        - ._outdent.png
            - treegrid-dnd
                - treegrid-dnd.html
                - treegrid-dnd.js
        - icon.css
        - iconfont
            - demo.css
            - demo_fontclass.html
            - demo_symbol.html
            - demo_unicode.html
            - iconfont.css
            - iconfont.eot
            - iconfont.js
            - iconfont.svg
            - iconfont.ttf
            - iconfont.woff
        - icons
            - arrow_refresh.png
            - arrow_rotate_anticlockwise.png
            - arrow_rotate_clockwise.png
            - arrow_switch.png
            - back.png
            - blank.gif
            - cancel.png
            - clear.png
            - cog.png
            - cut.png
            - download.png
            - edit_add.png
            - edit_remove.png
            - filesave.png
            - filter.png
            - folder_search.png
            - help.png
            - information.png
            - large_chart.png
            - large_clipart.png
            - large_picture.png
            - large_shapes.png
            - large_smartart.png
            - lock.png
            - lock_go.png
            - lock_password.png
            - man.png
            - mini-append.png
            - mini-cancel.png
            - mini-delete.png
            - mini-ok.png
            - mini-pencil.png
            - mini-reload.png
            - mini_add.png
            - mini_cross.png
            - mini_delete.png
            - mini_edit.png
            - mini_minus.png
            - mini_refresh.png
            - mini_set.png
            - mini_tick.png
            - more.png
            - myadd.png
            - myConfig.png
            - mydelete.png
            - myedit.png
            - myview.png
            - my_print_qrcode.png
            - no.png
            - ok.png
            - pencil.png
            - print.png
            - recover_deleted_items.png
            - redo.png
            - reload.png
            - search.png
            - search1.png
            - set.png
            - sort_number.png
            - sum.png
            - tip.png
            - undo.png
            - update.png
            - user_config.png
            - wrench.png
            - wrench_orange.png
        - images
            - about.png
            - accordion_arrows.png
            - alert_icons.png
            - append-images.png
            - arrow-right.png
            - blank.gif
            - calendar_arrows.png
            - combo_arrow.png
            - datagrid-filter.png
            - datagrid_icons.png
            - datebox_arrow.png
            - disable.png
            - footer_projection.png
            - icons_error.png
            - icons_help.png
            - icons_info.png
            - icons_prompt.png
            - icons_success.png
            - icons_warning.png
            - info-icons-at.png
            - info-icons-tel.png
            - layout_arrows.png
            - linkbutton_bg.png
            - list-loding.gif
            - loading.gif
            - logo.png
            - logo_110.png
            - menu_arrows.png
            - menu_arrows_blue.png
            - menu_arrows_white.png
            - messager_icons.png
            - navigate_more.png
            - pagination_icons.png
            - panel_tools - 副本.png
            - panel_tools.png
            - panel_tools_white.png
            - passwordbox_close.png
            - passwordbox_open.png
            - portrait129x129.png
            - portrait172x172-select.png
            - portrait172x172.png
            - portrait20x20.png
            - portrait250x250.png
            - portrait28x28.png
            - portrait32x32.png
            - portrait38x38.png
            - portrait43x43.png
            - portrait86x86.png
            - progressbar-background.png
            - QRcode-close.png
            - QRcode-demo.png
            - QRcode.png
            - searchbox_button.png
            - searchbox_navigate_button.png
            - set.png
            - slider_handle.png
            - spinner_arrows.png
            - tabs_icons.png
            - tabs_icons_hover.png
            - tabs_icons_white.png
            - tagbox_icons.png
            - tagbox_icons_white.png
            - tree_icons.png
            - tree_icons_2.png
            - validatebox_warning.png
        - insdep.easyui.min.css
        - insdep.extend.min.js
        - insdep.theme_default.css
        - jquery.easyui.min.js
        - jquery.min.js
        - locale
            - easyui-lang-af.js
            - easyui-lang-am.js
            - easyui-lang-ar.js
            - easyui-lang-bg.js
            - easyui-lang-ca.js
            - easyui-lang-cs.js
            - easyui-lang-cz.js
            - easyui-lang-da.js
            - easyui-lang-de.js
            - easyui-lang-el.js
            - easyui-lang-en.js
            - easyui-lang-es.js
            - easyui-lang-fr.js
            - easyui-lang-it.js
            - easyui-lang-jp.js
            - easyui-lang-ko.js
            - easyui-lang-nl.js
            - easyui-lang-pl.js
            - easyui-lang-pt_BR.js
            - easyui-lang-ru.js
            - easyui-lang-sv_SE.js
            - easyui-lang-tr.js
            - easyui-lang-zh_CN.js
            - easyui-lang-zh_TW.js
        - plugin
            - colpick-2.0.2
                - colpick.jquery.json
                - css
                    - colpick.css
                - js
                    - colpick.js
                - LICENSE
                - README.md
            - cropper-2.3.4
                - .csscomb.json
                - .csslintrc
                - .editorconfig
                - .gitattributes
                - .gitignore
                - .htmlcombrc
                - .jscsrc
                - .jshintrc
                - .npmignore
                - .travis.yml
                - assets
                    - css
                        - bootstrap.min.css
                        - font-awesome.min.css
                        - qunit.css
                    - fonts
                        - fontawesome-webfont.eot
                        - fontawesome-webfont.svg
                        - fontawesome-webfont.ttf
                        - fontawesome-webfont.woff
                        - fontawesome-webfont.woff2
                        - FontAwesome.otf
                        - glyphicons-halflings-regular.eot
                        - glyphicons-halflings-regular.svg
                        - glyphicons-halflings-regular.ttf
                        - glyphicons-halflings-regular.woff
                        - glyphicons-halflings-regular.woff2
                    - img
                        - data.jpg
                        - layers.jpg
                        - picture-2.jpg
                        - picture-3.jpg
                        - picture.jpg
                    - js
                        - bootstrap.min.js
                        - jquery.min.js
                        - qunit.js
                - bower.json
                - CHANGELOG.md
                - CONTRIBUTING.md
                - demo
                    - css
                        - main.css
                    - index.html
                    - js
                        - main.js
                - dist
                    - cropper.css
                    - cropper.js
                    - cropper.min.css
                    - cropper.min.js
                - docs
                    - apple-touch-icon.png
                    - css
                        - main.css
                    - favicon.ico
                    - index.html
                    - js
                        - main.js
                    - LICENSE
                - examples
                    - a-range-of-aspect-ratio.html
                    - crop-a-round-image.html
                    - crop-avatar
                        - crop.php
                        - css
                            - main.css
                        - img
                            - 20161211210545.original.jpeg
                            - 20161211210545.png
                            - 20161211210555.original.jpeg
                            - 20161211210555.png
                            - 20161211210606.original.jpeg
                            - 20161211210606.png
                            - loading.gif
                            - picture.jpg
                        - index.html
                        - js
                            - main.js
                        - README.md
                    - crop-on-canvas.html
                    - cropper-in-modal.html
                    - customize-preview.html
                    - fixed-crop-box.html
                    - full-crop-box.html
                    - multiple-croppers.html
                    - responsive-container.html
                - gulpfile.js
                - ISSUE_TEMPLATE.md
                - LICENSE
                - package.json
                - README.md
                - src
                    - img
                        - bg.png
                    - js
                        - bind.js
                        - build.js
                        - change.js
                        - cropper.js
                        - defaults.js
                        - handlers.js
                        - init.js
                        - intro.js
                        - methods.js
                        - outro.js
                        - plugin.js
                        - preview.js
                        - render.js
                        - template.js
                        - utilities.js
                        - variables.js
                    - scss
                        - cropper.scss
                        - _main.scss
                        - _mixins.scss
                        - _utilities.scss
                        - _variables.scss
                - test
                    - css
                        - main.css
                    - events
                        - build.js
                        - built.js
                        - crop.js
                        - cropend.js
                        - cropmove.js
                        - cropstart.js
                        - zoom.js
                    - events.html
                    - js
                        - main.js
                    - methods
                        - clear.js
                        - crop.js
                        - destroy.js
                        - disable.js
                        - enable.js
                        - getCanvasData.js
                        - getContainerData.js
                        - getCropBoxData.js
                        - getCroppedCanvas.js
                        - getData.js
                        - getImageData.js
                        - move.js
                        - moveTo.js
                        - replace.js
                        - reset.js
                        - rotate.js
                        - rotateTo.js
                        - scale.js
                        - scaleX.js
                        - scaleY.js
                        - setAspectRatio.js
                        - setCanvasData.js
                        - setCropBoxData.js
                        - setData.js
                        - setDragMode.js
                        - zoom.js
                        - zoomTo.js
                    - methods.html
                    - options
                        - aspectRatio.js
                        - autoCrop.js
                        - background.js
                        - center.js
                        - checkCrossOrigin.js
                        - checkOrientation.js
                        - cropBoxMovable.js
                        - cropBoxResizable.js
                        - data.js
                        - dragMode.js
                        - guides.js
                        - highlight.js
                        - minCanvasHeight.js
                        - minCanvasWidth.js
                        - minContainerHeight.js
                        - minContainerWidth.js
                        - minCropBoxHeight.js
                        - minCropBoxWidth.js
                        - modal.js
                        - movable.js
                        - rotatable.js
                        - scalable.js
                        - toggleDragModeOnDblclick.js
                        - viewMode.js
                        - zoomable.js
                        - zoomOnTouch.js
                        - zoomOnWheel.js
                    - options.html
            - font-awesome-4.7.0
                - css
                    - font-awesome.css
                    - font-awesome.min.css
                - fonts
                    - fontawesome-webfont.eot
                    - fontawesome-webfont.svg
                    - fontawesome-webfont.ttf
                    - fontawesome-webfont.woff
                    - fontawesome-webfont.woff2
                    - FontAwesome.otf
                - HELP-US-OUT.txt
                - less
                    - animated.less
                    - bordered-pulled.less
                    - core.less
                    - fixed-width.less
                    - font-awesome.less
                    - icons.less
                    - larger.less
                    - list.less
                    - mixins.less
                    - path.less
                    - rotated-flipped.less
                    - screen-reader.less
                    - stacked.less
                    - variables.less
                - scss
                    - font-awesome.scss
                    - _animated.scss
                    - _bordered-pulled.scss
                    - _core.scss
                    - _fixed-width.scss
                    - _icons.scss
                    - _larger.scss
                    - _list.scss
                    - _mixins.scss
                    - _path.scss
                    - _rotated-flipped.scss
                    - _screen-reader.scss
                    - _stacked.scss
                    - _variables.scss
            - Highcharts-5.0.0
                - examples
                    - 3d-column-interactive
                        - index.htm
                    - 3d-column-null-values
                        - index.htm
                    - 3d-column-stacking-grouping
                        - index.htm
                    - 3d-pie
                        - index.htm
                    - 3d-pie-donut
                        - index.htm
                    - 3d-scatter-draggable
                        - index.htm
                    - area-basic
                        - index.htm
                    - area-inverted
                        - index.htm
                    - area-missing
                        - index.htm
                    - area-negative
                        - index.htm
                    - area-stacked
                        - index.htm
                    - area-stacked-percent
                        - index.htm
                    - arearange
                        - index.htm
                    - arearange-line
                        - index.htm
                    - areaspline
                        - index.htm
                    - bar-basic
                        - index.htm
                    - bar-negative-stack
                        - index.htm
                    - bar-stacked
                        - index.htm
                    - box-plot
                        - index.htm
                    - bubble
                        - index.htm
                    - bubble-3d
                        - index.htm
                    - column-basic
                        - index.htm
                    - column-drilldown
                        - index.htm
                    - column-negative
                        - index.htm
                    - column-parsed
                        - index.htm
                    - column-placement
                        - index.htm
                    - column-rotated-labels
                        - index.htm
                    - column-stacked
                        - index.htm
                    - column-stacked-and-grouped
                        - index.htm
                    - column-stacked-percent
                        - index.htm
                    - columnrange
                        - index.htm
                    - combo
                        - index.htm
                    - combo-dual-axes
                        - index.htm
                    - combo-histogram
                        - index.htm
                    - combo-meteogram
                        - index.htm
                    - combo-multi-axes
                        - index.htm
                    - combo-regression
                        - index.htm
                    - combo-timeline
                        - index.htm
                    - dynamic-click-to-add
                        - index.htm
                    - dynamic-master-detail
                        - index.htm
                    - dynamic-update
                        - index.htm
                    - error-bar
                        - index.htm
                    - funnel
                        - index.htm
                    - gauge-activity
                        - index.htm
                    - gauge-clock
                        - index.htm
                    - gauge-dual
                        - index.htm
                    - gauge-solid
                        - index.htm
                    - gauge-speedometer
                        - index.htm
                    - gauge-vu-meter
                        - index.htm
                    - heatmap
                        - index.htm
                    - heatmap-canvas
                        - index.htm
                    - line-ajax
                        - index.htm
                    - line-basic
                        - index.htm
                    - line-labels
                        - index.htm
                    - line-log-axis
                        - index.htm
                    - line-time-series
                        - index.htm
                    - pie-basic
                        - index.htm
                    - pie-donut
                        - index.htm
                    - pie-drilldown
                        - index.htm
                    - pie-gradient
                        - index.htm
                    - pie-legend
                        - index.htm
                    - pie-monochrome
                        - index.htm
                    - pie-semi-circle
                        - index.htm
                    - polar
                        - index.htm
                    - polar-spider
                        - index.htm
                    - polar-wind-rose
                        - index.htm
                    - polygon
                        - index.htm
                    - pyramid
                        - index.htm
                    - renderer
                        - index.htm
                    - scatter
                        - index.htm
                    - sparkline
                        - index.htm
                    - spline-inverted
                        - index.htm
                    - spline-irregular-time
                        - index.htm
                    - spline-plot-bands
                        - index.htm
                    - spline-symbols
                        - index.htm
                    - synchronized-charts
                        - index.htm
                    - treemap-coloraxis
                        - index.htm
                    - treemap-large-dataset
                        - index.htm
                    - treemap-with-levels
                        - index.htm
                    - waterfall
                        - index.htm
                - gfx
                    - vml-radial-gradient.png
                - graphics
                    - highslide
                        - close.png
                        - closeX.png
                        - outlines
                            - rounded-white.png
                        - resize.gif
                        - zoomout.cur
                    - meteogram-symbols-30px.png
                    - search.png
                    - skies.jpg
                    - snow.png
                    - sun.png
                - index.htm
                - js
                    - css
                        - highcharts.css
                    - highcharts-3d.js
                    - highcharts-3d.src.js
                    - highcharts-more.js
                    - highcharts-more.src.js
                    - highcharts.css
                    - highcharts.js
                    - highcharts.src.js
                    - js
                        - highcharts-3d.js
                        - highcharts-3d.src.js
                        - highcharts-more.js
                        - highcharts-more.src.js
                        - highcharts.js
                        - highcharts.src.js
                        - modules
                            - accessibility.js
                            - accessibility.src.js
                            - annotations.js
                            - annotations.src.js
                            - boost.js
                            - boost.src.js
                            - broken-axis.js
                            - broken-axis.src.js
                            - data.js
                            - data.src.js
                            - drilldown.js
                            - drilldown.src.js
                            - exporting.js
                            - exporting.src.js
                            - funnel.js
                            - funnel.src.js
                            - heatmap.js
                            - heatmap.src.js
                            - no-data-to-display.js
                            - no-data-to-display.src.js
                            - offline-exporting.js
                            - offline-exporting.src.js
                            - overlapping-datalabels.js
                            - overlapping-datalabels.src.js
                            - series-label.js
                            - series-label.src.js
                            - solid-gauge.js
                            - solid-gauge.src.js
                            - treemap.js
                            - treemap.src.js
                        - themes
                            - dark-blue.js
                            - dark-green.js
                            - dark-unica.js
                            - gray.js
                            - grid-light.js
                            - grid.js
                            - sand-signika.js
                            - skies.js
                    - lib
                        - canvg.js
                        - canvg.src.js
                        - rgbcolor.js
                        - rgbcolor.src.js
                    - modules
                        - accessibility.js
                        - accessibility.src.js
                        - annotations.js
                        - annotations.src.js
                        - boost.js
                        - boost.src.js
                        - broken-axis.js
                        - broken-axis.src.js
                        - data.js
                        - data.src.js
                        - drilldown.js
                        - drilldown.src.js
                        - exporting.js
                        - exporting.src.js
                        - funnel.js
                        - funnel.src.js
                        - heatmap.js
                        - heatmap.src.js
                        - no-data-to-display.js
                        - no-data-to-display.src.js
                        - offline-exporting.js
                        - offline-exporting.src.js
                        - overlapping-datalabels.js
                        - overlapping-datalabels.src.js
                        - series-label.js
                        - series-label.src.js
                        - solid-gauge.js
                        - solid-gauge.src.js
                        - treemap.js
                        - treemap.src.js
                    - themes
                        - dark-blue.js
                        - dark-green.js
                        - dark-unica.js
                        - gray.js
                        - grid-light.js
                        - grid.js
                        - sand-signika.js
                        - skies.js
                - readme.txt
            - highlight-9.9.0
                - .editorconfig
                - .gitattributes
                - .gitignore
                - .travis.yml
                - AUTHORS.en.txt
                - AUTHORS.ru.txt
                - CHANGES.md
                - demo
                    - demo.js
                    - index.html
                    - jquery-2.1.1.min.js
                    - perfect-scrollbar.min.css
                    - perfect-scrollbar.min.js
                    - style.css
                - docs
                    - api.rst
                    - building-testing.rst
                    - conf.py
                    - css-classes-reference.rst
                    - index.rst
                    - language-contribution.rst
                    - language-guide.rst
                    - language-requests.rst
                    - line-numbers.rst
                    - Makefile
                    - reference.rst
                    - release-process.rst
                    - style-guide.rst
                    - _static
                        - .gitkeep
                    - _templates
                        - .gitkeep
                - highlight.min.js
                - LICENSE
                - package.json
                - README.md
                - README.ru.md
                - src
                    - highlight.js
                    - languages
                        - 1c.js
                        - abnf.js
                        - accesslog.js
                        - actionscript.js
                        - ada.js
                        - apache.js
                        - applescript.js
                        - arduino.js
                        - armasm.js
                        - asciidoc.js
                        - aspectj.js
                        - autohotkey.js
                        - autoit.js
                        - avrasm.js
                        - awk.js
                        - axapta.js
                        - bash.js
                        - basic.js
                        - bnf.js
                        - brainfuck.js
                        - cal.js
                        - capnproto.js
                        - ceylon.js
                        - clean.js
                        - clojure-repl.js
                        - clojure.js
                        - cmake.js
                        - coffeescript.js
                        - coq.js
                        - cos.js
                        - cpp.js
                        - crmsh.js
                        - crystal.js
                        - cs.js
                        - csp.js
                        - css.js
                        - d.js
                        - dart.js
                        - delphi.js
                        - diff.js
                        - django.js
                        - dns.js
                        - dockerfile.js
                        - dos.js
                        - dsconfig.js
                        - dts.js
                        - dust.js
                        - ebnf.js
                        - elixir.js
                        - elm.js
                        - erb.js
                        - erlang-repl.js
                        - erlang.js
                        - excel.js
                        - fix.js
                        - flix.js
                        - fortran.js
                        - fsharp.js
                        - gams.js
                        - gauss.js
                        - gcode.js
                        - gherkin.js
                        - glsl.js
                        - go.js
                        - golo.js
                        - gradle.js
                        - groovy.js
                        - haml.js
                        - handlebars.js
                        - haskell.js
                        - haxe.js
                        - hsp.js
                        - htmlbars.js
                        - http.js
                        - hy.js
                        - inform7.js
                        - ini.js
                        - irpf90.js
                        - java.js
                        - javascript.js
                        - json.js
                        - julia.js
                        - kotlin.js
                        - lasso.js
                        - ldif.js
                        - leaf.js
                        - less.js
                        - lisp.js
                        - livecodeserver.js
                        - livescript.js
                        - llvm.js
                        - lsl.js
                        - lua.js
                        - makefile.js
                        - markdown.js
                        - mathematica.js
                        - matlab.js
                        - maxima.js
                        - mel.js
                        - mercury.js
                        - mipsasm.js
                        - mizar.js
                        - mojolicious.js
                        - monkey.js
                        - moonscript.js
                        - nginx.js
                        - nimrod.js
                        - nix.js
                        - nsis.js
                        - objectivec.js
                        - ocaml.js
                        - openscad.js
                        - oxygene.js
                        - parser3.js
                        - perl.js
                        - pf.js
                        - php.js
                        - pony.js
                        - powershell.js
                        - processing.js
                        - profile.js
                        - prolog.js
                        - protobuf.js
                        - puppet.js
                        - purebasic.js
                        - python.js
                        - q.js
                        - qml.js
                        - r.js
                        - rib.js
                        - roboconf.js
                        - rsl.js
                        - ruby.js
                        - ruleslanguage.js
                        - rust.js
                        - scala.js
                        - scheme.js
                        - scilab.js
                        - scss.js
                        - smali.js
                        - smalltalk.js
                        - sml.js
                        - sqf.js
                        - sql.js
                        - stan.js
                        - stata.js
                        - step21.js
                        - stylus.js
                        - subunit.js
                        - swift.js
                        - taggerscript.js
                        - tap.js
                        - tcl.js
                        - tex.js
                        - thrift.js
                        - tp.js
                        - twig.js
                        - typescript.js
                        - vala.js
                        - vbnet.js
                        - vbscript-html.js
                        - vbscript.js
                        - verilog.js
                        - vhdl.js
                        - vim.js
                        - x86asm.js
                        - xl.js
                        - xml.js
                        - xquery.js
                        - yaml.js
                        - zephir.js
                    - styles
                        - agate.css
                        - androidstudio.css
                        - arduino-light.css
                        - arta.css
                        - ascetic.css
                        - atelier-cave-dark.css
                        - atelier-cave-light.css
                        - atelier-dune-dark.css
                        - atelier-dune-light.css
                        - atelier-estuary-dark.css
                        - atelier-estuary-light.css
                        - atelier-forest-dark.css
                        - atelier-forest-light.css
                        - atelier-heath-dark.css
                        - atelier-heath-light.css
                        - atelier-lakeside-dark.css
                        - atelier-lakeside-light.css
                        - atelier-plateau-dark.css
                        - atelier-plateau-light.css
                        - atelier-savanna-dark.css
                        - atelier-savanna-light.css
                        - atelier-seaside-dark.css
                        - atelier-seaside-light.css
                        - atelier-sulphurpool-dark.css
                        - atelier-sulphurpool-light.css
                        - atom-one-dark.css
                        - atom-one-light.css
                        - brown-paper.css
                        - brown-papersq.png
                        - codepen-embed.css
                        - color-brewer.css
                        - darcula.css
                        - dark.css
                        - darkula.css
                        - default.css
                        - docco.css
                        - dracula.css
                        - far.css
                        - foundation.css
                        - github-gist.css
                        - github.css
                        - googlecode.css
                        - grayscale.css
                        - gruvbox-dark.css
                        - gruvbox-light.css
                        - hopscotch.css
                        - hybrid.css
                        - idea.css
                        - ir-black.css
                        - kimbie.dark.css
                        - kimbie.light.css
                        - magula.css
                        - mono-blue.css
                        - monokai-sublime.css
                        - monokai.css
                        - obsidian.css
                        - ocean.css
                        - paraiso-dark.css
                        - paraiso-light.css
                        - pojoaque.css
                        - pojoaque.jpg
                        - purebasic.css
                        - qtcreator_dark.css
                        - qtcreator_light.css
                        - railscasts.css
                        - rainbow.css
                        - school-book.css
                        - school-book.png
                        - solarized-dark.css
                        - solarized-light.css
                        - sunburst.css
                        - tomorrow-night-blue.css
                        - tomorrow-night-bright.css
                        - tomorrow-night-eighties.css
                        - tomorrow-night.css
                        - tomorrow.css
                        - vs.css
                        - xcode.css
                        - xt256.css
                        - zenburn.css
                - test
                    - api
                        - binaryNumber.js
                        - cNumber.js
                        - fixmarkup.js
                        - getLanguage.js
                        - highlight.js
                        - ident.js
                        - index.js
                        - number.js
                        - starters.js
                        - underscoreIdent.js
                    - browser
                        - index.js
                        - plain.js
                        - worker.js
                    - detect
                        - 1c
                            - default.txt
                        - abnf
                            - default.txt
                        - accesslog
                            - default.txt
                        - actionscript
                            - default.txt
                        - ada
                            - default.txt
                        - apache
                            - default.txt
                        - applescript
                            - default.txt
                        - arduino
                            - default.txt
                        - armasm
                            - default.txt
                        - asciidoc
                            - default.txt
                        - aspectj
                            - default.txt
                        - autohotkey
                            - default.txt
                        - autoit
                            - default.txt
                        - avrasm
                            - default.txt
                        - awk
                            - default.txt
                        - axapta
                            - default.txt
                        - bash
                            - default.txt
                        - basic
                            - default.txt
                        - bnf
                            - default.txt
                        - brainfuck
                            - default.txt
                        - cal
                            - default.txt
                        - capnproto
                            - default.txt
                        - ceylon
                            - default.txt
                        - clean
                            - default.txt
                        - clojure
                            - default.txt
                        - clojure-repl
                            - default.txt
                        - cmake
                            - default.txt
                        - coffeescript
                            - default.txt
                        - coq
                            - default.txt
                        - cos
                            - default.txt
                        - cpp
                            - comment.txt
                            - default.txt
                        - crmsh
                            - default.txt
                        - crystal
                            - default.txt
                        - cs
                            - default.txt
                        - csp
                            - default.txt
                        - css
                            - default.txt
                        - d
                            - default.txt
                        - dart
                            - default.txt
                        - delphi
                            - default.txt
                        - diff
                            - default.txt
                        - django
                            - default.txt
                        - dns
                            - default.txt
                        - dockerfile
                            - default.txt
                        - dos
                            - default.txt
                        - dsconfig
                            - default.txt
                        - dts
                            - default.txt
                        - dust
                            - default.txt
                        - ebnf
                            - default.txt
                        - elixir
                            - default.txt
                        - elm
                            - default.txt
                        - erb
                            - default.txt
                        - erlang
                            - default.txt
                        - erlang-repl
                            - default.txt
                        - excel
                            - default.txt
                        - fix
                            - default.txt
                        - flix
                            - default.txt
                        - fortran
                            - default.txt
                        - fsharp
                            - default.txt
                        - gams
                            - default.txt
                        - gauss
                            - default.txt
                        - gcode
                            - default.txt
                        - gherkin
                            - default.txt
                        - glsl
                            - default.txt
                        - go
                            - default.txt
                            - swift-like.txt
                        - golo
                            - default.txt
                        - gradle
                            - default.txt
                        - groovy
                            - default.txt
                        - haml
                            - default.txt
                        - handlebars
                            - default.txt
                        - haskell
                            - default.txt
                        - haxe
                            - default.txt
                        - hsp
                            - default.txt
                        - htmlbars
                            - default.txt
                        - http
                            - default.txt
                        - hy
                            - default.txt
                        - index.js
                        - inform7
                            - default.txt
                        - ini
                            - default.txt
                        - irpf90
                            - default.txt
                        - java
                            - default.txt
                        - javascript
                            - default.txt
                            - sample1.txt
                            - short-plain.txt
                        - json
                            - default.txt
                        - julia
                            - default.txt
                        - kotlin
                            - default.txt
                        - lasso
                            - default.txt
                        - ldif
                            - default.txt
                        - leaf
                            - default.txt
                        - less
                            - default.txt
                        - lisp
                            - default.txt
                        - livecodeserver
                            - default.txt
                        - livescript
                            - default.txt
                        - llvm
                            - default.txt
                        - lsl
                            - default.txt
                        - lua
                            - default.txt
                        - makefile
                            - default.txt
                        - markdown
                            - default.txt
                        - mathematica
                            - default.txt
                        - matlab
                            - default.txt
                        - maxima
                            - default.txt
                        - mel
                            - default.txt
                        - mercury
                            - default.txt
                        - mipsasm
                            - default.txt
                        - mizar
                            - default.txt
                        - mojolicious
                            - default.txt
                        - monkey
                            - default.txt
                        - moonscript
                            - default.txt
                        - nginx
                            - default.txt
                        - nimrod
                            - default.txt
                        - nix
                            - default.txt
                        - nsis
                            - default.txt
                        - objectivec
                            - default.txt
                        - ocaml
                            - default.txt
                        - openscad
                            - default.txt
                        - oxygene
                            - default.txt
                        - parser3
                            - default.txt
                        - perl
                            - default.txt
                        - pf
                            - default.txt
                        - php
                            - default.txt
                        - pony
                            - default.txt
                        - powershell
                            - default.txt
                        - processing
                            - default.txt
                        - profile
                            - default.txt
                        - prolog
                            - default.txt
                        - protobuf
                            - default.txt
                        - puppet
                            - default.txt
                        - purebasic
                            - default.txt
                        - python
                            - default.txt
                        - q
                            - default.txt
                        - qml
                            - default.txt
                        - r
                            - default.txt
                        - rib
                            - default.txt
                        - roboconf
                            - default.txt
                        - rsl
                            - default.txt
                        - ruby
                            - default.txt
                            - double-colon.txt
                        - ruleslanguage
                            - default.txt
                        - rust
                            - default.txt
                        - scala
                            - default.txt
                        - scheme
                            - default.txt
                        - scilab
                            - default.txt
                        - scss
                            - default.txt
                        - smali
                            - default.txt
                        - smalltalk
                            - default.txt
                        - sml
                            - default.txt
                        - sqf
                            - default.txt
                        - sql
                            - default.txt
                        - stan
                            - default.txt
                        - stata
                            - default.txt
                        - step21
                            - default.txt
                        - stylus
                            - default.txt
                        - subunit
                            - default.txt
                        - swift
                            - default.txt
                        - taggerscript
                            - default.txt
                        - tap
                            - default.txt
                        - tcl
                            - default.txt
                        - tex
                            - default.txt
                        - thrift
                            - default.txt
                        - tp
                            - default.txt
                        - twig
                            - default.txt
                        - typescript
                            - default.txt
                        - vala
                            - default.txt
                        - vbnet
                            - default.txt
                        - vbscript
                            - default.txt
                        - vbscript-html
                            - default.txt
                        - verilog
                            - default.txt
                        - vhdl
                            - default.txt
                        - vim
                            - default.txt
                        - x86asm
                            - default.txt
                        - xl
                            - default.txt
                        - xml
                            - default.txt
                            - groovy-julia.txt
                        - xquery
                            - default.txt
                        - yaml
                            - default.txt
                        - zephir
                            - default.txt
                    - fixtures
                        - expect
                            - brInPre.txt
                            - custommarkup.txt
                            - customtabreplace.txt
                            - endsWithParentVariants.txt
                            - explicit1.txt
                            - explicit2.txt
                            - languagealias.txt
                            - sublanguages.txt
                            - tabreplace.txt
                            - useBr.txt
                        - index.html
                        - nested.js
                    - index.js
                    - markup
                        - accesslog
                            - default.expect.txt
                            - default.txt
                        - actionscript
                            - method-call.expect.txt
                            - method-call.txt
                        - aspectj
                            - intertype-constructor.expect.txt
                            - intertype-constructor.txt
                            - intertype-method.expect.txt
                            - intertype-method.txt
                        - bash
                            - no-numbers.expect.txt
                            - no-numbers.txt
                        - ceylon
                            - nested-comments.expect.txt
                            - nested-comments.txt
                        - clojure
                            - symbols-numbers.expect.txt
                            - symbols-numbers.txt
                        - coffeescript
                            - division.expect.txt
                            - division.txt
                            - function.expect.txt
                            - function.txt
                            - regex.expect.txt
                            - regex.txt
                        - cos
                            - basic.expect.txt
                            - basic.txt
                            - embedded.expect.txt
                            - embedded.txt
                        - cpp
                            - expression-keywords.expect.txt
                            - expression-keywords.txt
                            - function-params.expect.txt
                            - function-params.txt
                            - function-preprocessor.expect.txt
                            - function-preprocessor.txt
                            - function-title.expect.txt
                            - function-title.txt
                            - number-literals.expect.txt
                            - number-literals.txt
                            - pointers-returns.expect.txt
                            - pointers-returns.txt
                            - primitive-types.expect.txt
                            - primitive-types.txt
                            - string-literals.expect.txt
                            - string-literals.txt
                        - cs
                            - dotted-namespace.expect.txt
                            - dotted-namespace.txt
                            - string-interpolation.expect.txt
                            - string-interpolation.txt
                            - titles.expect.txt
                            - titles.txt
                        - css
                            - pseudo-selector.expect.txt
                            - pseudo-selector.txt
                            - url.expect.txt
                            - url.txt
                        - delphi
                            - compiler-directive.expect.txt
                            - compiler-directive.txt
                        - diff
                            - comments.expect.txt
                            - comments.txt
                        - dos
                            - comments.expect.txt
                            - comments.txt
                        - dsconfig
                            - default.expect.txt
                            - default.txt
                        - elixir
                            - function-title.expect.txt
                            - function-title.txt
                        - excel
                            - comments.expect.txt
                            - comments.txt
                        - fortran
                            - numbers.expect.txt
                            - numbers.txt
                        - fsharp
                            - bang-keywords.expect.txt
                            - bang-keywords.txt
                        - go
                            - numbers.expect.txt
                            - numbers.txt
                        - golo
                            - default.expect.txt
                            - default.txt
                        - haskell
                            - infix.expect.txt
                            - infix.txt
                            - nested-comments.expect.txt
                            - nested-comments.txt
                        - http
                            - default.expect.txt
                            - default.txt
                        - index.js
                        - java
                            - gh1031.expect.txt
                            - gh1031.txt
                            - numbers.expect.txt
                            - numbers.txt
                            - titles.expect.txt
                            - titles.txt
                        - javascript
                            - arrow-function.expect.txt
                            - arrow-function.txt
                            - class.expect.txt
                            - class.txt
                            - default-parameters.expect.txt
                            - default-parameters.txt
                            - jsx.expect.txt
                            - jsx.txt
                            - keywords.expect.txt
                            - keywords.txt
                            - method-call.expect.txt
                            - method-call.txt
                            - modules.expect.txt
                            - modules.txt
                            - object-attr.expect.txt
                            - object-attr.txt
                            - shebang.expect.txt
                            - shebang.txt
                            - template-strings.expect.txt
                            - template-strings.txt
                        - kotlin
                            - class.expect.txt
                            - class.txt
                            - function.expect.txt
                            - function.txt
                        - lasso
                            - delimiters.expect.txt
                            - delimiters.txt
                        - ldif
                            - ldapmodify.expect.txt
                            - ldapmodify.txt
                            - schema.expect.txt
                            - schema.txt
                        - less
                            - selectors.expect.txt
                            - selectors.txt
                        - lisp
                            - mec.expect.txt
                            - mec.txt
                        - markdown
                            - code.expect.txt
                            - code.txt
                        - matlab
                            - block_comment.expect.txt
                            - block_comment.txt
                        - maxima
                            - example.expect.txt
                            - example.txt
                            - numbers.expect.txt
                            - numbers.txt
                            - symbols.expect.txt
                            - symbols.txt
                        - ocaml
                            - literals.expect.txt
                            - literals.txt
                            - types.expect.txt
                            - types.txt
                        - php
                            - comments.expect.txt
                            - comments.txt
                            - heredoc.expect.txt
                            - heredoc.txt
                        - pony
                            - prime.expect.txt
                            - prime.txt
                        - powershell
                            - apos-herestring.expect.txt
                            - apos-herestring.txt
                            - quote-herestring.expect.txt
                            - quote-herestring.txt
                        - protobuf
                            - message-message.expect.txt
                            - message-message.txt
                        - python
                            - function-header.expect.txt
                            - function-header.txt
                            - matrix-multiplication.expect.txt
                            - matrix-multiplication.txt
                        - ruby
                            - gemfile.expect.txt
                            - gemfile.txt
                            - heredoc.expect.txt
                            - heredoc.txt
                            - prompt.expect.txt
                            - prompt.txt
                            - regexes.expect.txt
                            - regexes.txt
                        - rust
                            - comments.expect.txt
                            - comments.txt
                            - numbers.expect.txt
                            - numbers.txt
                            - strings.expect.txt
                            - strings.txt
                            - traits.expect.txt
                            - traits.txt
                            - types.expect.txt
                            - types.txt
                            - variables.expect.txt
                            - variables.txt
                        - scala
                            - case-classes.expect.txt
                            - case-classes.txt
                        - scheme
                            - lambda.expect.txt
                            - lambda.txt
                            - quoted.expect.txt
                            - quoted.txt
                        - sql
                            - keywords.expect.txt
                            - keywords.txt
                        - subunit
                            - subunit-errorline.expect.txt
                            - subunit-errorline.txt
                            - subunit-failureline.expect.txt
                            - subunit-failureline.txt
                            - subunit-progressline.expect.txt
                            - subunit-progressline.txt
                            - subunit-skipline.expect.txt
                            - subunit-skipline.txt
                            - subunit-successline.expect.txt
                            - subunit-successline.txt
                            - subunit-tagline.expect.txt
                            - subunit-tagline.txt
                            - subunit-testline.expect.txt
                            - subunit-testline.txt
                            - subunit-timeline.expect.txt
                            - subunit-timeline.txt
                            - subunit-uxsuccessline.expect.txt
                            - subunit-uxsuccessline.txt
                            - subunit-xfailline.expect.txt
                            - subunit-xfailline.txt
                        - swift
                            - functions.expect.txt
                            - functions.txt
                        - tap
                            - basic.expect.txt
                            - basic.txt
                            - without-numbers.expect.txt
                            - without-numbers.txt
                            - yaml-block.expect.txt
                            - yaml-block.txt
                        - twig
                            - filter_with_underscore.expect.txt
                            - filter_with_underscore.txt
                            - template_tags.expect.txt
                            - template_tags.txt
                        - typescript
                            - class.expect.txt
                            - class.txt
                            - functions.expect.txt
                            - functions.txt
                            - jsx.expect.txt
                            - jsx.txt
                            - module-id.expect.txt
                            - module-id.txt
                        - verilog
                            - misc.expect.txt
                            - misc.txt
                            - numbers.expect.txt
                            - numbers.txt
                        - vim
                            - strings-comments.expect.txt
                            - strings-comments.txt
                        - x86asm
                            - labels-directives.expect.txt
                            - labels-directives.txt
                        - xml
                            - space-attributes.expect.txt
                            - space-attributes.txt
                            - unquoted-attributes.expect.txt
                            - unquoted-attributes.txt
                        - yaml
                            - string.expect.txt
                            - string.txt
                    - mocha.opts
                    - special
                        - buildClassName.js
                        - customMarkup.js
                        - endsWithParentVariants.js
                        - explicitLanguage.js
                        - index.js
                        - languageAlias.js
                        - noHighlight.js
                        - subLanguages.js
                        - useBr.js
                    - utility.js
                - tools
                    - all.js
                    - browser.js
                    - build.js
                    - cdn.js
                    - codeformat.js
                    - developer.html
                    - keywordsformat.js
                    - node.js
                    - tasks.js
                    - utility.js
            - justgage-1.2.2
                - examples
                    - auto-adjust.htm
                    - counter.html
                    - custom-interval.htm
                    - custom-node.html
                    - custom-sectors.html
                    - custom-value-renderer.html
                    - customize-style.htm
                    - defaults.html
                    - font-options.html
                    - format-number.html
                    - html5-data-attribute-config.html
                    - human-friendly-numbers.html
                    - pointer.html
                    - refresh-maximum.html
                    - responsive-gauges.htm
                    - reverse.html
                - justgage.js
                - raphael-2.1.4.min.js
            - plupload-2.1.9
                - examples
                    - custom.html
                    - dump.php
                    - events.html
                    - jquery
                        - all_runtimes.html
                        - jquery_ui_widget.html
                        - queue_widget.html
                        - s3.php
                    - upload.php
                - js
                    - i18n
                        - ar.js
                        - az.js
                        - bg.js
                        - bs.js
                        - ca.js
                        - cs.js
                        - cy.js
                        - da.js
                        - de.js
                        - el.js
                        - en.js
                        - es.js
                        - et.js
                        - fa.js
                        - fi.js
                        - fr.js
                        - he.js
                        - hr.js
                        - hu.js
                        - hy.js
                        - id.js
                        - it.js
                        - ja.js
                        - ka.js
                        - kk.js
                        - km.js
                        - ko.js
                        - ku_IQ.js
                        - lt.js
                        - lv.js
                        - ms.js
                        - nl.js
                        - pl.js
                        - pt.js
                        - pt_BR.js
                        - ro.js
                        - ru.js
                        - sk.js
                        - sl.js
                        - sq.js
                        - sr.js
                        - sv.js
                        - th_TH.js
                        - tr.js
                        - uk_UA.js
                        - vi.js
                        - zh_CN.js
                        - zh_TW.js
                    - jquery.plupload.queue
                        - css
                            - jquery.plupload.queue.css
                        - img
                            - backgrounds.gif
                            - buttons-disabled.png
                            - buttons.png
                            - delete.gif
                            - done.gif
                            - error.gif
                            - throbber.gif
                            - transp50.png
                        - jquery.plupload.queue.js
                        - jquery.plupload.queue.min.js
                    - jquery.ui.plupload
                        - css
                            - jquery.ui.plupload.css
                        - img
                            - loading.gif
                            - plupload.png
                        - jquery.ui.plupload.js
                        - jquery.ui.plupload.min.js
                    - moxie.js
                    - moxie.min.js
                    - Moxie.swf
                    - Moxie.xap
                    - plupload.dev.js
                    - plupload.full.min.js
                    - plupload.min.js
                - license.txt
                - readme.md
            - ueditor-1.4.3.3
                - dialogs
                    - anchor
                        - anchor.html
                    - attachment
                        - attachment.css
                        - attachment.html
                        - attachment.js
                        - fileTypeImages
                            - icon_chm.gif
                            - icon_default.png
                            - icon_doc.gif
                            - icon_exe.gif
                            - icon_jpg.gif
                            - icon_mp3.gif
                            - icon_mv.gif
                            - icon_pdf.gif
                            - icon_ppt.gif
                            - icon_psd.gif
                            - icon_rar.gif
                            - icon_txt.gif
                            - icon_xls.gif
                        - images
                            - alignicon.gif
                            - alignicon.png
                            - bg.png
                            - file-icons.gif
                            - file-icons.png
                            - icons.gif
                            - icons.png
                            - image.png
                            - progress.png
                            - success.gif
                            - success.png
                    - background
                        - background.css
                        - background.html
                        - background.js
                        - images
                            - bg.png
                            - success.png
                    - charts
                        - chart.config.js
                        - charts.css
                        - charts.html
                        - charts.js
                        - images
                            - charts0.png
                            - charts1.png
                            - charts2.png
                            - charts3.png
                            - charts4.png
                            - charts5.png
                    - emotion
                        - emotion.css
                        - emotion.html
                        - emotion.js
                        - images
                            - 0.gif
                            - bface.gif
                            - cface.gif
                            - fface.gif
                            - jxface2.gif
                            - neweditor-tab-bg.png
                            - tface.gif
                            - wface.gif
                            - yface.gif
                    - gmap
                        - gmap.html
                    - help
                        - help.css
                        - help.html
                        - help.js
                    - image
                        - image.css
                        - image.html
                        - image.js
                        - images
                            - alignicon.jpg
                            - bg.png
                            - icons.gif
                            - icons.png
                            - image.png
                            - progress.png
                            - success.gif
                            - success.png
                    - insertframe
                        - insertframe.html
                    - internal.js
                    - link
                        - link.html
                    - map
                        - map.html
                        - show.html
                    - music
                        - music.css
                        - music.html
                        - music.js
                    - preview
                        - preview.html
                    - scrawl
                        - images
                            - addimg.png
                            - brush.png
                            - delimg.png
                            - delimgH.png
                            - empty.png
                            - emptyH.png
                            - eraser.png
                            - redo.png
                            - redoH.png
                            - scale.png
                            - scaleH.png
                            - size.png
                            - undo.png
                            - undoH.png
                        - scrawl.css
                        - scrawl.html
                        - scrawl.js
                    - searchreplace
                        - searchreplace.html
                        - searchreplace.js
                    - snapscreen
                        - snapscreen.html
                    - spechars
                        - spechars.html
                        - spechars.js
                    - table
                        - dragicon.png
                        - edittable.css
                        - edittable.html
                        - edittable.js
                        - edittd.html
                        - edittip.html
                    - template
                        - config.js
                        - images
                            - bg.gif
                            - pre0.png
                            - pre1.png
                            - pre2.png
                            - pre3.png
                            - pre4.png
                        - template.css
                        - template.html
                        - template.js
                    - video
                        - images
                            - bg.png
                            - center_focus.jpg
                            - file-icons.gif
                            - file-icons.png
                            - icons.gif
                            - icons.png
                            - image.png
                            - left_focus.jpg
                            - none_focus.jpg
                            - progress.png
                            - right_focus.jpg
                            - success.gif
                            - success.png
                        - video.css
                        - video.html
                        - video.js
                    - webapp
                        - webapp.html
                    - wordimage
                        - fClipboard_ueditor.swf
                        - imageUploader.swf
                        - tangram.js
                        - wordimage.html
                        - wordimage.js
                - index.html
                - lang
                    - en
                        - en.js
                        - images
                            - addimage.png
                            - alldeletebtnhoverskin.png
                            - alldeletebtnupskin.png
                            - background.png
                            - button.png
                            - copy.png
                            - deletedisable.png
                            - deleteenable.png
                            - listbackground.png
                            - localimage.png
                            - music.png
                            - rotateleftdisable.png
                            - rotateleftenable.png
                            - rotaterightdisable.png
                            - rotaterightenable.png
                            - upload.png
                    - zh-cn
                        - images
                            - copy.png
                            - localimage.png
                            - music.png
                            - upload.png
                        - zh-cn.js
                - php
                    - action_crawler.php
                    - action_list.php
                    - action_upload.php
                    - config.json
                    - controller.php
                    - Uploader.class.php
                - themes
                    - default
                        - css
                            - ueditor.css
                            - ueditor.min.css
                        - dialogbase.css
                        - images
                            - anchor.gif
                            - arrow.png
                            - arrow_down.png
                            - arrow_up.png
                            - button-bg.gif
                            - cancelbutton.gif
                            - charts.png
                            - cursor_h.gif
                            - cursor_h.png
                            - cursor_v.gif
                            - cursor_v.png
                            - dialog-title-bg.png
                            - filescan.png
                            - highlighted.gif
                            - icons-all.gif
                            - icons-all.psd
                            - icons.gif
                            - icons.png
                            - loaderror.png
                            - loading.gif
                            - lock.gif
                            - neweditor-tab-bg.png
                            - pagebreak.gif
                            - scale.png
                            - sortable.png
                            - spacer.gif
                            - sparator_v.png
                            - table-cell-align.png
                            - tangram-colorpicker.png
                            - toolbar_bg.png
                            - unhighlighted.gif
                            - upload.png
                            - videologo.gif
                            - word.gif
                            - wordpaste.png
                    - iframe.css
                - third-party
                    - codemirror
                        - codemirror.css
                        - codemirror.js
                    - highcharts
                        - adapters
                            - mootools-adapter.js
                            - mootools-adapter.src.js
                            - prototype-adapter.js
                            - prototype-adapter.src.js
                            - standalone-framework.js
                            - standalone-framework.src.js
                        - highcharts-more.js
                        - highcharts-more.src.js
                        - highcharts.js
                        - highcharts.src.js
                        - modules
                            - annotations.js
                            - annotations.src.js
                            - canvas-tools.js
                            - canvas-tools.src.js
                            - data.js
                            - data.src.js
                            - drilldown.js
                            - drilldown.src.js
                            - exporting.js
                            - exporting.src.js
                            - funnel.js
                            - funnel.src.js
                            - heatmap.js
                            - heatmap.src.js
                            - map.js
                            - map.src.js
                            - no-data-to-display.js
                            - no-data-to-display.src.js
                        - themes
                            - dark-blue.js
                            - dark-green.js
                            - gray.js
                            - grid.js
                            - skies.js
                    - jquery-1.10.2.js
                    - jquery-1.10.2.min.js
                    - jquery-1.10.2.min.map
                    - snapscreen
                        - UEditorSnapscreen.exe
                    - SyntaxHighlighter
                        - shCore.js
                        - shCoreDefault.css
                    - video-js
                        - font
                            - vjs.eot
                            - vjs.svg
                            - vjs.ttf
                            - vjs.woff
                        - video-js.css
                        - video-js.min.css
                        - video-js.swf
                        - video.dev.js
                        - video.js
                    - webuploader
                        - Uploader.swf
                        - webuploader.css
                        - webuploader.custom.js
                        - webuploader.custom.min.js
                        - webuploader.flashonly.js
                        - webuploader.flashonly.min.js
                        - webuploader.html5only.js
                        - webuploader.html5only.min.js
                        - webuploader.js
                        - webuploader.min.js
                        - webuploader.withoutimage.js
                        - webuploader.withoutimage.min.js
                    - xss.min.js
                    - zeroclipboard
                        - ZeroClipboard.js
                        - ZeroClipboard.min.js
                        - ZeroClipboard.swf
                - ueditor.all.js
                - ueditor.all.min.js
                - ueditor.config.js
                - ueditor.parse.js
                - ueditor.parse.min.js
    - jsPlumb
        - jsplumb.bundle.js
    - layui
        - css
            - layui.css
            - layui.mobile.css
            - layui_black.css
            - modules
                - code.css
                - laydate
                    - default
                        - laydate.css
                - layer
                    - default
                        - icon-ext.png
                        - icon.png
                        - layer.css
                        - loading-0.gif
                        - loading-1.gif
                        - loading-2.gif
        - define
            - InsertSelect.js
            - multiSelect.js
            - table-select
                - css
                    - layui-table-select.css
                - js
                    - layui-table-select.js
            - xm-select.js
        - font
            - iconfont.eot
            - iconfont.svg
            - iconfont.ttf
            - iconfont.woff
            - iconfont.woff2
        - images
            - face
                - 0.gif
                - 1.gif
                - 10.gif
                - 11.gif
                - 12.gif
                - 13.gif
                - 14.gif
                - 15.gif
                - 16.gif
                - 17.gif
                - 18.gif
                - 19.gif
                - 2.gif
                - 20.gif
                - 21.gif
                - 22.gif
                - 23.gif
                - 24.gif
                - 25.gif
                - 26.gif
                - 27.gif
                - 28.gif
                - 29.gif
                - 3.gif
                - 30.gif
                - 31.gif
                - 32.gif
                - 33.gif
                - 34.gif
                - 35.gif
                - 36.gif
                - 37.gif
                - 38.gif
                - 39.gif
                - 4.gif
                - 40.gif
                - 41.gif
                - 42.gif
                - 43.gif
                - 44.gif
                - 45.gif
                - 46.gif
                - 47.gif
                - 48.gif
                - 49.gif
                - 5.gif
                - 50.gif
                - 51.gif
                - 52.gif
                - 53.gif
                - 54.gif
                - 55.gif
                - 56.gif
                - 57.gif
                - 58.gif
                - 59.gif
                - 6.gif
                - 60.gif
                - 61.gif
                - 62.gif
                - 63.gif
                - 64.gif
                - 65.gif
                - 66.gif
                - 67.gif
                - 68.gif
                - 69.gif
                - 7.gif
                - 70.gif
                - 71.gif
                - 8.gif
                - 9.gif
        - lay
            - modules
                - carousel.js
                - code.js
                - colorpicker.js
                - dropdown.js
                - element.js
                - flow.js
                - form.js
                - jquery.js
                - lay.js
                - laydate.js
                - layedit.js
                - layer.js
                - laypage.js
                - laytpl.js
                - mobile.js
                - rate.js
                - slider.js
                - table.js
                - transfer.js
                - tree.js
                - upload.js
                - upload1.js
                - util.js
        - layui.all.js
        - layui.js
    - layui-2.6.8
        - css
            - layui.css
            - modules
                - code.css
                - laydate
                    - default
                        - font.css
                        - laydate.css
                - layer
                    - default
                        - icon-ext.png
                        - icon.png
                        - layer.css
                        - loading-0.gif
                        - loading-1.gif
                        - loading-2.gif
        - font
            - iconfont.eot
            - iconfont.svg
            - iconfont.ttf
            - iconfont.woff
            - iconfont.woff2
        - layui.js
        - modules
            - all.js
            - carousel.js
            - code.js
            - colorpicker.js
            - demo.js
            - dropdown.js
            - element.js
            - flow.js
            - form.js
            - jquery.js
            - lay.js
            - laydate.js
            - layedit.js
            - layer.js
            - laypage.js
            - laytpl.js
            - layui.all.js
            - mobile.js
            - rate.js
            - slider.js
            - table.js
            - transfer.js
            - tree.js
            - upload.js
            - util.js
    - layui-2.8.x
        - css
            - layui.css
        - font
            - iconfont.eot
            - iconfont.svg
            - iconfont.ttf
            - iconfont.woff
            - iconfont.woff2
        - layui.js
    - layui_exts
        - xm-select.js
    - loading
        - jquery.loading.min.js
        - load-min.js
        - load.css
        - load.js
        - loading.gif
    - preview
        - actualSize.png
        - b1.jpg
        - close.png
        - jquery.rotate.min.js
        - next.png
        - prev.png
        - preview.css
        - preview.js
        - preview1.css
        - preview1.js
        - rotateLeft.png
        - rotateRight.png
        - zoomIn.png
        - zoomOut.png
    - webuploader
        - README.md
        - Uploader.swf
        - webuploader.css
        - webuploader.custom.js
        - webuploader.custom.min.js
        - webuploader.fis.js
        - webuploader.flashonly.js
        - webuploader.flashonly.min.js
        - webuploader.html5nodepend.js
        - webuploader.html5nodepend.min.js
        - webuploader.html5only.js
        - webuploader.html5only.min.js
        - webuploader.js
        - webuploader.min.js
        - webuploader.noimage.js
        - webuploader.noimage.min.js
        - webuploader.nolog.js
        - webuploader.nolog.min.js
        - webuploader.withoutimage.js
        - webuploader.withoutimage.min.js
    - word
        - FileSaver.js
        - jquery.wordexport.js
    - xlsxFull
        - .keep
        - xlsx.full.min.js
        - xlsx.full.min1.js
        - xlsx.full.min2.js
        - xlsxutils.js
    - ztree
        - css
            - awesomeStyle
                - awesome.css
                - awesome.less
                - fa.less
                - img
                    - loading.gif
            - bootstrapStyle.css
            - contextMenu.css
            - demo.css
            - img
                - bootstrap.png
                - line_conn.png
            - metroStyle
                - img
                    - line_conn.png
                    - loading.gif
                    - metro.gif
                    - metro.png
                - metroStyle.css
            - zTreeStyle
                - img
                    - diy
                        - 1_close.png
                        - 1_open.png
                        - 2.png
                        - 3.png
                        - 4.png
                        - 5.png
                        - 6.png
                        - 7.png
                        - 8.png
                        - 9.png
                    - line_conn.gif
                    - loading.gif
                    - zTreeStandard.gif
                    - zTreeStandard.png
                - zTreeStyle.css
        - js
            - jquery-1.4.4.min.js
            - jquery.contextMenu.min.js
            - jquery.js
            - jquery.ztree.all.js
            - jquery.ztree.all.min.js
            - jquery.ztree.core.js
            - jquery.ztree.core.min.js
            - jquery.ztree.excheck.js
            - jquery.ztree.excheck.min.js
            - jquery.ztree.exedit.js
            - jquery.ztree.exedit.min.js
            - jquery.ztree.exhide.js
            - jquery.ztree.exhide.min.js
- README.md
- sql
    - 20250528.sql
    - archive
        - 20200313.sql
        - 20200319.sql
        - 20200324.sql
        - 20200327.sql
        - 20200417.sql
        - 20200425.sql
        - 20200506.sql
        - 20200514.sql
        - 20200715.sql
        - 20200731.sql
        - 20210128.sql
        - 20210902.sql
        - 20210924.sql
        - 20211108.sql
        - 20211111.sql
        - 20211118.sql
        - 20211124.sql
        - 20211206.sql
        - 20211230.sql
        - 20220126.sql
        - 20220223.sql
        - 20220303.sql
        - 20220309.sql
        - 20220324.sql
        - 20220325.sql
        - 20220331.sql
        - 20220419.sql
        - 20220509.sql
        - 20220519.sql
        - 20220527发射场专业.sql
        - 20220609.sql
        - 20220718.sql
        - 20220721.sql
        - 20220811.sql
        - 20220829.sql
        - 20220905.sql
        - 20220928.sql
        - 20221008.sql
        - 20221115.sql
        - 20221201.sql
        - 20230406.sql
        - 20230417.sql
        - 20230510.sql
        - 20230529.sql
        - 20230627.sql
        - 20230724.sql
        - 20230726.sql
        - 20230802.sql
        - 20230818.sql
        - 20231008.sql
        - 20231024.sql
        - 20231107.sql
        - 20231109.sql
        - 20231116.sql
        - 20231207.sql
        - 20231225.sql
        - 20231228.sql
        - 20240110.sql
        - 20240124.sql
        - 20240129.sql
        - 20240219.sql
        - 20240304.sql
        - 20240315.sql
        - 20240320.sql
        - 20240410.sql
        - 20240524.sql
        - 20240926.sql
        - 20241107.sql
        - 20241127.sql
        - 20241217.sql
        - 20250123.sql
        - 20250211.sql
        - 20250220.sql
        - 20250224.sql
        - 20250226.sql
        - 20250304.sql
        - 20250329.sql
        - 20250402.sql
        - 20250408.sql
        - 20250409.sql
        - 20250415.sql
        - 20250508.sql
        - 20250515.sql
        - query.sql
        - 发射场sql.sql
    - create_table_oracle.sql
    - init
        - 20191221create.sql
        - 20191224Alert.sql
        - 20200107.sql
        - CRAFT_DATA_LIST.sql
        - create.sql
        - delete.sql
        - DESIGN_DATA_LIST.sql
        - index.sql
        - insertData.sql
        - PROCESS_CONTROL_LIST.sql
        - QUALITY_CONTROL_LIST.sql
    - liuxw
        - alldatalistview.sql
        - dataPackageManagement.sql
        - STATE_CHECK.sql
        - sysAuthority.sql
        - xmlContentSql.sql
    - lvyb
        - auditlog.sql
        - db_sys_init.sql
        - menudata.sql
        - sequence.sql
        - statictics_tables.sql
        - sys.sql
        - view.sql
    - pyk
        - MANUAL_SYNCQUEUE_SEQ.sql
        - SYS_MANUALSYNC_QUEUE.sql
    - wanghq
        - 20220110.sql
        - DATAPACKAGETREE.sql
        - DATA_INIT.sql
        - DATA_PACKAGE.sql
        - DICTIONARY.sql
        - DICTIONARY_DATA.sql
        - INIT_STATUS.sql
        - PARAM_CONFIG.sql
        - RELATION_TABLE.sql
        - RESULTGATHER.sql
        - sequence.sql
        - TABLE_CONFIG.sql
        - TABLE_DATA.sql
        - THINGWORX_SYS_MENU.sql
- twx
    - ExcelParseThing
        - QueryAllCable.js
        - QueryCableDataByRef.js
        - SaveaDta.js
        - SaveBasicInfo.js
    - ExtApi
        - GetModels.js
        - GetTable.js
    - HDFS_Thing
        - formatFileSize.js
        - testDownloadHdfs.js
        - testListHdfs.js
        - testUploadHdfs.js
    - publishMissionThing
        - AddDatasToCaptureTable.js
        - AddDataToCaptureManagementTable.js
        - AddDataToDataListTable.js
        - AddDataToWSConfigTable.js
        - CheckAutoCollectConfig.js
        - deleteDataListDataByTypeAndID.js
        - getAllCaptureManagementDataByTypeAndNodeName.js
        - getAllDataListDataByType.js
        - getAllDataListDataByTypeAndNodeName.js
        - getAllDataListDataByTypeAndStatus.js
        - getAllWSConfigData.js
        - getDataFromDataDictionary.js
        - getDataPackageByProductModel.js
        - getDPFileType.js
        - getTreeNodeByTypeAndParentID.js
        - getWSConfigDataByConditions.js
        - setNodeToResultTable.js
        - setStateToResultTable.js
        - TestFileService.js
        - updateDataToDataListTable.js
        - updateDataToMSConfigTable.js
        - UpdateListStatus.js
    - Thing.DB.Oracle
        - All.sql
        - RunCommand.sql
        - RunQuery.sql
        - test.js
        - test1.js
    - Thing.DB.Oracle.Device
        - RunCommand.sql
        - RunQuery.sql
    - Thing.DP.BasicInit
        - DeleteAllTables.js
        - InitBasicData.js
        - InitDataPackageTree.js
    - Thing.Fn.AitScreen
        - CreateProblemData.js
        - CreateRandomData.js
        - CreateRandomSubmitData.js
        - GetChangeOrderChartOption.js
        - GetChartDataZoom.js
        - GetListChartOption.js
        - GetNonconformityChartOption.js
        - GetProblemChartOption.js
        - GetProblemTypeCountSql.js
        - GetProblemTypeDataSql.js
        - GetQueryCountSql.js
        - GetSeverityLevels.js
        - GetSituationTypes.js
        - GetSubmitChartOption.js
        - GetSubmitListSql.js
        - GetTempChartOption.js
        - GetUserDataTag.js
        - GetUserRoleModel.js
        - GetWhereSql.js
        - QueryAllModel.js
        - QueryAllModelProcess.js
        - QueryChangeBranchDetail.js
        - QueryChangeOrderCount.js
        - QueryChangeOrderList.js
        - QueryFanyi.js
        - QueryList.js
        - QueryModelListCount.js
        - QueryModelPhaseAIT.js
        - QueryModelProcess.js
        - QueryModelProcessFromMes.js
        - QueryModelSubmitCount.js
        - QueryNonconformityCount.js
        - QueryNonconformityList.js
        - QueryProblemCount.js
        - QueryProccessCount.js
        - QuerySubmitCount.js
        - QuerySubmitList.js
        - QuerySubmitUnitCount.js
        - QueryTempList.js
        - QueryTemporaryCount.js
        - QueryUpdateTime.js
        - SetModelPhaseCurrentAITNode.js
        - StrIsEmpty.js
        - SyncMESModelProgress.js
        - UpdateAllData.js
        - UpdateChangeOrderCount.js
        - UpdateModelStatistics.js
        - UpdateModelSubmitCount.js
        - UpdateModelTypeListCount.js
        - UpdateNonconformityCount.js
        - UpdateProblemCount.js
    - Thing.Fn.BigScreen
        - FormatFileSize.js
        - QueryAllModel.js
        - QueryListTable.js
        - QueryModelProcess.js
        - QueryModelProcessCount.js
        - QueryModelSort.js
        - QueryNum.js
        - QueryPhotoSort.js
        - QueryQualityData.js
        - QueryRecentPhoto.js
        - QueryTypeQuality.js
        - UpdateAllData.js
        - UpdateModelFileSize.js
        - UpdateModelListCount.js
        - UpdateModelProcess.js
        - UpdateModelQualityDataCount.js
        - UpdateModelQualityPhotoCount.js
    - Thing.Fn.BOM
        - addNode.js
        - autoAsyncBOM.js
        - AutoUpdateProductTree.js
        - BatchImportBom.js
        - DeleteNode.js
        - DeleteProductNode.js
        - getModelList.js
        - GetNodeTypeByLevel.js
        - getProduct.js
        - GetProductNodeTypeByLevel.js
        - GetProductTreeNewId.js
        - GetSortByParentId.js
        - GetSortByPid.js
        - getTreeNewId.js
        - ManualAddNode.js
        - ManualAddProductNode.js
        - parseBOMxml.js
        - ProductLinkList.js
        - QueryBomTreeByCode.js
        - QueryBomTreeById.js
        - QueryBomTreeRoot.js
        - QueryLabel.js
        - QueryPdmOid.js
        - QueryProductTreeById.js
        - QueryProductTreeByPhase.js
        - QueryProductTreeRoot.js
        - UpdateNode.js
        - UpdateProductNode.js
        - UpdateTreeNodeSort.js
    - Thing.Fn.BPM
        - CreateBranch.js
        - CreateRandomNonconformityData.js
        - CreateRandomTCData.js
        - CreateTechnicalChangeBranchData.js
        - SyncNonconformityReviewData.js
        - SyncTechnicalChangeBranchData.js
        - SyncTechnicalChangeMainData.js
    - Thing.Fn.Certificate
        - CreateCertificate.js
        - QueryCertificatePath.js
    - Thing.Fn.Costing
        - AddCalcNode.js
        - AddCalcParam.js
        - AddCalcResult.js
        - AddForm.js
        - DeleteCalcNode.js
        - DeleteCalcParam.js
        - DeleteCalcResult.js
        - DeleteForm.js
        - DeployZeroProcess.js
        - Handle1.js
        - InitDictionary.js
        - InitiateZero.js
        - postFlw.js
        - QueryCalcParam.js
        - QueryCalcResult.js
        - QueryCalcTreeChild.js
        - QueryCalcTreeRoot.js
        - QueryCandidateUser.js
        - QueryExpenseType.js
        - QueryFinishedTask.js
        - QueryFlowDef.js
        - QueryFlwRecords.js
        - QueryForm.js
        - QueryFormula.js
        - QueryMyInitProcess.js
        - QueryTodoTask.js
        - RelateForm.js
        - SaveForm.js
        - SaveFormula.js
        - SaveResultFormula.js
        - test.js
        - UpdateBusinessData.js
        - UpdateCalcNode.js
        - UpdateCalcNodeSort.js
        - UpdateCalcParam.js
        - UpdateCalcResult.js
    - Thing.Fn.DataCollect
        - BatchReAssignData.js
        - GetDataPkgFileType.js
        - GetDataPkgPlanFileType.js
        - GetTableNameByTabName.js
        - GetTypeByTabName.js
        - GetUploadFileType.js
        - ProductLink.js
        - QueryPageData.js
        - QueryPhotoData.js
        - QueryPhotoTotalNumbers.js
        - QueryTotalNumbers.js
        - ReAssignData.js
        - UpdateaDataState.js
        - UpdateParam.js
    - Thing.Fn.DataDownload
        - GetDataPkgSql.js
        - QueryDataPkg.js
        - QueryDataPkgByTreeId.js
        - QueryDataPkgCount.js
        - QueryDataPkgHasResult.js
        - QueryMajor.js
        - QueryPhase.js
        - QueryProcess.js
        - QueryProduct.js
        - QueryResultByDataPkgId.js
    - Thing.Fn.DataPackage
        - AddFolder.js
        - AddNode.js
        - copyNode.js
        - CreateDataPkg.js
        - DealFolderSort.js
        - DealModelSort.js
        - DeleteNode.js
        - GetAllTestModel.js
        - getDataPkgNumber.js
        - getNodeTypeByLevel.js
        - getNowTime.js
        - getSortByParentId.js
        - getTreeJson.js
        - InitData.js
        - IsAllowDeleteNode.js
        - IsAllowEditNode.js
        - QueryDataPackageJSON.js
        - QueryDataPackageTree.js
        - QueryDataPackageTree1.js
        - QueryDataPackageTreeRoot.js
        - QueryDataPkg.js
        - QueryDataPkgByName.js
        - QueryFolder.js
        - QueryModelByFolder.js
        - QueryResultByTreeId.js
        - QueryTag.js
        - SelectFolder.js
        - SelectTag.js
        - TransferNode.js
        - updateNodeName.js
        - updateNodeSort.js
    - Thing.Fn.DataSearch
        - DealAddParamCol.js
        - getDataSearchConditionSql.js
        - getDataSearchConditionSql1.js
        - getDataSearchTableId.js
        - getDataSearchTableName.js
        - GetPhotoIsExist.js
        - getProductListData.js
        - getSeachCondSql.js
        - QueryAddendum.js
        - QueryAddendumCount.js
        - QueryAddendumNoPage.js
        - QueryAttachment.js
        - QueryAttachmentCount.js
        - QueryData.js
        - QueryDataCount.js
        - QueryDataSearch.js
        - QueryDataSearch1.js
        - QueryDataSearchCount.js
        - QueryDataSearchCount1.js
        - QueryDataSearchNoPage.js
        - QueryDataSearchNoPage1.js
        - QueryMateriel.js
        - QueryMaterielCount.js
        - QueryPhotoPreviewUrl.js
        - QueryProcessData.js
        - QueryProcessDataCount.js
        - QueryProcessPhotoData.js
        - QueryProcessPhotoDataCount.js
        - QueryProcessTypeData.js
        - QueryQualityDataCount.js
        - QueryTreeIdsByNodeCode.js
        - QueryTreeIdsByResultId.js
        - QueryTreeIdsByTreeId.js
    - Thing.Fn.Dl
        - QueryDlCode.js
        - SyncDLReport.js
    - Thing.Fn.ElectricTest
        - CreateTestFile.js
        - CreateTestTemporaryFile.js
        - GetAllTestModel.js
        - GetMappingNodeCode.js
        - GetTempCategory.js
        - GetTempSql.js
        - GetTestEventSearchItem.js
        - GetTestEventSql.js
        - InitTemporaryTable.js
        - ManualSync.js
        - PostCreateFile.js
        - QueryExportData.js
        - QueryStatisticsDate.js
        - QueryStatisticsModel.js
        - QueryTempCompletionStatus.js
        - QueryTempStatisticsModel.js
        - QueryTestEvent.js
        - SaveTestTable.js
        - SyncAll.js
        - UpdateAllTestLog.js
        - UpdateTestLog.js
    - Thing.Fn.ExcelImport
        - addDataToResultTable.js
        - GetConditionSql.js
        - insert_cable_insulation_test.js
        - insert_electronic_components.js
        - insert_heating_circuit_test.js
        - insert_heating_element_reinspection.js
        - QueryExcelData.js
        - QueryTotalNumbers.js
    - Thing.Fn.IndexAnalysis
        - GetNumberValue.js
        - QueryAnalysis.js
        - QueryIndexParam.js
        - QueryModelTree.js
        - QueryQualityType.js
    - Thing.Fn.Interface
        - AddTask.js
        - AssignPlanTask.js
        - CreatePkg.js
        - CreatePkgList.js
        - CreatePkgTpl.js
        - DeletePkg.js
        - DeletePkgList.js
        - FinishedTask.js
        - GetReqUrl.js
        - GetTaskPostData.js
        - Guuid.js
        - QueryAllFolder.js
        - QueryAllUser.js
        - QueryCollectionTiming.js
        - QueryFinishedTaskList.js
        - QueryListInfo.js
        - QueryListTpl.js
        - QueryPkgTpl.js
        - QueryPlanListPkg.js
        - QueryPlanTaskInfo.js
        - QueryProjectCategory.js
        - QueryTaskInfo.js
        - QueryToDoTaskList.js
        - RejectTask.js
        - RequireBasicData.js
        - RequireSearchDplStatus.js
        - RequireSubmitPkg.js
        - RequireSubmitTask.js
        - RequireSyncTask.js
        - ReturnTask.js
        - SaveData.js
        - SavePlanningPkg.js
        - SavePlanningTableData.js
        - StrToClobSql.js
        - SubmitPkg.js
        - SubmitTask.js
        - SyncAllBasicData.js
        - SyncDataDictionary.js
        - SyncDataPacketListTemplate.js
        - SyncDataPacketTemplate.js
        - SyncHeaderDictionary.js
        - SyncPlanTask.js
        - SyncTask.js
        - SyncTaskStatus.js
        - test.js
        - TestTaskData.js
    - Thing.Fn.KeyAttrAnalyze
        - getAllKeyAttrInfo.js
        - getChildNodeInTree.js
        - getDataByModel.js
        - getKeyItemAnalyticsByParams.js
        - getProductByNameAndModel.js
        - getProductByTableName.js
        - test.js
    - Thing.Fn.LaunchConfirm
        - AddATable.js
        - AddBTable.js
        - AddModel.js
        - AddPhoto.js
        - AddPhotos.js
        - AddProject.js
        - AddSign.js
        - BatchDealHtmlData.js
        - CopyModel.js
        - CopyNode.js
        - DealHistorySignHtml.js
        - DealTableHeader.js
        - DeleteNode.js
        - DeletePhoto.js
        - GetNewPhotoNum.js
        - GetSortByPid.js
        - GetTableName.js
        - GetTreeNewId.js
        - ImportModelData.js
        - ImportPhotoData.js
        - ImportSignData.js
        - QueryChildrenByPid.js
        - QueryNodeById.js
        - QueryPhotoById.js
        - QuerySignById.js
        - QueryTplTree.js
        - QueryTree.js
        - QueryTreeById.js
        - QueryTreeNodeByPid.js
        - QueryTreeRoot.js
        - SaveTableData.js
        - StrToClobSql.js
        - TableData2Html.js
        - UpdateLockRow.js
        - UpdateModel.js
        - UpdateNode.js
        - UpdateNodeSort.js
        - UpdateSignHtml.js
        - UpdateTableHeader.js
        - UpdateTableStatus.js
        - UploadTpl.js
    - Thing.Fn.LaunchOnlineConfirm
        - CheckDirectoryUpdateStatus.js
        - CreateOrUpdateSyncStatus.js
        - CreateSyncStatus.js
        - ExportMorePdf.js
        - ExtractPdfPageNumbers.js
        - GetAllCommitmentNodeSql.js
        - ProcessDirectoryUpdateTask.js
        - QueryCommitmentModel.js
        - QueryMappingAitTableId.js
        - RecoverSync.js
        - StopSync.js
        - SyncCommitmentData.js
        - UpdateDirectory.js
        - UpdateSyncStatus.js
    - Thing.Fn.ListData
        - AsyncPDMFile.js
        - GetListDataFieldSql.js
        - GetPDMPreviewUrl.js
        - GetQueryCheckCardSql.js
        - GetQueryListDataSql.js
        - LinkListData.js
        - QueryCheckCard.js
        - QueryCheckCardCount.js
        - QueryCheckCardPage.js
        - QueryCurveData.js
        - QueryDataPkgAndList.js
        - QueryGZKAndPhoto.js
        - QueryLinkListData.js
        - QueryListData.js
        - QueryListDataCount.js
        - QueryListDataPage.js
    - Thing.Fn.PhotoQuery
        - QueryAITPhotoByProduct.js
        - QueryAllProcess.js
        - QueryFinalProcessPhoto.js
        - QueryIdCol.js
        - QueryPhoto.js
        - QueryPhotoCount.js
        - QueryProcessByProduct.js
        - QueryProcessPhoto.js
        - QueryWholeStartPhoto.js
        - UpdateAllPhotoCount.js
        - UpdatePhotoCount.js
    - Thing.Fn.PlanBuild
        - DeletePlanListByID.js
    - Thing.Fn.ProcessTree
        - QueryAllNodeByLeafNode.js
        - QueryChildNode.js
        - QueryLinkFileType.js
        - QueryTreeRoot.js
    - Thing.Fn.ProductDataQuery
        - GetListDataSql.js
        - QueryListDataCount.js
        - QueryListDataPage.js
        - QueryParentsByTreeId.js
        - QueryQualityTypeByTreeId.js
        - QuerySecondTableData.js
        - QuerySecondTableDataCount.js
        - QuerySecondTableDataPage.js
    - Thing.Fn.ProductQuality
        - AddPush.js
        - ConfirmQuality.js
        - GetTableDataSql.js
        - insert_P_GENERALSTRUCTURE.js
        - insert_P_LOWFREQUENCYCABLE.js
        - insert_P_MULTILAYERMACHINING.js
        - insert_P_OSRPASTE.js
        - insert_P_STARARROWLINK.js
        - insert_P_ThermalControlSpraying.js
        - insert_P_THERMISTORMACHINING.js
        - insert_P_THERMOTUBEMACHINING.js
        - QueryData.js
        - QueryMyReceive.js
        - QueryQualityTypeByCode.js
        - QueryTableData.js
        - QueryTableDataCount.js
        - QueryTableDataPage.js
        - QueryTreeIdsByTreeId.js
        - test.js
    - Thing.Fn.PushFiles
        - AddPushInfo.js
        - AddPushLists.js
        - ClosePush.js
        - QueryPushInfo.js
        - QueryPushListPage.js
        - UpdatePushInfo.js
    - Thing.Fn.QualityConfirm
        - AddATable.js
        - AddBTable.js
        - AddModel.js
        - AddPhoto.js
        - AddPhotos.js
        - AddProject.js
        - AddSign.js
        - CopyNode.js
        - DeleteNode.js
        - GetNewPhotoNum.js
        - GetSortByPid.js
        - GetTreeNewId.js
        - ImportModelData.js
        - ImportPhotoData.js
        - ImportSignData.js
        - QueryChildrenByPid.js
        - QueryNodeById.js
        - QueryPhotoById.js
        - QuerySignById.js
        - QueryTree.js
        - QueryTreeById.js
        - QueryTreeNodeByPid.js
        - QueryTreeRoot.js
        - SaveTableData.js
        - StrToClobSql.js
        - TableData2Html.js
        - UpdateModel.js
        - UpdateNode.js
        - UpdateNodeSort.js
        - UpdateSignHtml.js
        - UpdateTableHeader.js
        - UpdateTableStatus.js
    - Thing.Fn.QualityOnlineConfirm
        - QueryQualityCaseList.js
    - Thing.Fn.QualityReport
        - AddOnePhoto.js
        - AddOneSign.js
        - AddPhoto.js
        - AddPhotos.js
        - AddReport.js
        - AddSign.js
        - AddTableNode.js
        - AsyncQueryTree.js
        - BatchMarkNodes.js
        - CheckOptimisticLock.js
        - CloseEdit.js
        - CopyNode.js
        - DealAddTableNum.js
        - DealHistoryBase64.js
        - DealHistorySignHtml.js
        - DealTableHeader.js
        - DeleteNode.js
        - DeletePhoto.js
        - DirIsProcessNode.js
        - DragNode.js
        - excelToRowCol.js
        - GetCertificateMap.js
        - GetDownloadTableSql.js
        - GetNewPhotoNum.js
        - GetSortByPid.js
        - GetTreeNewId.js
        - ImportData.js
        - ImportLogData.js
        - ImportPdf.js
        - ImportPhotoData.js
        - ImportSignData.js
        - ImportTable.js
        - InitReport.js
        - IsHasReport.js
        - MoveNode.js
        - OpenEdit.js
        - QueryAllPId.js
        - QueryAllPNode.js
        - QueryAutoTable.js
        - QueryChildrenByPid.js
        - QueryDataType.js
        - QueryDownloadSearch.js
        - QueryDownloadTable.js
        - QueryFileList.js
        - QueryLogById.js
        - QueryNodeById.js
        - QueryPhotoById.js
        - QueryProcessNodesById.js
        - QuerySignById.js
        - QuerySignByPid.js
        - QueryTestTable.js
        - QueryTreeById.js
        - QueryTreeRoot.js
        - QueryUnlockedTables.js
        - QueryUnsignedTables.js
        - ReqGenerateFile.js
        - SaveConfig.js
        - SaveTableData.js
        - StrToClobSql.js
        - SyncBTableData.js
        - SyncCreoData.js
        - TableData2Html.js
        - test.js
        - UpdateLockRow.js
        - UpdateNode.js
        - UpdateSign.js
        - UpdateSignHtml.js
        - UpdateTableData.js
        - UpdateTableHeader.js
        - UpdateTableNode.js
        - UpdateTableStatus.js
    - Thing.Fn.QualityReportTpl
        - AddReport.js
        - AddTableNode.js
        - CloseEdit.js
        - CopyNode.js
        - DealAddTableNum.js
        - DealHistoryTpl.js
        - DeleteNode.js
        - DragNode.js
        - GetSortByPid.js
        - GetTreeNewId.js
        - ImportTable.js
        - OpenEdit.js
        - QueryAllPId.js
        - QueryChildrenByPid.js
        - QueryDataType.js
        - QueryNodeById.js
        - QueryNodeByPid.js
        - QueryProcessNodesById.js
        - QueryTplTree.js
        - QueryTreeRoot.js
        - SaveConfig.js
        - SaveTableData.js
        - StrToClobSql.js
        - TableData2Html.js
        - UpdateNode.js
        - UpdateTableHeader.js
        - UpdateTableNode.js
    - Thing.Fn.QualitySearch
        - getDataSearchConditionSql.js
        - GetProductTypeByTreeId.js
        - GetTableDataSql.js
        - QueryQualityDataCount.js
        - QuerySecondTableType.js
        - QueryTableData.js
        - QueryTableDataCount.js
        - QueryTableDataPage.js
    - Thing.Fn.QualityTestDataQuery
        - ApplyQualityTestResult.js
        - CalculateQualityTestResult.js
        - DeleteAllData.js
        - GetClaculateInputValues.js
        - QueryCalculationData.js
        - QueryCalibrationParameters.js
        - QueryMassTestData.js
        - QueryMomentCalibrationData.js
        - QueryMomentTestData.js
        - SyncData.js
    - Thing.Fn.RG
        - CreateTestFile.js
        - QueryRgCode.js
        - SyncRgPhoto.js
    - Thing.Fn.SecondTable
        - AddParam.js
        - AddQualityPhoto.js
        - AddQualityPhotos.js
        - AddQualityPlan.js
        - AddQualityReportTpl.js
        - AddQualitySign.js
        - AddTable.js
        - AddTableData.js
        - AddTestParam.js
        - BatchSyncAllTables.js
        - CancelDataConfirm.js
        - changeData.js
        - changeData_OSR.js
        - changeData_热控喷涂.js
        - changeData_热管.js
        - ClearEmptyRow.js
        - ClearTable.js
        - CompareValue.js
        - ConfirmTableData.js
        - ConvertTableHtml.js
        - CopyTable.js
        - DealProductId.js
        - DeleteDownloadFile.js
        - DeleteParam.js
        - DeletePhoto.js
        - DeleteTable.js
        - DeleteTableData.js
        - GenerateFileComplete.js
        - GenerateQualityReportByTreeId.js
        - getChildNodeInTree.js
        - GetNewParamSeq.js
        - GetProductTypeByTreeId.js
        - GetQualityPhotoTypeByTreeId.js
        - GetSecondTableHeader.js
        - GetTableDataSql.js
        - isCapital.js
        - manualSyncMes.js
        - NumToChinese.js
        - ParseQualityPhotoPlan.js
        - ParseQualityPlan.js
        - QualityDataConfirm.js
        - QueryAllPhoto.js
        - QueryAllTable.js
        - QueryDownloadTable.js
        - QueryMesTable.js
        - QueryParams.js
        - QueryParamsById.js
        - QueryProcessQualityData.js
        - QueryProcessQualityPhotoData.js
        - QueryQualityPhotoSummary.js
        - QueryQualityReportTpl.js
        - QueryQualitySign.js
        - QueryQualitySummary.js
        - QueryStandAlone.js
        - QueryTable.js
        - QueryTableById.js
        - QueryTableByNodename.js
        - QueryTableComponent.js
        - QueryTableData.js
        - QueryTableDataCount.js
        - QueryTableDataPage.js
        - QueryTableSortCol.js
        - QueryTdPhoto.js
        - QueryTemplateTree.js
        - QueryTreeNodeByName.js
        - QueryTreesByTableId.js
        - QueryType4TableData.js
        - ReassociationData.js
        - RecordDownloadFile.js
        - RelationBomTree.js
        - RelationTable.js
        - ReqGenerateFile.js
        - reqTestControl.js
        - ReqTestTable.js
        - StrToClobSql.js
        - SyncMesData.js
        - SyncTestConfirmTable.js
        - test.js
        - test2.js
        - testclob.js
        - testclob2.js
        - testMes.js
        - TestXml.js
        - TestXml1.js
        - transbase10.js
        - UpdateHistoryData.js
        - UpdateHistoryTableConfig.js
        - UpdateHistoyrTableAndData.js
        - UpdateParam.js
        - UpdateQualityDataStatus.js
        - UpdateTable.js
        - UpdateTableTplFile.js
        - UploadTableImage.js
    - Thing.Fn.StateCheck
        - insertStateCheckRecord.js
        - queryStateCheckRecords.js
    - Thing.Fn.SystemDic
        - addDictionary.js
        - addDictionaryData.js
        - addDictionaryDataByName.js
        - deleteDictionary.js
        - deleteDictionaryData.js
        - editDictionary.js
        - editDictionaryData.js
        - getDataSearchType.js
        - GetDicDataByDicType.js
        - getDictionary.js
        - getDictionaryData.js
        - getDictionaryDataByName.js
        - getExcelImportData.js
        - getFileTypeByName.js
        - getFileUploadPath.js
        - getKeyByNames.js
        - GetSecDataBySecLevel.js
        - InitWorkHoursDic.js
    - Thing.Fn.SystemManagement
        - AddFunc.js
        - AddMenu.js
        - addRole.js
        - addUser.js
        - AddUserGroup.js
        - AddUserModel.js
        - AssignFuncToRole.js
        - AssignMenuToRole.js
        - BatchImportUser.js
        - DealSql.js
        - DeleteAssignModel.js
        - DeleteAssignRole.js
        - DeleteFuncByFuncID.js
        - DeleteMenu.js
        - DeleteRole.js
        - DeleteUser.js
        - getAllMenus.js
        - getAllRole.js
        - getAllToAssignRoles.js
        - getAllUser.js
        - getMenuIdByPath.js
        - getUserByFullName.js
        - GetUserQuerySql.js
        - QueryAllAssignMenuFuncs.js
        - QueryAllUser.js
        - QueryAssignMenu.js
        - QueryDefaultMenu.js
        - QueryFuncsByMenuId.js
        - QueryModelsByUserId.js
        - QueryRoleByRoleName.js
        - QueryRolesByUserId.js
        - QueryUserCount.js
        - QueryUserPage.js
        - SearchRoleByRoleName.js
        - SortMenu.js
        - UpdateFunc.js
        - UpdateMenu.js
        - UpdateRole.js
        - UpdateUser.js
        - UploadUserSign.js
    - Thing.Fn.TableSearch
        - GetQueryParams.js
        - GetTableSearchType.js
    - Thing.Fn.TestEvaluation
        - AddCategory.js
        - AddFiles.js
        - AddTreeNode.js
        - Confirm.js
        - CopyTreeNode.js
        - Delete.js
        - DeleteTreeNode.js
        - GetCategories.js
        - GetCategoryPId.js
        - GetDeploymentTypeStatistics.js
        - GetFilesByCategory.js
        - GetFileStatistics.js
        - GetManufacturerUnits.js
        - GetModelProgressStatistics.js
        - GetModels.js
        - GetProductList.js
        - GetProductListV2.js
        - GetProductsByLevel.js
        - GetProductStatistics.js
        - GetProductStatus.js
        - GetQueryTestFileSql.js
        - GetStatusStatistics.js
        - GetSystemStatistics.js
        - InitCategory.js
        - ModifyFileCreateTime.js
        - QueryAllTestFile.js
        - QueryCategory.js
        - QueryFolders.js
        - QueryTestFile.js
        - QueryTreeById.js
        - QueryTreeRoot.js
        - QueryTreeTestFile.js
        - SimulateProductData.js
        - UpdateNodeParent.js
        - UpdateNodeStatus.js
        - UpdateTreeNode.js
        - UpdateTreeNodeSort.js
    - Thing.Fn.Workbench
        - BacthRelationProduct.js
        - BatchRelationProcess.js
        - GetSecondTableViewSql.js
        - QueryDateCount.js
        - QueryModel.js
        - QueryModelCount.js
        - QueryMyPkgTable.js
        - QueryMyTable.js
        - QueryPdmTable.js
        - QueryPhase.js
        - QueryQualityCount.js
        - QueryQualityCountCopy.js
        - QueryTypeCount.js
        - RelationProcess.js
        - RelationProduct.js
        - test.js
    - Thing.Integration.DataCollect
        - addDataToResultTable.js
        - AddPdmDoc.js
        - AddPdmEcn.js
        - AsyncPDM.js
        - automaticCollect.js
        - autoSync.js
        - DeleteTempPhoto.js
        - downloadFileTest.js
        - downloadPhoto.js
        - getMaterieResultId.js
        - getModelByTreeid.js
        - getTagFileName.js
        - insertAddendum.js
        - insertAttachment.js
        - insertStructuralAssembly.js
        - insertXmlData.js
        - insertXmlData_Photo.js
        - isStructuralAssembly.js
        - manualSync.js
        - msecToTime.js
        - parseStructuralAssemblyFile.js
        - reqTestControl.js
        - resolveXmlData_CheckCard.js
        - resolveXmlData_MaterialDelivery.js
        - resolveXmlData_Photo.js
        - resolveXmlData_ProductOutIn.js
        - resolveXmlData_ProductSubmit.js
        - resolveXmlData_ScrapNotice.js
        - resolveXmlData_TechCard.js
        - resolveXmlData_TechPorblem.js
        - resolveXmlData_UndeliveredProduct.js
        - test.js
        - test2.js
        - test3.js
        - testDataCapture.js
        - testQuerySeqService.js
        - updateDataToResultTable.js
        - UpdateResultStatus.js
    - Thing.Integration.LogUtil
        - createLog.js
        - GetConditionSql.js
        - LogIntegrationRecord.js
        - QueryAllLogDirect.js
        - QueryPageDataLogs.js
        - QueryTotalNumbers.js
    - Thing.Statictics.Util
        - AddDataToConnectorOnOff.js
        - AddDataToConnectorOnOffTimes.js
        - AddDataToHeater.js
        - AddDataToHeatResist.js
        - AddDataToLayersOnOff.js
        - AddDataToStandAlong.js
        - AddResultToQuanlityaControl.js
        - AutoSync.js
        - AutoSyncCollect.js
        - DealConnectorConnectorOnOffTimesXML.js
        - DealConnectorOnOffXML.js
        - DealHeaterXML.js
        - DealHeatResistXML.js
        - DealLayersOnOffXML.js
        - DealStandAlongXML.js
        - GetAllWaitToSyncCollect.js
        - GetConditionSql.js
        - GetStandAlongConditionSql.js
        - insertTJFXXmlData.js
        - QueryData.js
        - QueryPageData.js
        - QueryTotalNumbers.js
        - test1.js
        - testAuto.js
        - TestDataInsert.js
        - testList.js
        - testQList.js
    - Thing.TemplateUtil
        - AddTemplateData.js
        - DeleteTemplateData.js
        - GetTemplateDataPage.js
        - InitDPDataList.js
        - queryTotalNumbers.js
        - UpdateTemplateData.js
    - Thing.Timer.DeleteTempPhoto
        - DeleteTempPhoto.js
    - Thing.Timer.DeleteTemps
        - CleanupExpiredData.js
        - DeleteTemps.js
    - Thing.UserLogin
        - ArrayDistinct.js
        - caLogin.js
        - DataPkgFeedback.js
        - GetFileHandleUrl.js
        - GetNewFileHandleUrl.js
        - GetPreviewUrl.js
        - GetRoleSecurity.js
        - login.js
        - setUserPwd.js
    - Thing.UserLogUtil
        - GetLogCondition.js
        - logRecord.js
        - queryAllLogs.js
        - QueryAllOperations.js
        - QueryData.js
        - queryLogCount.js
        - QueryPageData.js
        - queryTotalNumbers.js
    - Thing.Util.HandsonTable
        - AddLog.js
        - AddOnePhoto.js
        - AddOneSign.js
        - AddPhoto.js
        - AddPhotos.js
        - AddSign.js
        - AddTableNode.js
        - Base64ToFile.js
        - CheckOptimisticLock.js
        - ClearDataSign.js
        - ClearUserEdit.js
        - CloseEdit.js
        - DealEditPhoto.js
        - DealHistoryBase64.js
        - DealHistorySignHtml.js
        - DeleteConfirmFile.js
        - DeleteNode.js
        - DeletePhoto.js
        - GenerateFileComplete.js
        - GetLogCondition.js
        - OpenEdit.js
        - QueryLog.js
        - QueryLogCount.js
        - QueryLogModule.js
        - QueryLogOperation.js
        - QueryLogPage.js
        - QueryLogTotal.js
        - QuerySignByPid.js
        - QueryTreeNodeByPid.js
        - QueryTreeRoot.js
        - RecordDownloadFile.js
        - ReplaceSMText.js
        - ReqGenerateFile.js
        - SaveTableData.js
        - SelectWorkType.js
        - StrToClobSql.js
        - TableData2Html.js
        - UnifiedDealEditPhoto.js
        - UpdateLockRow.js
        - UpdateSign.js
        - UpdateSignHtml.js
        - UpdateTableData.js
        - UpdateTableHeader.js
        - UpdateTableNode.js
        - UpdateTableStatus.js
    - Thing.whq
        - service1.js
        - service2.js
        - service4.js
    - Thing.XmlContent
        - getXmlDataByTypeAndID.js
    - TT_Thing.Tpl.OnlineConfirm
        - AddATable.js
        - AddBTable.js
        - AddModel.js
        - AddOnePhoto.js
        - AddOneSign.js
        - AddPhoto.js
        - AddPhotos.js
        - AddProject.js
        - AddSign.js
        - AddTableNode.js
        - BatchDealHtmlData.js
        - CheckOptimisticLock.js
        - CloseEdit.js
        - CopyModel.js
        - CopyNode.js
        - DealHistoryBase64.js
        - DealHistorySignHtml.js
        - DealTableHeader.js
        - DeleteNode.js
        - DeletePhoto.js
        - GetDownloadTableSql.js
        - GetNewPhotoNum.js
        - GetSortByPid.js
        - GetTreeNewId.js
        - ImportLogData.js
        - ImportModelData.js
        - ImportPdf.js
        - ImportPhotoData.js
        - ImportSignData.js
        - ImportTable.js
        - MoveNode.js
        - OpenEdit.js
        - QueryAllPId.js
        - QueryChildrenByPid.js
        - QueryDownloadSearch.js
        - QueryDownloadTable.js
        - QueryFolder.js
        - QueryLogById.js
        - QueryModelNodesById.js
        - QueryNodeById.js
        - QueryPhotoById.js
        - QuerySignById.js
        - QuerySignByPid.js
        - QueryTree.js
        - QueryTreeById.js
        - QueryTreeNodeByPid.js
        - QueryTreeRoot.js
        - RecordDownloadFile.js
        - ReqGenerateFile.js
        - SaveTableData.js
        - SelectFolder.js
        - StrToClobSql.js
        - UpdateLockRow.js
        - UpdateModel.js
        - UpdateNode.js
        - UpdateNodeSort.js
        - UpdateSign.js
        - UpdateSignHtml.js
        - UpdateTableData.js
        - UpdateTableHeader.js
        - UpdateTableNode.js
        - UpdateTableStatus.js
    - WSThing
        - insertXmlContent.js
        - testGetXmlService.js
        - testService.js
    - _xmls
        - Things_AuditArchiveCleanupNotificationScheduler.xml
        - Things_AuditArchiveCleanupScheduler.xml
        - Things_AuditArchiveScheduler.xml
        - Things_DataTable_PushInfo.xml
        - Things_DataTable_PushList.xml
        - Things_ExcelParseThing.xml
        - Things_ExtApi.xml
        - Things_HDFS_Thing.xml
        - Things_KEPServerEX01.xml
        - Things_publishMissionThing.xml
        - Things_SecurityMonitor.xml
        - Things_SyncCaptureDataTimer.xml
        - Things_test1.xml
        - Things_TestAddThing.xml
        - Things_test_vs.xml
        - Things_Thing_CAUserLogin.xml
        - Things_Thing_DB_Oracle.xml
        - Things_Thing_DB_Oracle1.xml
        - Things_Thing_DB_Oracle_Device.xml
        - Things_Thing_DP_BasicInit.xml
        - Things_Thing_Fn_AitScreen.xml
        - Things_Thing_Fn_BigScreen.xml
        - Things_Thing_Fn_BOM.xml
        - Things_Thing_Fn_BPM.xml
        - Things_Thing_Fn_Certificate.xml
        - Things_Thing_Fn_Costing.xml
        - Things_Thing_Fn_DataCollect.xml
        - Things_Thing_Fn_DataDownload.xml
        - Things_Thing_Fn_DataPackage.xml
        - Things_Thing_Fn_DataSearch.xml
        - Things_Thing_Fn_Dl.xml
        - Things_Thing_Fn_ElectricTest.xml
        - Things_Thing_Fn_ExcelImport.xml
        - Things_Thing_Fn_IndexAnalysis.xml
        - Things_Thing_Fn_Interface.xml
        - Things_Thing_Fn_KeyAttrAnalyze.xml
        - Things_Thing_Fn_LaunchConfirm.xml
        - Things_Thing_Fn_LaunchOnlineConfirm.xml
        - Things_Thing_Fn_ListData.xml
        - Things_Thing_Fn_PhotoQuery.xml
        - Things_Thing_Fn_PlanBuild.xml
        - Things_Thing_Fn_ProcessTree.xml
        - Things_Thing_Fn_ProductDataQuery.xml
        - Things_Thing_Fn_ProductQuality.xml
        - Things_Thing_Fn_PushFiles.xml
        - Things_Thing_Fn_QualityConfirm.xml
        - Things_Thing_Fn_QualityOnlineConfirm.xml
        - Things_Thing_Fn_QualityReport.xml
        - Things_Thing_Fn_QualityReportTpl.xml
        - Things_Thing_Fn_QualitySearch.xml
        - Things_Thing_Fn_QualityTestDataQuery.xml
        - Things_Thing_Fn_RG.xml
        - Things_Thing_Fn_SecondTable.xml
        - Things_Thing_Fn_StateCheck.xml
        - Things_Thing_Fn_SystemDic.xml
        - Things_Thing_Fn_SystemManagement.xml
        - Things_Thing_Fn_TableSearch.xml
        - Things_Thing_Fn_TestEvaluation.xml
        - Things_Thing_Fn_Workbench.xml
        - Things_Thing_Integration_DataCollect.xml
        - Things_Thing_Integration_LogUtil.xml
        - Things_Thing_SchedulerForTest.xml
        - Things_Thing_Statictics_Util.xml
        - Things_Thing_TemplateUtil.xml
        - Things_thing_timer.xml
        - Things_Thing_TimerScheduler.xml
        - Things_Thing_TimerScheduler_ManualSyncQueue.xml
        - Things_Thing_Timer_AsyncPdm.xml
        - Things_Thing_Timer_BigScreen.xml
        - Things_Thing_Timer_DeleteTempPhoto.xml
        - Things_Thing_Timer_DeleteTemps.xml
        - Things_Thing_Timer_ElectricTest.xml
        - Things_Thing_Timer_PhotoCount.xml
        - Things_Thing_Timer_SyncBomTree.xml
        - Things_Thing_Timer_SyncBpmData.xml
        - Things_Thing_Timer_SyncListData.xml
        - Things_Thing_Timer_SyncMesTable.xml
        - Things_Thing_Timer_SyncTask.xml
        - Things_Thing_Timer_SyncTest.xml
        - Things_Thing_UserLogin.xml
        - Things_Thing_UserLogUtil.xml
        - Things_Thing_Util_HandsonTable.xml
        - Things_thing_vs.xml
        - Things_Thing_whq.xml
        - Things_Thing_XmlContent.xml
        - Things_ValueStream_Test_ASC.xml
        - Things_WSFileRepo.xml
        - Things_WSThing.xml
        - ThingTemplates_CALoginThingTemplate.xml
        - ThingTemplates_ojdbc6JDBCTemplate.xml
        - ThingTemplates_Thing_Tpl_OnlineConfirm.xml
- WebSign_Setup.exe
- 备份脚本
    - 812所产品数据包管理系统_备份方案.docx
    - file
        - file-backup-exc.bat
        - file-backup.bat
        - file-backup20230509.log
        - task-add.bat
        - task-delete.bat
        - 使用说明.txt
    - oracle
        - oracle-backup.bat
        - task-add.bat
        - task-delete.bat
        - 使用说明.txt
    - Oracle常用sql.txt
    - ~$2所产品数据包管理系统_备份方案.docx
- 更新代码脚本
```
